package summary_meta_store

import (
	"context"
	"time"

	"code.byted.org/ies/codin/application/codesearch/entity/summary"
)

// SummaryMetaValue 元数据项的值结构
type SummaryMetaValue struct {
	MerkleId      string                `json:"merkle_id"`
	Status        summary.SummaryStatus `json:"status"`
	LastUpdatedAt time.Time             `json:"last_updated_at"`
}

func NewSummaryMetaValue(merkleId string, status summary.SummaryStatus) *SummaryMetaValue {
	return &SummaryMetaValue{
		MerkleId:      merkleId,
		Status:        status,
		LastUpdatedAt: time.Now(),
	}
}

// SummaryMetaStore 元数据存储接口
type SummaryMetaStore interface {
	Get(ctx context.Context, key string) (*SummaryMetaValue, error)
	Set(ctx context.Context, key string, value *SummaryMetaValue) error
}
