package summary_meta_store

import (
	"context"
	"time"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs"
	"code.byted.org/ies/codin/application/codesearch/entity/summary"
	"code.byted.org/kv/goredis"
)

const (
	tableName = "codesearch_summary_meta"
)

type SummaryMetaAbaseImpl struct {
	redisClient *goredis.Client
}

func NewSummaryMetaAbaseImpl() (SummaryMetaStore, error) {
	options := goredis.NewOption()
	options.SetPoolInitSize(20)
	options.ReadTimeout = 800 * time.Millisecond
	options.WriteTimeout = 800 * time.Millisecond

	redisClient, err := goredis.NewAbaseClientWithOption("bytedance.abase2.devops", tableName, options)
	if err != nil {
		logs.Error("SummaryMetaAbaseImpl.NewSummaryMetaAbaseImpl, NewAbaseClientWithOption error = %v", err)
		return nil, err
	}

	return &SummaryMetaAbaseImpl{redisClient: redisClient}, nil
}

func (i *SummaryMetaAbaseImpl) Set(ctx context.Context, key string, value *SummaryMetaValue) error {
	logs.CtxInfo(ctx, "SummaryMetaAbaseImpl.Set, key = %s, value = %v", key, jsonx.ToString(value))
	return i.redisClient.Set(key, jsonx.ToString(value), 0).Err()
}

func (i *SummaryMetaAbaseImpl) Get(ctx context.Context, key string) (*SummaryMetaValue, error) {
	logs.CtxInfo(ctx, "SummaryMetaAbaseImpl.Get, key = %s", key)

	cacheValue, err := i.redisClient.Get(key).Result()
	if err != nil {
		logs.CtxWarn(ctx, "SummaryMetaAbaseImpl.Get, redis get error =  %v", err)
		return NewSummaryMetaValue("", summary.SummaryStatusNone), nil // redis 客户端没有 value 的时候会是 err=redisNil，这里不需要
	}

	summaryMetaValue := new(SummaryMetaValue)
	if err = jsonx.UnmarshalFromString(cacheValue, &summaryMetaValue); err != nil {
		logs.CtxError(ctx, "SummaryMetaAbaseImpl.Get, UnmarshalFromString error = %v", err)
		return nil, err
	}
	return summaryMetaValue, nil
}
