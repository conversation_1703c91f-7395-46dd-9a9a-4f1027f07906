package repo

import (
	"context"

	businessConfig "code.byted.org/ies/codin/application/codesearch/entity/business"
	config "code.byted.org/ies/codin/application/codesearch/repo/repo/config"
	"code.byted.org/ies/codin/common/tcc"
)

/**
 * RepoManager 负责维护 repo_name 到仓库的一些信息
 */
type RepoManager struct {
	repoItemMap     map[string]businessConfig.BusinessRepoConfig
	currentRepoName string
}

/**
 * NewRepoManager 创建 RepoManager 实例，并初始化映射
 */
func NewRepoManager(ctx context.Context) *RepoManager {
	mgr := &RepoManager{
		repoItemMap: make(map[string]businessConfig.BusinessRepoConfig),
	}

	repoConfigList := []tcc.RepoConfigItem{}
	// if env.IsPPE() || env.IsProduct() {
	// 	repoConfigList, _ = tcc.GetTccReader().GetCodesearchRepoConfig(ctx)
	// } else {
	repoConfigList = config.RepoConfigList
	// }

	for _, item := range repoConfigList {
		mgr.repoItemMap[item.RepoName] = businessConfig.BusinessRepoConfig{
			RepoName: item.RepoName,
			RepoURL:  item.RepoURL,
			PathList: item.PathList,
		}
	}
	return mgr
}

/**
 * GetRepoURL 获取指定 business+platform 的 git 仓库地址
 * GetRepoURL returns the git repo URL for the given business and platform
 */
func (m *RepoManager) GetRepoURL(repoName string) (string, bool) {
	repoURL, ok := m.repoItemMap[repoName]
	if !ok {
		return "", false
	}
	return repoURL.RepoURL, true
}

/**
 * GetBusinessRepoConfig 获取指定 business+platform 的 BusinessRepoConfig
 * GetBusinessRepoConfig returns the BusinessRepoConfig for the given business and platform
 */
func (m *RepoManager) GetRepoItem(repoName string) (*businessConfig.BusinessRepoConfig, bool) {
	repoConfig, ok := m.repoItemMap[repoName]
	if !ok {
		return &businessConfig.BusinessRepoConfig{}, false
	}
	return &repoConfig, true
}

func (m *RepoManager) GetCurrentRepoName() string {
	return m.currentRepoName
}

func (m *RepoManager) SetCurrentRepoName(repoName string) {
	m.currentRepoName = repoName
}

func (m *RepoManager) UpdateRepoItem(repoName string, repoConfig businessConfig.BusinessRepoConfig) {
	m.repoItemMap[repoName] = repoConfig
}
