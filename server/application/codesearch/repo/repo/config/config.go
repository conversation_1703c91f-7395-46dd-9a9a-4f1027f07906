package config

import "code.byted.org/ies/codin/common/tcc"

var RepoConfigList = []tcc.RepoConfigItem{
	{
		// 即梦web仓库
		RepoURL:  "https://code.byted.org/ies/lvweb.git",
		PathList: []string{"apps/dreamina", "apps/story-agent"},
		RepoName: "ies/lvweb",
	},
	{
		// 即梦web仓库
		RepoURL:  "https://code.byted.org/ies/code-index",
		PathList: []string{"apps/dreamina", "apps/story-agent"},
		RepoName: "ies/code-index",
	},
	{
		// kotlin demo 仓库
		RepoURL:  "https://code.byted.org/ies/codin-index-kotlin-demo",
		PathList: []string{""},
		RepoName: "ies/codin-index-kotlin-demo",
	},
	{
		// swift demo 仓库
		RepoURL:  "https://code.byted.org/ies/codin-index-swift-demo",
		PathList: []string{""},
		RepoName: "ies/codin-index-swift-demo",
	},
	{
		RepoURL: "https://code.byted.org/ies/lv-lynx",
		PathList: []string{
			"apps/dreamina-h5",
			"apps/dreamina-lynx",
			"packages/dreamina-services",
			"packages/dreamina-lynx-rs",
			"packages/ai-dreamina",
		},
		RepoName: "ies/lv-lynx",
	},
	{
		// 即梦 server mweb-api 服务仓库
		RepoURL:  "https://code.byted.org/videocut-aigc/mweb-api.git",
		PathList: []string{""},
		RepoName: "videocut-aigc:mweb-api",
	},
	{
		// 即梦 server agent 服务仓库
		RepoURL:  "https://code.byted.org/videocut-aigc/dreamina_agent_core.git",
		PathList: []string{""},
		RepoName: "videocut-aigc:dreamina_agent_core_server",
	},
	{
		// 即梦 server 生成服务仓库
		RepoURL:  "https://code.byted.org/videocut-aigc/content_generate.git",
		PathList: []string{""},
		RepoName: "videocut-aigc:content_generate_server",
	},
	{
		// 即梦 server aigcUtils
		RepoURL:  "https://code.byted.org/videocut-aigc/aigc-utils.git",
		PathList: []string{""},
		RepoName: "videocut-aigc:aigc-utils_server",
	},
	{
		// 即梦安卓仓库
		RepoURL:  "https://code.byted.org/faceu-android/vega",
		PathList: []string{"apps/dreamina", "modules/dreamina", "modules/dreaminabase"},
		RepoName: "faceu-android/vega",
	},
	{
		// android demo 工程仓库
		RepoURL:  "https://code.byted.org/qingyingliu.671/codin-android-demo.git",
		PathList: []string{""},
		RepoName: "qingyingliu.671:codin-android-demo",
	},
	{
		// 即梦ios仓库
		RepoURL:  "https://code.byted.org/faceu-ios/iMovie",
		PathList: []string{"Modules/JMMain", "Modules/JMStory", "Modules/JMPaymentCenter", "Modules/JMBase"},
		RepoName: "dreamina_ios",
	},
	// {
	// 	// pippit lynx仓库
	// 	RepoURL:     "https://code.byted.org/ies/lv-lynx",
	// 	PathList: []string{"apps/pippit-lynx", "packages/pippit"},
	// 	RepoName: "pippit_lynx",
	// },
	{
		// douyin web仓库
		RepoURL:  "https://bits.bytedance.net/code/ies/douyin_web",
		PathList: []string{""},
		RepoName: "douyin_web",
	},
	{
		RepoURL:  "https://code.byted.org/capcut-server/capcut_web_common_handler.git",
		PathList: []string{""},
		RepoName: "capcut_web_common_handler",
	},
	{
		RepoURL:  "https://code.byted.org/capcut-business/business_api.git",
		PathList: []string{""},
		RepoName: "capcut-business-business_api",
	},
}
