package config

import (
	"flag"

	"code.byted.org/gopkg/logs/v2/log"
)

// Config 是业务配置数据结构。
// 这里需要用户自己填充业务配置字段。
type Config struct {
	ConfigPath string
}

func New() *Config {
	var configPath string
	flag.StringVar(&configPath, "configs", "", "指定配置文件目录路径")
	flag.Parse()
	if configPath == "" {
		log.V2.Error().Str("未指定配置文件目录路径").Emit()
		panic("未指定配置文件目录路径")
	}

	return &Config{
		ConfigPath: configPath,
	}
}
