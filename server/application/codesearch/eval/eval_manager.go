package eval

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/logs"
	codeEntity "code.byted.org/ies/codin/application/codesearch/entity/code"
	"code.byted.org/ies/codin/application/codesearch/logic"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/flow/devops/evaluation/callback_common"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/flow/devops/evaluation/evaluator_callback"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/flow/devops/evaluation/object_callback"
)

var EvalManager = &evalManager{}

type evalManager struct{}

// handleSemanticSearch 处理代码总结场景
func (e *evalManager) HandleSemanticSearch(ctx context.Context, request *codesearch.AgentExecuteRequest) (*codesearch.AgentExecuteResponse, error) {
	var app = logic.NewCodeSearch(ctx)

	userKnowledgeId, err := CheckReqInputVariables(request.Input.Variables, "user_knowledge_id")
	if err != nil {
		return nil, err
	}

	query, err := CheckReqInputVariables(request.Input.Variables, "query")
	if err != nil {
		return nil, err
	}

	logs.CtxInfo(ctx, "[AgentExecute][SemanticSearch] userKnowledgeId=%s, query=%v", userKnowledgeId, query)

	codeInfos, err := app.SemanticManager.SearchCodeEntity(ctx, &codeEntity.GetCodeRequest{
		UserKnowledgeId: userKnowledgeId,
		Query:           query,
	})
	if err != nil {
		logs.CtxError(ctx, "[AgentExecute][SemanticSearch] SearchCode error: %+v", err)
		return nil, err
	}

	codeStr := ""
	codeRefsSet := make(map[string]bool)
	codeRefs := make([]string, 0)

	for _, codeInfo := range codeInfos {
		codeStr += fmt.Sprintf("entityName: %s\n", codeInfo.EntityName)
		codeStr += fmt.Sprintf("entityDesc: %s\n", codeInfo.EntityDesc)
		codeStr += fmt.Sprintf("chunkIds: %v\n", codeInfo.ChunkIDs)
		codeStr += fmt.Sprintf("filePaths: %v\n", codeInfo.FilePath)
		codeStr += "\n------------------------------------\n"

		for _, filePath := range codeInfo.FilePath {
			if !codeRefsSet[filePath] {
				codeRefsSet[filePath] = true
				codeRefs = append(codeRefs, filePath)
			}
		}
	}

	codeRefsBytes, err := json.Marshal(codeRefs)
	if err != nil {
		logs.CtxError(ctx, "[AgentExecute][SemanticSearch] json.Marshal error: %+v", err)
		return nil, err
	}
	codeRefsStr := string(codeRefsBytes)

	return &codesearch.AgentExecuteResponse{
		Trajectory: &object_callback.Trajectory{
			Actions: []*evaluator_callback.AgentAction{
				{
					Type:       evaluator_callback.ActionType_ToolCall,
					ActionMeta: "semantic_search",
					Input: &evaluator_callback.Content{
						DataType: conv.Ptr(callback_common.DataType_PlainText),
						Text:     &query,
					},
					Output: &evaluator_callback.Content{
						DataType: conv.Ptr(callback_common.DataType_JSONString),
						JSONInfo: &callback_common.JSONInfo{
							Content: &codeRefsStr,
						},
					},
				},
			},
		},
		Output: &object_callback.Output{
			PredictionV2: &object_callback.Content{
				DataType: conv.Ptr(callback_common.DataType_PlainText),
				Text:     &codeStr,
			},
		},
	}, nil
}

// handleCodeSearchScene 处理代码搜索场景（原有逻辑）
func (e *evalManager) HandleCodeSearchScene(ctx context.Context,
	request *codesearch.AgentExecuteRequest,
	getCode func(ctx context.Context, req *codesearch.CodeSearchRequest) (*codesearch.CodeSearchResponse, error),
) (*codesearch.AgentExecuteResponse, error) {

	repoName, err := CheckReqInputVariables(request.Input.Variables, "repo_name")
	if err != nil {
		return nil, err
	}

	branch, err := CheckReqInputVariables(request.Input.Variables, "branch")
	if err != nil {
		return nil, err
	}

	pathList, err := CheckReqInputVariables(request.Input.Variables, "path_list")
	if err != nil {
		return nil, err
	}

	var pathListArray []string
	if err := json.Unmarshal([]byte(pathList), &pathListArray); err != nil {
		err = fmt.Errorf("[AgentExecute]: path_list is not a valid JSON string array: %v", err)
		logs.CtxError(ctx, err.Error())
		return nil, err
	}

	mode, err := CheckReqInputVariables(request.Input.Variables, "mode")
	if err != nil {
		return nil, err
	}
	if mode == "" {
		mode = "normal"
	}

	queryList, err := CheckReqInputVariables(request.Input.Variables, "query_list")
	if err != nil {
		return nil, err
	}

	if request.Input.Input == "" {
		err := fmt.Errorf("[AgentExecute]: query (request.Input.Input) is empty")
		logs.CtxError(ctx, err.Error())
		return nil, err
	}

	var queries []string
	if strings.HasPrefix(queryList, "[") && strings.HasSuffix(queryList, "]") && json.Unmarshal([]byte(queryList), &queries) == nil {
		logs.CtxInfo(ctx, "[AgentExecute] input query is a list: %s", queryList)
	}

	resp, err := getCode(ctx, &codesearch.CodeSearchRequest{
		Uid:       "fornax",
		Did:       "fornax",
		RepoName:  repoName,
		Branch:    branch,
		RepoPath:  fmt.Sprintf("/usr/fornax/%s", repoName),
		PathList:  pathListArray,
		Query:     request.Input.Input,
		QueryList: queries,
		Mode:      &mode,
	})
	if err != nil {
		logs.CtxError(ctx, "[AgentExecute] GetCode error: %+v", err)
		return nil, err
	}

	codeData := map[string]interface{}{
		"relate_code_locations": resp.Codes,
	}
	codeBytes, err := json.Marshal(codeData)
	if err != nil {
		logs.CtxError(ctx, "[AgentExecute] json.MarshalIndent error: %+v", err)
		return nil, err
	}

	codeStr := string(codeBytes)

	text := resp.Summary + "\n"
	ext := make(map[string]string)
	if resp.Codes != nil {
		for _, codeInfo := range resp.Codes {
			if codeInfo == nil {
				continue
			}

			ext[codeInfo.FilePath] = codeInfo.Summary

			text += "\n"
			text += "- " + codeInfo.FilePath
			text += ": "
			text += codeInfo.Summary
			text += "\n"
		}
	}

	logs.CtxInfo(ctx, "[AgentExecute][CodeSearch] summary: %s, code: %s", resp.Summary, codeStr)
	return &codesearch.AgentExecuteResponse{
		Trajectory: &object_callback.Trajectory{
			Actions: []*evaluator_callback.AgentAction{
				{
					Type:       evaluator_callback.ActionType_ToolCall,
					ActionMeta: "code_search",
					Input: &evaluator_callback.Content{
						DataType: conv.Ptr(callback_common.DataType_PlainText),
						Text:     &request.Input.Input,
					},
					Output: &evaluator_callback.Content{
						DataType: conv.Ptr(callback_common.DataType_JSONString),
						JSONInfo: &callback_common.JSONInfo{
							Content: &codeStr,
						},
					},
				},
			},
		},
		Output: &object_callback.Output{
			PredictionV2: &object_callback.Content{
				DataType: conv.Ptr(callback_common.DataType_MultiContent),
				MultiContentInfo: &callback_common.MultiContentInfo{
					Contents: []*callback_common.Content{
						{
							DataType: conv.Ptr(callback_common.DataType_PlainText),
							Text:     &text,
						},
					},
				},
			},
		},
	}, nil
}
