package eval

import (
	"fmt"

	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/flow/devops/evaluation/object_callback"
	"github.com/samber/lo"
)

func CheckReqInputVariables(fornaxInputVariables map[string]*object_callback.Content, variableKey string) (string, error) {
	variableValue, ok := fornaxInputVariables[variableKey]
	if !ok || variableValue == nil || variableValue.Text == nil {
		return "", fmt.Errorf("checkReqInputVariables, [%v] variable is missing or invalid in Input.Variables", variableKey)
	}
	return lo.FromPtrOr(variableValue.Text, ""), nil
}
