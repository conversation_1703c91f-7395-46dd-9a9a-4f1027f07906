package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codesearch/logic"
	summaryUpdate "code.byted.org/ies/codin/application/codesearch/logic/summary/update/summary_update"
	knowledge "code.byted.org/ies/codin/application/codesearch/repo/knowledge"
	knowledgeEntity "code.byted.org/ies/codin/application/codesearch/repo/knowledge/entity"
	"code.byted.org/ies/codinmodel/kitex_gen/agentserver"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

type SummaryTool struct {
	app      *logic.CodeSearch
	repoInfo *agentserver.RepoInfo
	uid      string
	did      string
}

func GetSummaryTool(app *logic.CodeSearch, repoInfo *agentserver.RepoInfo, uid string, did string) tool.InvokableTool {
	return &SummaryTool{
		app:      app,
		repoInfo: repoInfo,
		uid:      uid,
		did:      did,
	}
}

func (t *SummaryTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "get_summary",
		Desc: `描述：使用此命令可以查看代码库的总结`,
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"reason": {
				Type:     "string",
				Desc:     "列出执行该命令的原因",
				Required: true,
			},
			"repo": {
				Type:     "string",
				Desc:     "需要查询的代码库名",
				Required: true,
			},
		}),
	}, nil
}

func (t *SummaryTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	log.V2.Info().With(ctx).Str("argumentsInJSON").
		Str(argumentsInJSON).
		Str("repo").
		Obj(*t.repoInfo).
		Str("uid").
		Str(t.uid).
		Str("did").
		Str(t.did).Emit()

	// 解析参数
	request := &codesearch.CodeSearchRequest{
		RepoName: *t.repoInfo.RepoName,
		RepoPath: *t.repoInfo.Path,
		Branch:   *t.repoInfo.Branch,
		Uid:      t.uid,
		Did:      t.did,
	}
	summary, err := GetSummary(ctx, t.app, request)
	if err != nil {
		return "获取summary失败" + err.Error(), nil
	}

	return summary, nil
}

func GetSummary(ctx context.Context, app *logic.CodeSearch, req *codesearch.CodeSearchRequest) (string, error) {
	summaryStr := strings.Builder{}
	summaryStr.WriteString("<project_summary> \n")
	if env.IsPPE() || env.IsProduct() {
		summary, err := getSummaryProd(ctx, app, req)
		summaryStr.WriteString(summary)
		if err != nil {
			log.V2.Error().With(ctx).Str("---------------get_summary_failed").Error(err).Emit()
		}
	} else {
		// 在非生产环境下使用本地mockdata
		summary, err := getSummaryStrLocal(ctx, app, req)
		summaryStr.WriteString(summary)
		if err != nil {
			log.V2.Error().With(ctx).Str("---------------get_summary_local_failed").Error(err).Emit()
		}
	}
	summaryStr.WriteString("</project_summary> \n")
	return summaryStr.String(), nil
}

// 从生产的tos里面拉到summary
func getSummaryProd(ctx context.Context, app *logic.CodeSearch, req *codesearch.CodeSearchRequest) (string, error) {
	userKnowledgeId := summaryUpdate.GetSummaryUserKnowledgeId(&summaryUpdate.GetSummaryDataRequest{
		Uid:      req.Uid,
		RepoPath: req.RepoPath,
		Did:      req.Did,
		RepoName: req.RepoName,
		Branch:   req.Branch,
	})
	summaryData, err := summaryUpdate.GetUserSummaryData(ctx, app, userKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------get_summaryData_failed").Error(err).Emit()
		return "", err
	}
	formattedTree := knowledge.FormatKnowledgeTree(&summaryData.Knowledge)
	summaryStr := strings.Builder{}
	summaryStr.WriteString(formattedTree)
	return summaryStr.String(), nil
}

/**
 * getSummaryStrLocal 从本地mockdata目录读取项目摘要信息
 */
func getSummaryStrLocal(ctx context.Context, app *logic.CodeSearch, req *codesearch.CodeSearchRequest) (string, error) {
	// 构建mockdata文件路径
	mockdataDir := "application/codesearch/logic/code/builder/mockdata"
	filePath := mockdataDir + "/" + req.RepoName
	fmt.Println("filePath: ", filePath)
	// 读取mockdata文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------read_mockdata_failed").Error(err).Str("file_path", filePath).Emit()
		return "", err
	}

	// 解析JSON数据
	knowledgeData := knowledgeEntity.Knowledge{}
	err = json.Unmarshal(data, &knowledgeData)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------unmarshal_failed").Error(err).Emit()
		return "", err
	}

	// 格式化知识库树
	formattedTree := knowledge.FormatKnowledgeTree(&knowledgeData)
	summaryStr := strings.Builder{}
	summaryStr.WriteString(formattedTree)
	return summaryStr.String(), nil
}
