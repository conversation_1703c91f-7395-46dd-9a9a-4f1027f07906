package agent

import (
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codesearch/logic"
	"code.byted.org/ies/codin/application/codesearch/logic/tools"
	"code.byted.org/ies/codin/common/agentsdk/einox"
	"code.byted.org/ies/codin/common/llm"
	clienttool "code.byted.org/ies/codin/common/tool"
	"code.byted.org/ies/codinmodel/kitex_gen/agentclient"
	"code.byted.org/ies/codinmodel/kitex_gen/agentserver"
	"code.byted.org/ies/codinmodel/kitex_gen/base"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/compose"
)

const CodesearchAgentId = "Codesearch"

type NewCodesearchAgentParams struct {
	Ctx         context.Context
	App         *logic.CodeSearch
	ModelType   base.ModelType
	ClientTools []*agentclient.ToolInfo
	Uid         string
	Did         string
	RepoInfo    *agentserver.RepoInfo
}

func NewCodesearchAgent(params *NewCodesearchAgentParams) (einox.Agent, error) {
	ctx := params.Ctx
	log.V2.Info().With(ctx).Str("start NewCodesearchAgent create").
		Str("model type", params.ModelType.String()).Emit()

	chatModel, err := llm.GetChatModel(ctx, params.ModelType)
	if err != nil {
		log.V2.Error().With(ctx).Str("create chat model fail").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().With(ctx).Str("create chat model successfully").Emit()

	// 创建基础工具（子agent作为tool）
	log.V2.Info().With(ctx).Str("start build tools").Emit()
	if params.RepoInfo == nil {
		log.V2.Error().With(ctx).Str("repo info is nil").Emit()
		return nil, err
	}
	tools := []tool.BaseTool{
		// 思考工具
		tools.GetThinkTool(params.App),
		tools.GetSummaryTool(params.App, params.RepoInfo, params.Uid, params.Did),
		tools.GetSemanticSearchTool(params.App, params.RepoInfo, params.Uid, params.Did),
	}
	log.V2.Info().With(ctx).
		Str("server tools").Int(len(tools)).
		Str("client tools").Int(len(params.ClientTools)).
		Emit()
	interruptBeforeToolCall := make([]string, 0, len(params.ClientTools))
	for _, tool := range params.ClientTools {
		tools = append(tools, clienttool.NewClientTool(tool))
		interruptBeforeToolCall = append(interruptBeforeToolCall, tool.Name)
	}
	log.V2.Info().With(ctx).Str("total tools").Int(len(tools)).Emit()
	log.V2.Info().With(ctx).Str("start create react agent").Emit()
	ragent, err := einox.NewReActAgent(ctx, &einox.ReActConfig{
		ID:    CodesearchAgentId,
		Model: chatModel,
		ToolsConfig: compose.ToolsNodeConfig{
			Tools:               tools,
			ExecuteSequentially: true,
		},
		MaxStep:                 200,
		InterruptBeforeToolCall: interruptBeforeToolCall,
		MaxTokens:               int32(float64(llm.GetInputTokenByModelType(params.ModelType)) * 0.7),
	})

	if err != nil {
		log.V2.Error().With(ctx).Str("创建agent失败").Error(err).Emit()
		return nil, err
	}

	log.V2.Info().With(ctx).Str("CodesearchAgent 创建成功").Emit()
	return ragent, nil
}
