package summary_update

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codesearch/entity/summary"
	"code.byted.org/ies/codin/application/codesearch/logic"
	merklet "code.byted.org/ies/codin/application/codesearch/logic/merklet"
	"code.byted.org/ies/codin/application/codesearch/logic/summary/entity"
	knowledgeUtils "code.byted.org/ies/codin/application/codesearch/repo/codebase/manager"
	metaStore "code.byted.org/ies/codin/application/codesearch/repo/summary_meta_store"
	"code.byted.org/ies/codin/application/codesearch/repo/tos"
	"code.byted.org/ies/codin/application/codesearch/repo/tos/config"
	"code.byted.org/ies/codin/common/group"
)

// TosManagerInterface 定义 TOS 管理器接口
type TosManagerInterface interface {
	InitTosClientIfNeeded(ctx context.Context, config config.TosConfig) error
	DownloadFileFromTos(ctx context.Context, objectKey string) ([]byte, error)
	UploadFileToTos(ctx context.Context, fileData []byte, rootHash string) (string, error)
}

// DownloadResult 表示下载结果的结构体
type DownloadResult struct {
	Priority int    // 优先级：1=最高，2=中等，3=最低
	Data     []byte // 下载的数据
	Error    error  // 下载错误
}

// UploadResult 表示上传结果的结构体
type UploadResult struct {
	Priority int   // 优先级：1=最高，2=中等，3=最低
	Error    error // 上传错误
}

type GetSummaryDataRequest struct {
	Uid      string
	RepoPath string
	Did      string
	RepoName string
	Branch   string
}

const tosDir = "summary/"

func NewSummaryTosManager() *tos.TosManager {
	tosManager := tos.NewTosManager()
	tosManager.SetDir(tosDir)
	return tosManager
}

/**
 * GetKnowledge 并发下载三个 TOS 文件，然后转换成 SummaryData，按照优先级1，2，3去返回
 * 有1返回1，没1返回2，1，2都没有返回3
 * @param ctx - 上下文信息
 * @param app - 代码搜索应用实例
 * @param request - Merkle 差异请求
 * @returns *SummaryData - 返回的知识库，如果所有下载都失败则返回 nil
 * @returns error - 错误信息
 */
func GetSummaryData(ctx context.Context, app *logic.CodeSearch, request *GetSummaryDataRequest) (*entity.SummaryData, string, error) {
	// 构建三个不同优先级的文件路径
	userKnowledgeId := GetSummaryUserKnowledgeId(request)
	knowledgeBranchId := GetSummaryKnowledgeBranchId(request)
	knowledgebaseId := GetSummaryKnowledgebaseId(request)

	// 定义三个不同优先级的文件路径
	filePaths := map[int]string{
		1: userKnowledgeId,   // 优先级1：用户特定的知识库
		2: knowledgeBranchId, // 优先级2：知识库+分支
		3: knowledgebaseId,   // 优先级3：知识库级别
	}
	tosManager := NewSummaryTosManager()

	// 初始化 TOS 管理器
	if err := tosManager.InitTosClientIfNeeded(config.SummaryStorageConfig); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return nil, "", fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	// 并发下载三个文件
	results := downloadFilesConcurrently(ctx, tosManager, filePaths)

	// 按照优先级选择最佳结果并解析
	return selectBestResultAndParse(results, filePaths)
}

func GetUserSummaryData(ctx context.Context, app *logic.CodeSearch, userKnowledgeId string) (*entity.SummaryData, error) {
	tosManager := NewSummaryTosManager()

	// 初始化 TOS 管理器
	if err := tosManager.InitTosClientIfNeeded(config.SummaryStorageConfig); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return nil, fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	data, err := tosManager.DownloadFileFromTos(ctx, userKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("下载用户知识库失败").Error(err).Emit()
		return nil, fmt.Errorf("下载用户知识库失败: %w", err)
	}

	var summaryData entity.SummaryData
	if err := json.Unmarshal(data, &summaryData); err != nil {
		log.V2.Error().With(ctx).Str("解析用户知识库失败").Error(err).Emit()
		return nil, fmt.Errorf("解析用户知识库失败: %w", err)
	}

	return &summaryData, nil
}

// 下载merkleTree
func DownloadMerkleTree(ctx context.Context, app *logic.CodeSearch, merkleId string) (*merklet.TreeNode, error) {
	// 这里需要使用merkleStorage的桶，因为merkleTree的桶是用来存储merkleTree的，而summary的桶是用来存储summary的
	tosManager := tos.NewTosManager()
	// merkle树相关的内容在merkleStorage的桶中
	err := tosManager.InitTosClientIfNeeded(config.MerkleStorageConfig)
	if err != nil {
		logs.CtxError(ctx, "get tos client error: %v, keyName: %s", err, merkleId)
		return nil, err
	}
	logs.CtxInfo(ctx, "get tos client success, keyName: %s", merkleId)
	// 1. 下载merkleTree
	merkleTree, err := tosManager.DownloadFileFromTos(ctx, merkleId)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] DownloadMerkleTree error: %+v", err)
		return nil, err
	}

	// 2. 解压数据
	uncompressedData, err := merklet.GzipDecompress(merkleTree)
	if err != nil {
		logs.CtxError(ctx, "GzipUncompress error: %v", err)
		return nil, err
	}

	serverTree, err := merklet.DeserializeTree(uncompressedData)
	if err != nil {
		logs.CtxError(ctx, "DeserializeTree error: %v", err)
		return nil, err
	}
	return serverTree, nil
}

func UpdateMetaStatus(ctx context.Context, merkleId string, request *GetSummaryDataRequest, summaryStatus summary.SummaryStatus) error {
	// 初始化元存储
	summaryMetaStore, err := metaStore.NewImpl()
	if err != nil {
		logs.CtxError(ctx, "UpdateMetaStatus, new_summary_meta_store_failed, err = %v", err)
		return err
	}

	metaKeys := ListSummaryKey(request)

	for _, metaKey := range metaKeys {
		if setErr := summaryMetaStore.Set(ctx, metaKey, metaStore.NewSummaryMetaValue(merkleId, summaryStatus)); setErr != nil {
			log.V2.Error().With(ctx).Str("UpdateMetaStatus, set_summary_meta_failed").Error(setErr).Emit()
			return setErr
		}
	}
	return nil
}

func ListSummaryKey(request *GetSummaryDataRequest) map[int]string {
	// 每一份 summary 都有三个 key
	userKnowledgeId := GetSummaryUserKnowledgeId(request)
	knowledgeBranchId := GetSummaryKnowledgeBranchId(request)
	knowledgebaseId := GetSummaryKnowledgebaseId(request)

	// 定义三个不同优先级的文件路径
	filePaths := map[int]string{
		1: userKnowledgeId,   // 优先级1：用户特定的知识库
		2: knowledgeBranchId, // 优先级2：知识库+分支
		3: knowledgebaseId,   // 优先级3：知识库级别
	}

	return filePaths
}

/**
 * UploadSummaryData 并发上传三个不同优先级的摘要数据到 TOS
 * @param ctx - 上下文信息
 * @param summaryData - 要上传的摘要数据
 * @param request - 请求参数
 * @returns error - 错误信息
 */
func UploadSummaryData(ctx context.Context, summaryData *entity.SummaryData, request *GetSummaryDataRequest) error {
	// 定义三个不同优先级的文件路径
	filePaths := ListSummaryKey(request)
	tosManager := NewSummaryTosManager()

	// 初始化 TOS 客户端
	if err := tosManager.InitTosClientIfNeeded(config.SummaryStorageConfig); err != nil {
		log.V2.Error().With(ctx).Str("初始化TOS客户端失败").Error(err).Emit()
		return fmt.Errorf("初始化TOS客户端失败: %w", err)
	}

	// 序列化摘要数据
	summaryDataJson, err := json.MarshalIndent(summaryData, "", "  ")
	if err != nil {
		log.V2.Error().With(ctx).Str("序列化摘要数据失败").Error(err).Emit()
		return fmt.Errorf("序列化摘要数据失败: %w", err)
	}

	// 并发上传三个文件
	results := uploadFilesConcurrently(ctx, tosManager, summaryDataJson, filePaths)

	// 检查上传结果
	successCount := 0
	for priority, result := range results {
		if result.Error == nil {
			successCount++
			log.V2.Info().With(ctx).Str("成功上传摘要数据").KVs(
				"priority", priority,
				"filePath", filePaths[priority],
			).Emit()
		} else {
			log.V2.Error().With(ctx).Str("上传摘要数据失败").KVs(
				"priority", priority,
				"filePath", filePaths[priority],
			).Error(result.Error).Emit()
		}
	}

	// 如果所有上传都失败，返回错误
	if successCount == 0 {
		return fmt.Errorf("所有优先级的摘要数据上传都失败")
	}

	log.V2.Info().With(ctx).Str("摘要数据上传完成").KVs(
		"successCount", successCount,
		"totalCount", len(filePaths),
	).Emit()

	return nil
}

/**
 * getUserKnowledgeId 生成用户特定的知识库ID，将路径分隔符替换为连字符
 * @param request - 请求参数
 * @returns string - 处理后的用户知识库ID
 */
func GetSummaryUserKnowledgeId(request *GetSummaryDataRequest) string {
	// 将路径分隔符 / 替换为连字符 -
	userKnowledgeId := knowledgeUtils.GetUserKnowledgeId(request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	encodedId := strings.ReplaceAll(userKnowledgeId, "/", "-")
	return encodedId
}

func GetSummaryKnowledgeBranchId(request *GetSummaryDataRequest) string {
	knowledgeBranchId := knowledgeUtils.GetKnowledgeBranchId(request.RepoName, request.Branch)
	// 将路径分隔符 / 替换为连字符 -
	encodedId := strings.ReplaceAll(knowledgeBranchId, "/", "-")
	return encodedId
}

/**
 * getKnowledgebaseId 生成知识库ID，将路径分隔符替换为连字符
 * @param request - 请求参数
 * @returns string - 处理后的知识库ID
 */
func GetSummaryKnowledgebaseId(request *GetSummaryDataRequest) string {
	knowledgebaseId := knowledgeUtils.GetKnowledgebaseId(request.RepoName)
	// 将路径分隔符 / 替换为连字符 -
	encodedId := strings.ReplaceAll(knowledgebaseId, "/", "-")
	return encodedId
}

/**
 * downloadFilesConcurrently 并发下载多个文件
 * @param ctx - 上下文信息
 * @param tosManager - TOS 管理器接口
 * @param filePaths - 文件路径映射，key为优先级，value为文件路径
 * @returns map[int]*DownloadResult - 下载结果映射
 */
func downloadFilesConcurrently(ctx context.Context, tosManager *tos.TosManager, filePaths map[int]string) map[int]*DownloadResult {
	results := make(map[int]*DownloadResult)
	var mu sync.Mutex

	handlers := make([]func() error, 0)
	for priority, filePath := range filePaths {
		p := priority
		path := filePath
		handlers = append(handlers, func() error {
			result := &DownloadResult{
				Priority: p,
			}

			// 下载文件
			data, err := tosManager.DownloadFileFromTos(ctx, path)
			if err != nil {
				result.Error = err
			} else {
				result.Data = data
			}

			// 线程安全地存储结果
			mu.Lock()
			results[p] = result
			mu.Unlock()
			return nil
		})
	}

	// 使用 group.GoAndWait 并发执行所有下载任务
	group.GoAndWait(handlers...)

	return results
}

/**
 * uploadFilesConcurrently 并发上传多个文件
 * @param ctx - 上下文信息
 * @param tosManager - TOS 管理器接口
 * @param fileData - 要上传的文件数据
 * @param filePaths - 文件路径映射，key为优先级，value为文件路径
 * @returns map[int]*UploadResult - 上传结果映射
 */
func uploadFilesConcurrently(ctx context.Context, tosManager *tos.TosManager, fileData []byte, filePaths map[int]string) map[int]*UploadResult {
	results := make(map[int]*UploadResult)
	var mu sync.Mutex

	handlers := make([]func() error, 0)
	for priority, filePath := range filePaths {
		p := priority
		path := filePath
		handlers = append(handlers, func() error {
			result := &UploadResult{
				Priority: p,
			}

			// 上传文件
			_, err := tosManager.UploadFileToTos(ctx, fileData, path)
			if err != nil {
				result.Error = err
			}

			// 线程安全地存储结果
			mu.Lock()
			results[p] = result
			mu.Unlock()
			return nil
		})
	}

	// 使用 group.GoAndWait 并发执行所有上传任务
	group.GoAndWait(handlers...)

	return results
}

/**
 * selectBestResultAndParse 根据优先级选择最佳结果并解析
 * @param results - 下载结果映射
 * @returns *SummaryData - 最佳的知识库结果
 * @returns error - 错误信息
 */
func selectBestResultAndParse(results map[int]*DownloadResult, filePaths map[int]string) (*entity.SummaryData, string, error) {
	// 按照优先级顺序检查结果
	priorities := []int{1, 2, 3}

	for _, priority := range priorities {
		if result, exists := results[priority]; exists && result.Data != nil && result.Error == nil {
			// 尝试解析为 SummaryData 结构
			var summaryData entity.SummaryData
			if err := json.Unmarshal(result.Data, &summaryData); err != nil {
				log.V2.Error().With(context.Background()).Str("解析知识库失败").KVs(
					"priority", priority,
				).Error(err).Emit()
				continue // 解析失败，尝试下一个优先级
			}

			log.V2.Info().With(context.Background()).Str("选择优先级并解析成功").Int(priority).KVs(
				"priority", priority,
				"modulesCount", len(summaryData.Knowledge.Modules),
			).Emit()
			return &summaryData, filePaths[priority], nil
		}
	}

	return nil, "", fmt.Errorf("没有找到有效的知识库数据")
}
