package main

import (
	"context"
	"os"

	"code.byted.org/ies/codin/application/codesearch/service"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
)

func main() {
	ctx := context.Background()

	os.Setenv("ARK_KEY", "xxxxxxxxxxxxxxxxxx")
	os.Setenv("CODESUMMARY_SEED16_MODEL", "ep-20250714151539-s4vvm")
	service := service.New(ctx)
	service.BuildIndex(ctx, &codesearch.BuildIndexRequest{
		Branch:    "master",
		Language:  "typescript",
		ProjectID: "123",
		RepoName:  "ies/code-index",
		RepoURL:   "https://code.byted.org/ies/code-index",
	})
}
