package handler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codinmodel/kitex_gen/commonerrcode"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_agw/kitex_gen/agw"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch/codesearchservice"
)

type MerkleQueryBuildRequest struct {
	RepoName string `json:"repo_name"`
	RepoPath string `json:"repo_path"`
	Did      string `json:"did"`
	Uid      string `json:"uid"`
	Branch   string `json:"branch"`
}

// MrComment handles the product mr comment request
func MerkleQueryBuild(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	req := &agw.QueryBuildRecordRequest{}
	if err := c.BindJSON(req); err != nil {
		logs.CtxWarn(ctx, "[MerkleQueryBuild] bind json error: %v", err)
		utils.SetResponse(ctx, c, commonerrcode.ErrCode_InvalidArgument, err.Error())
		return
	}

	if req.Uid == "" || req.RepoName == "" || req.Branch == "" || req.RepoPath == "" || req.Did == "" {
		logs.CtxError(ctx, "[MerkleQueryBuild] invalid request: %v", req)
		utils.SetResponse(ctx, c, commonerrcode.ErrCode_InvalidArgument, "invalid request")
		return
	}

	resp, err := handleQueryBuildResponse(ctx, req)
	if err != nil {
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, err.Error())
		return
	}
	utils.SetResponse(ctx, c, resp.BaseResp.StatusCode, resp.BaseResp.GetStatusMessage(), resp)
}

func handleQueryBuildResponse(ctx context.Context, req *agw.QueryBuildRecordRequest) (*codesearch.QueryBuildRecordResponse, error) {
	client := codesearchservice.MustNewClient(
		"capcut.devops.codesearch",
		client.WithConnectTimeout(10*time.Second),
		client.WithRPCTimeout(10*time.Second),
	)
	request := &codesearch.QueryBuildRecordRequest{
		Uid:      req.Uid,
		RepoName: req.RepoName,
		Branch:   req.Branch,
		RepoPath: req.RepoPath,
		Did:      req.Did,
	}

	logs.CtxInfo(ctx, "generate request: %+v", request)

	return client.QueryBuildRecord(ctx, request)
}
