package handler

import (
	"context"
	"net/http"

	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/capcut_devops_agw/kitex_gen/agw"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch/codesearchservice"
)

func AgentRetryCodeSearch(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	req := &agw.RetryCodeSearchAgentRequest{}
	if err := c.<PERSON>d<PERSON>(req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
		})
		return
	}

	client := codesearchservice.MustNewClient(
		"capcut.devops.codesearch",
	)

	resp, err := client.RetryCodeSearch(ctx, &codesearch.RetryCodeSearchRequest{
		Params: req.Params,
	})
	if err != nil {
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, err.Error())
		return
	}
	utils.SetResponse(ctx, c, resp.BaseResp.StatusCode, resp.BaseResp.GetStatusMessage(), resp)
}
