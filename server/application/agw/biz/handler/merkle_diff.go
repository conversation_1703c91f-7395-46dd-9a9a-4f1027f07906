package handler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/ies/codin/application/agw/biz/utils"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codinmodel/kitex_gen/commonerrcode"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/middleware/hertz/pkg/app"
	agw "code.byted.org/overpass/capcut_devops_agw/kitex_gen/agw"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch/codesearchservice"
)

func MerkleDiff(ctx context.Context, c *app.RequestContext) {
	// 处理预检请求
	if string(c.Request.Method()) == "OPTIONS" {
		setCORSHeaders(c)
		c.Status(http.StatusNoContent)
		return
	}

	// 为所有请求设置跨域头
	setCORSHeaders(c)

	req := &agw.GetMerkleDiffRequest{}
	if err := c.BindJSON(req); err != nil {
		logs.CtxError(ctx, "[MerkleDiff] parse json error: %v", err)
		utils.SetResponse(ctx, c, commonerrcode.ErrCode_InvalidArgument, err.Error())
		return
	}

	if req.Uid == "" || req.RepoName == "" || req.Branch == "" || req.RepoPath == "" || req.Did == "" || req.MerkleTreeKey == "" {
		logs.CtxError(ctx, "[MerkleDiff] invalid request: %v", req)
		utils.SetResponse(ctx, c, commonerrcode.ErrCode_InvalidArgument, "invalid request")
		return
	}

	resp, err := handleMerkleDiff(ctx, req)
	if err != nil {
		utils.SetResponse(ctx, c, rpcerr.RetUnknown, err.Error())
		return
	}
	utils.SetResponse(ctx, c, resp.BaseResp.StatusCode, resp.BaseResp.GetStatusMessage(), resp)
}

func handleMerkleDiff(ctx context.Context, req *agw.GetMerkleDiffRequest) (*codesearch.GetMerkleDiffResponse, error) {
	client := codesearchservice.MustNewClient(
		"capcut.devops.codesearch",
		client.WithConnectTimeout(100*time.Second),
		client.WithRPCTimeout(100*time.Second),
	)

	logs.CtxInfo(ctx, "generate request: %+v", req)

	return client.GetMerkleDiff(ctx, &codesearch.GetMerkleDiffRequest{
		MerkleTreeKey: req.MerkleTreeKey,
		Uid:           req.Uid,
		RepoName:      req.RepoName,
		Branch:        req.Branch,
		RepoPath:      req.RepoPath,
		Did:           req.Did,
	})
}
