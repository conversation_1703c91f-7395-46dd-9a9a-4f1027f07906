package einox

import (
	"context"

	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

var defaultOnChatModelStart = func(ctx context.Context, runInfo *callbacks.RunInfo, input *model.CallbackInput) context.Context {
	return ctx
}
var defaultOnChatModelEnd = func(ctx context.Context, runInfo *callbacks.RunInfo, output *model.CallbackOutput) context.Context {
	return ctx
}
var defaultOnChatModelEndWithStreamOutput = func(ctx context.Context, runInfo *callbacks.RunInfo, output *schema.StreamReader[*model.CallbackOutput]) context.Context {
	defer output.Close()
	return ctx
}
var defaultOnGraphError = func(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context {
	return ctx
}
var defaultOnGraphEnd = func(ctx context.Context, runInfo *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context {
	return ctx
}
var defaultOnServerToolStart = func(ctx context.Context, info *MixToolsCallbackInput) context.Context {
	return ctx
}
var defaultOnServerToolEnd = func(ctx context.Context, info *MixToolsCallbackOutput) context.Context {
	return ctx
}
var defaultOnClientToolStart = func(ctx context.Context, info *MixToolsCallbackInput) context.Context {
	return ctx
}

type helper struct {
	onChatModelStart               func(ctx context.Context, runInfo *callbacks.RunInfo, input *model.CallbackInput) context.Context
	onChatModelEnd                 func(ctx context.Context, runInfo *callbacks.RunInfo, output *model.CallbackOutput) context.Context
	onChatModelEndWithStreamOutput func(ctx context.Context, runInfo *callbacks.RunInfo, input *schema.StreamReader[*model.CallbackOutput]) context.Context
	onGraphError                   func(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context
	onGraphEnd                     func(ctx context.Context, runInfo *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context
	onServerToolStart              func(ctx context.Context, info *MixToolsCallbackInput) context.Context
	onServerToolEnd                func(ctx context.Context, info *MixToolsCallbackOutput) context.Context
	onClientToolStart              func(ctx context.Context, info *MixToolsCallbackInput) context.Context
}

func (i *helper) SetChatModelStart(f func(ctx context.Context, runInfo *callbacks.RunInfo, input *model.CallbackInput) context.Context) *helper {
	i.onChatModelStart = f
	return i
}

func (i *helper) SetChatModelEnd(f func(ctx context.Context, runInfo *callbacks.RunInfo, output *model.CallbackOutput) context.Context) *helper {
	i.onChatModelEnd = f
	return i
}

func (i *helper) SetChatModelEndWithStreamOutput(f func(ctx context.Context, runInfo *callbacks.RunInfo, input *schema.StreamReader[*model.CallbackOutput]) context.Context) *helper {
	i.onChatModelEndWithStreamOutput = f
	return i
}

func (i *helper) SetGraphError(f func(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context) *helper {
	i.onGraphError = f
	return i
}

func (i *helper) SetGraphEnd(f func(ctx context.Context, runInfo *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context) *helper {
	i.onGraphEnd = f
	return i
}

func (i *helper) SetServerToolStart(f func(ctx context.Context, info *MixToolsCallbackInput) context.Context) *helper {
	i.onServerToolStart = f
	return i
}

func (i *helper) SetServerToolEnd(f func(ctx context.Context, info *MixToolsCallbackOutput) context.Context) *helper {
	i.onServerToolEnd = f
	return i
}

func (i *helper) SetClientToolStart(f func(ctx context.Context, info *MixToolsCallbackInput) context.Context) *helper {
	i.onClientToolStart = f
	return i
}

func (i *helper) Handler() Callback {
	return i
}

func NewHandlerHelper() *helper {
	return &helper{
		onChatModelStart:               defaultOnChatModelStart,
		onChatModelEnd:                 defaultOnChatModelEnd,
		onChatModelEndWithStreamOutput: defaultOnChatModelEndWithStreamOutput,
		onGraphError:                   defaultOnGraphError,
		onGraphEnd:                     defaultOnGraphEnd,
		onServerToolStart:              defaultOnServerToolStart,
		onServerToolEnd:                defaultOnServerToolEnd,
	}
}

func (i *helper) OnChatModelStart(ctx context.Context, runInfo *callbacks.RunInfo, input *model.CallbackInput) context.Context {
	return i.onChatModelStart(ctx, runInfo, input)
}

func (i *helper) OnChatModelEnd(ctx context.Context, runInfo *callbacks.RunInfo, output *model.CallbackOutput) context.Context {
	return i.onChatModelEnd(ctx, runInfo, output)
}

func (i *helper) OnChatModelEndWithStreamOutput(ctx context.Context, runInfo *callbacks.RunInfo, input *schema.StreamReader[*model.CallbackOutput]) context.Context {
	return i.onChatModelEndWithStreamOutput(ctx, runInfo, input)
}

func (i *helper) OnGraphError(ctx context.Context, runInfo *callbacks.RunInfo, err error) context.Context {
	return i.onGraphError(ctx, runInfo, err)
}

func (i *helper) OnGraphEnd(ctx context.Context, runInfo *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context {
	return i.onGraphEnd(ctx, runInfo, output)
}

func (i *helper) OnServerToolStart(ctx context.Context, info *MixToolsCallbackInput) context.Context {
	return i.onServerToolStart(ctx, info)
}

func (i *helper) OnServerToolEnd(ctx context.Context, info *MixToolsCallbackOutput) context.Context {
	return i.onServerToolEnd(ctx, info)
}

func (i *helper) OnClientToolStart(ctx context.Context, info *MixToolsCallbackInput) context.Context {
	return i.onClientToolStart(ctx, info)
}
