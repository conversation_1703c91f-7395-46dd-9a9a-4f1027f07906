```mermaid
graph TD
    subgraph "服务端 Agent 执行图 (react.go) - 优化版"
        A[/"用户输入"/] --> B("1.模型思考<br>决定下一步行动<br>(model_node)");
        B -- "模型输出决策" --> C{"2.工具分发<br>检查模型是否要调用工具<br>(part_tool_call_node)"};
        C -- "决策: 需要调用【服务端】工具" --> D("3.执行所有服务端工具<br>(tools_node)");
        C -- "决策: 只需调用【客户端】工具" --> G("5.中断流程<br>将工具调用指令发给客户端<br>(client_tool_node_1)");
        C -- "决策: 无需工具，直接回答" --> H[/"完成并输出答案"/];
        D -- "服务端工具执行完毕" --> E{"4.检查是否还需<br>调用【客户端】工具<br>(after_server_tools_node)"};
        E -- "决策: 是，还需客户端工具" --> I("5.中断流程<br>将工具调用指令发给客户端<br>(client_tool_node_2)");
        E -- "决策: 否，将结果喂给模型<br>进行新一轮思考 (循环)" --> B;
        G --> H;
        I --> H;
    end
```