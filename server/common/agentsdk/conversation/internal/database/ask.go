package database

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/common/agentsdk/conversation/internal/database/sql"
	"code.byted.org/ies/codin/common/agentsdk/conversation/model"
)

func (i *impl) SaveAsk(ctx context.Context, ask *model.Ask) error {
	now := time.Now()

	inputBytes, err := json.Marshal(ask.Input)
	if err != nil {
		logs.Error("marshal input failed: %v, ask: %+v", err, ask)
		return err
	}

	outputBytes, err := json.Marshal(ask.Output)
	if err != nil {
		logs.Error("marshal output failed: %v, ask: %+v", err, ask)
		return err
	}

	tokenUsageBytes, err := json.Marshal(ask.TokenUsage)
	if err != nil {
		logs.Error("marshal token usage failed: %v, ask: %+v", err, ask)
		return err
	}

	_, err = i.writer.ExecContext(ctx, sql.SaveAskSQL, ask.ConvId, ask.Uid, string(inputBytes), string(outputBytes), string(tokenUsageBytes), now)
	if err != nil {
		logs.Error("insert ask failed: %v, ask: %+v", err, ask)
		return err
	}
	return nil
}
