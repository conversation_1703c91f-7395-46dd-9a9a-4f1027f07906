CREATE TABLE IF NOT EXISTS `conversation_ask` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `conv_id` varchar(64) NOT NULL COMMENT '所属会话ID',
  `uid` varchar(32) DEFAULT '' COMMENT '这条消息的用户ID',
  `input` json DEFAULT NULL COMMENT '输入内容,不包含sp',
  `output` json DEFAULT NULL COMMENT '输出内容',
  `token_usage` json DEFAULT NULL COMMENT 'token使用信息',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_conv_id` (`conv_id`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单论任务表';
