package utils

import (
	"bytes"
	"reflect"

	"code.byted.org/bcc/conf_engine/jsoniter"
)

// TransformStruct 两个数据结构转换 泛型方法
func TransformStruct[T any](v1 any) (*T, error) {
	var t T
	obj := reflect.New(reflect.TypeOf(t)).Interface().(*T)

	ret, err := jsoniter.Marshal(v1)
	if err != nil {
		return nil, err
	}

	d := jsoniter.NewDecoder(bytes.NewBuffer(ret))
	d.Use<PERSON>umber()
	err = d.Decode(obj)
	if err != nil {
		return nil, err
	}
	return obj, nil
}
