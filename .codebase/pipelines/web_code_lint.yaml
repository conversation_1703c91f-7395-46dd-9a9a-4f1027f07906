name: Web Lint Check
trigger: [change]
jobs:
  build:
    name: Web Lint Check
    image: hub.byted.org/capcut/ccweb.playwright:e1c60f11f6b24969bee92660830e8f57
    envs:
      NODE_OPTIONS: "--max-old-space-size=32768"
    steps:
      - name: Init
        commands:
          - cd web
          - npm config set registry https://bnpm.byted.org
          - pnpm i
          - cd ..
          - ./web/node_modules/.bin/biome format ./web