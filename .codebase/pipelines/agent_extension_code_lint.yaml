name: Agent <PERSON> Lint Check
trigger: [change]
jobs:
  build:
    name: Agent <PERSON> Lint Check
    image: hub.byted.org/capcut/ccweb.playwright:e1c60f11f6b24969bee92660830e8f57
    envs:
      NODE_OPTIONS: "--max-old-space-size=32768"
    steps:
      - name: Init
        commands:
          - cd agent-extension
          - npm config set registry https://bnpm.byted.org
          - pnpm i
          - cd ../web
          - pnpm i
      - name: <PERSON>t
        commands:
          - ./web/node_modules/.bin/biome format ./agent-extension