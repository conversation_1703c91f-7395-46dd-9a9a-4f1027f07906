name: <PERSON>
trigger: [change]
jobs:
  build:
    name: <PERSON>t
    image: hub.byted.org/codebase/ci_go_1_23:latest
    steps:
      - commands:
        - go version
        - cd server
        - go vet ./...
        # - go install github.com/golangci/golangci-lint/v2/cmd/golangci-lint@v2.1.6
        # - cd server
        # - go mod tidy
        # - golangci-lint --version
        # - golangci-lint run --disable=unused