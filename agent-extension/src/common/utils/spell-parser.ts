/**
 * 将字符串由驼峰转换为下划线拼写
 * @param originString 源字符串
 * @returns 格式化后的字符串
 */
export function toSnakeCase(originString?: string): string {
  return originString ? originString.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`) : '';
}

/**
 * 将字符串由下划线转换为驼峰拼写
 * @param originString 源字符串
 * @returns 格式化后的字符串
 */
export function toCamelCase(originString?: string): string {
  return originString ? originString.replace(/_([a-z])/g, (_match, letter) => letter.toUpperCase()) : '';
}

/**
 * 数据转换
 * @param data 原始数据
 * @param convertFunc 转换函数
 * @returns 转换后的数据
 */
export function convertData(
  // biome-ignore lint/suspicious/noExplicitAny: any
  data: any,
  convertFunc: (key: string) => string,
  // biome-ignore lint/suspicious/noExplicitAny: any
): any {
  if (data && typeof data === 'object') {
    for (const key of Object.keys(data)) {
      const newKey = convertFunc(key);
      if (newKey !== key) {
        data[newKey] = data[key];
        delete data[key];
      }
      convertData(data[newKey], convertFunc);
    }
  }

  return data;
}
