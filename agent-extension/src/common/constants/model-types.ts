export enum ModelType {
  DeepSeek = 0,
  <PERSON> = 1,
  DoubaoPro = 2,
  DoubaoThinkingPro = 3,
  DeepSeekR1 = 4,
  GPT4_1 = 5,
  <PERSON>2_5_<PERSON> = 6,
  <PERSON><PERSON>_Sonnet = 7,
  <PERSON><PERSON>_<PERSON> = 8,
  Seed1_6_Thinking = 11,
  GPT_O3 = 12,
  Seed1_6 = 13,
  <PERSON><PERSON>_K2 = 17,
}

export type ModelTypeKey = keyof typeof ModelType;

export const DEFAULT_MODEL_TYPE: ModelType = ModelType.DeepSeek;
