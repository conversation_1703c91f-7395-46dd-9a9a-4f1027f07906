import type { ReactNode } from 'react';

export interface ConversationType {
  value: string;
  label: string;
  description: string;
  icon: string;
}

export interface TabItem {
  id: string;
  visible: boolean;
  label: string;
  icon: string;
  description: string;
  placeholder: string;
  emptyStateTitle: string;
  emptyStateDescription: ReactNode;
  sendMessageCommand: string;
  page: ConversationTypeEnum;
  createConversationCommand: string;
}

export enum ConversationTypeEnum {
  TechDocs = 'techDocs',
  Understanding = 'understanding',
  Coding = 'coding',
  D2c = 'd2c',
  Codesearch = 'codesearch',
}

export const CHAT_MODES: TabItem[] = [
  {
    id: 'chat',
    visible: true,
    label: '技术方案',
    icon: '💬',
    description: 'AI驱动的技术架构设计',
    placeholder: '描述你的技术需求，比如：设计一个高并发的电商系统... 使用@引用文件',
    emptyStateTitle: '开始设计你的技术方案',
    emptyStateDescription: '描述你的需求，我会帮你制定详细的技术架构方案',
    sendMessageCommand: 'techDocs.sendMessage',
    createConversationCommand: 'techDocs.createConversation',
    page: ConversationTypeEnum.TechDocs,
  },
  {
    id: 'coding',
    visible: true,
    label: '智能编程',
    icon: '🚀',
    description: '代码生成与优化助手',
    placeholder: '描述你想要实现的功能，比如：创建一个React组件... 使用@引用文件',
    emptyStateTitle: '开始你的编程之旅',
    emptyStateDescription: (
      <div>
        <span>告诉我你想要实现什么功能，我会为你生成高质量的代码</span>
        <p style={{ marginTop: '10px' }}>输入 @ 添加文件、目录（需以 / 结尾）</p>
      </div>
    ),
    sendMessageCommand: 'coding.sendMessage',
    createConversationCommand: 'coding.createConversation',
    page: ConversationTypeEnum.Coding,
  },
  {
    id: 'd2c',
    visible: false,
    label: '设计转代码',
    icon: '🔍',
    description: 'UI设计稿转换为代码',
    placeholder: '描述你的UI设计，或者上传设计稿...',
    emptyStateTitle: '开始你的 D2C 之旅',
    emptyStateDescription: '上传设计稿或描述UI界面，我会为你转换成高质量的代码',
    sendMessageCommand: 'd2c.sendMessage',
    createConversationCommand: 'd2c.createConversation',
    page: ConversationTypeEnum.D2c,
  },
  {
    id: 'private',
    visible: false,
    label: '私人助理',
    icon: '🙋',
    description: '个人定制化agent',
    placeholder: '干你想干的任何事情',
    emptyStateTitle: '开始你的私人助理之旅',
    emptyStateDescription: '描述你的需求，我会为你提供帮助',
    sendMessageCommand: 'private.sendMessage',
    createConversationCommand: 'private.createConversation',
    // @ts-expect-error
    page: 'private',
  },
  {
    id: 'understanding',
    visible: false,
    label: '需求理解',
    icon: '🧔',
    description: 'AI驱动的需求理解',
    placeholder: '描述你的需求，比如：设计一个高并发的电商系统... 使用@引用文件',
    emptyStateTitle: '开始你的需求理解之旅',
    emptyStateDescription: '描述你的需求，我会为你生成详细的需求理解',
    sendMessageCommand: 'understanding.sendMessage',
    createConversationCommand: 'understanding.createConversation',
    page: ConversationTypeEnum.Understanding,
  },
  {
    id: 'codesearch',
    visible: false,
    label: '代码语义理解搜索',
    icon: '🔍',
    description: 'AI驱动的需求理解',
    placeholder: '描述你的需求，比如：了解下dreamina的架构',
    emptyStateTitle: '开始你的代码理解搜索之旅',
    emptyStateDescription: '描述你的需求，我会为你提供帮助',
    sendMessageCommand: 'codesearch.sendMessage',
    createConversationCommand: 'codesearch.createConversation',
    page: ConversationTypeEnum.Codesearch,
  },
];

// 从 CHAT_MODES 派生出 CONVERSATION_TYPES
export const CONVERSATION_TYPES: Record<ConversationTypeEnum, ConversationType> = {
  [ConversationTypeEnum.TechDocs]: {
    value: 'techDocs',
    label: CHAT_MODES[0].label,
    description: CHAT_MODES[0].description,
    icon: CHAT_MODES[0].icon,
  },
  [ConversationTypeEnum.Coding]: {
    value: 'coding',
    label: CHAT_MODES[1].label,
    description: CHAT_MODES[1].description,
    icon: CHAT_MODES[1].icon,
  },
  [ConversationTypeEnum.D2c]: {
    value: 'd2c',
    label: CHAT_MODES[2].label,
    description: CHAT_MODES[2].description,
    icon: CHAT_MODES[2].icon,
  },
  [ConversationTypeEnum.Understanding]: {
    value: 'understanding',
    label: CHAT_MODES[3].label,
    description: CHAT_MODES[3].description,
    icon: CHAT_MODES[3].icon,
  },
  [ConversationTypeEnum.Codesearch]: {
    value: 'codesearch',
    label: CHAT_MODES[5].label,
    description: CHAT_MODES[5].description,
    icon: CHAT_MODES[5].icon,
  },
};

export function getConversationFullLabel(type: string): string {
  const conversationType = Object.values(CONVERSATION_TYPES).find((ct) => ct.value === type);
  return conversationType
    ? `${conversationType.icon} ${conversationType.label}`
    : `${CONVERSATION_TYPES[ConversationTypeEnum.Coding].icon} ${CONVERSATION_TYPES[ConversationTypeEnum.Coding].label}`;
}

export function getConversationType(type: string): ConversationType {
  return (
    Object.values(CONVERSATION_TYPES).find((ct) => ct.value === type) || CONVERSATION_TYPES[ConversationTypeEnum.Coding]
  );
}

export const CONVERSATION_TYPE_OPTIONS = Object.values(CONVERSATION_TYPES).map((type) => ({
  ...type,
  fullLabel: `${type.icon} ${type.label}`,
}));

export const DEFAULT_CONVERSATION_TYPE = ConversationTypeEnum.Coding;
