import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { defer } from '@byted-image/lv-bedrock/promise';
import pino, { type Logger, type LoggerOptions } from 'pino';

import { PinoRollStream } from './destination-streams/pino-roll';
import { VSCodeOutputChannelStream } from './destination-streams/output-channel';
import { type IFileLoggerService } from './file-logger-service.interface';
import type { IDestinationStream } from './destination-streams/destination-stream.interface';
import { isExportableDestinationStream } from './destination-streams/destination-exportable.interface';

/**
 * 文件日志服务实现
 */
export class FileLoggerService implements IFileLoggerService {
  public readonly _serviceBrand = undefined;

  private _logger: Logger | undefined;
  private _initDefer = defer();
  private _destinationStreams: IDestinationStream[] = [];

  constructor(@IInstantiationService private readonly _instantiationService: IInstantiationService) {
    this._init();
  }

  public initialize() {
    return this._initDefer.promise;
  }

  private async _init() {
    try {
      this._destinationStreams.push(
        this._instantiationService.createInstance(PinoRollStream),
        this._instantiationService.createInstance(VSCodeOutputChannelStream),
      );

      await Promise.all(this._destinationStreams.map((stream) => stream.initialize()));

      const loggerOptions: LoggerOptions = {
        level: 'info',
        timestamp: pino.stdTimeFunctions.isoTime,
      };

      this._logger = pino(
        loggerOptions,
        pino.multistream(this._destinationStreams.map((stream) => ({ stream, level: 'info' }))),
      );

      this._initDefer.resolve();
    } catch (error) {
      console.error('Failed to initialize file logger:', error);
      this._initDefer.reject(error);
    }
  }

  public info(message: string): void {
    try {
      console.log(message);
      this._logger!.info(message);
    } catch {}
  }

  public warn(message: string): void {
    try {
      console.warn(message);
      this._logger!.warn(message);
    } catch {}
  }

  public error(message: string, error?: Error): void {
    try {
      console.error(error, message);
      this._logger!.error(error, message);
    } catch {}
  }

  public dispose(): void {
    for (const stream of this._destinationStreams) {
      stream.dispose();
    }
  }

  /**
   * 导出日志文件目录
   */
  public getLogFileFolder() {
    for (const stream of this._destinationStreams) {
      if (isExportableDestinationStream(stream)) {
        return stream.getLogFileFolderForExport();
      }
    }
    throw new Error('没有可导出的日志');
  }
}
