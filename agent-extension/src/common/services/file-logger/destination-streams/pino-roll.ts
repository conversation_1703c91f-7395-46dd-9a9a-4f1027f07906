import { defer } from '@byted-image/lv-bedrock/promise';
import * as path from 'node:path';
import pinoRoll from 'pino-roll';
import { IExtensionContextService } from '../../commands/extension-context.interface';
import type { IDestinationStream } from './destination-stream.interface';
import type { IExportableDestinationStream } from './destination-exportable.interface';

export class PinoRollStream implements IDestinationStream, IExportableDestinationStream {
  private readonly _logFileName = 'codin.log';
  private _logFilePath?: string;
  private _roll: ReturnType<typeof pinoRoll>;
  private _initDefer = defer();

  constructor(@IExtensionContextService private readonly _contextService: IExtensionContextService) {
    this._init();
  }

  public initialize() {
    return this._initDefer.promise;
  }

  public dispose() {
    // 清理逻辑
  }

  public write(...args: any[]): void {
    this._roll.write(...args);
  }

  public getLogFileFolderForExport(): string {
    return this._contextService.context.logUri.fsPath;
  }

  private async _init() {
    try {
      const logFolderPath = this._contextService.context.logUri.fsPath;
      this._logFilePath = path.join(logFolderPath, this._logFileName);
      this._roll = await pinoRoll({
        file: this._logFilePath,
        frequency: 'daily',
        mkdir: true,
      });

      this._initDefer.resolve();
    } catch (error) {
      console.error('Failed to initialize pino roll stream:', error);
      this._initDefer.reject(error);
    }
  }
}
