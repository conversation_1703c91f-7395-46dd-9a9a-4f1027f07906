import { window, type LogOutputChannel } from 'vscode';
import type { IDestinationStream } from './destination-stream.interface';

export class VSCodeOutputChannelStream implements IDestinationStream {
  private _outputChannel: LogOutputChannel;

  constructor() {
    this._outputChannel = window.createOutputChannel('Codin: Logger', {
      log: true,
    });
  }

  public initialize() {
    return Promise.resolve();
  }

  public dispose() {
    this._outputChannel.dispose();
  }

  public write(chunk: string): void {
    try {
      const log = JSON.parse(chunk.toString());
      const level = log.level === 30 ? 'INFO' : log.level === 40 ? 'WARN' : 'ERROR';
      const message = `[${level}] ${log.time} - ${log.msg}`;
      this._outputChannel.appendLine(message);
    } catch (error) {
      this._outputChannel.appendLine(`[ERROR] Failed to parse log: ${chunk.toString()}`);
    }
  }
}
