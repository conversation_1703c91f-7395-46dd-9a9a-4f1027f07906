import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import type { ConversationTypeEnum } from '@/common/constants/conversation-types';

export interface IConversationCommandsService {
  _serviceBrand: undefined;

  // 新增会话命令
  newConversation(conversationType?: ConversationTypeEnum): Promise<ILvErrorOr<string>>;

  // 查看历史会话命令
  viewHistory(): Promise<ILvErrorOr<void>>;
}

export const IConversationCommandsService = createDecorator<IConversationCommandsService>(
  'conversation-commands-service',
);
