import { createDecorator } from '@byted-image/lv-bedrock/di';

export interface IStorageService {
  readonly _serviceBrand: undefined;
  /** 读取数据 */
  get: (key: string) => Thenable<string | undefined>;
  /** 存储数据 */
  set: (key: string, value: any) => Thenable<void>;
  /** 删除数据 */
  delete: (key: string) => Thenable<void>;
  /** 存储数据（在云真机评测环境有兼容性问题） */
  secretStore: (key: string, value: string) => Thenable<void>;
  /** 读取数据（在云真机评测环境有兼容性问题） */
  secretGet: (key: string) => Thenable<string | undefined>;
  /** 删除数据（在云真机评测环境有兼容性问题） */
  secretDelete: (key: string) => Thenable<void>;
}

export const IStorageService = createDecorator<IStorageService>('storage-service');
