import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';
import * as vscode from 'vscode';
import { GitService } from './git-service';

// Since we have global mocks, we can use vi.mocked to get typed mocks
const mockedVscode = vi.mocked(vscode);

describe('GitService', () => {
  let gitService: GitService;

  const mockGitApi = {
    repositories: [
      {
        state: {
          workingTreeChanges: [{ uri: { fsPath: '/test/file1.ts' } }],
          indexChanges: [{ uri: { fsPath: '/test/file2.ts' } }],
          mergeChanges: [{ uri: { fsPath: '/test/file3.ts' } }],
        },
      },
    ],
  };

  const mockGitExtension = {
    exports: {
      getAPI: vi.fn().mockReturnValue(mockGitApi),
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    gitService = new GitService();
  });

  describe('getGitApi', () => {
    it('should return the Git API if the extension is found', () => {
      (mockedVscode.extensions.getExtension as Mock).mockReturnValue(mockGitExtension as any);
      const api = gitService.getGitApi();
      expect(api).toBe(mockGitApi);
      expect(mockedVscode.extensions.getExtension).toHaveBeenCalledWith('vscode.git');
    });

    it('should show an error message and return undefined if the Git extension is not found', () => {
      (mockedVscode.extensions.getExtension as Mock).mockReturnValue(undefined);
      const api = gitService.getGitApi();
      expect(api).toBeUndefined();
      expect(mockedVscode.window.showErrorMessage).toHaveBeenCalledWith('Git extension not found.');
    });

    it('should cache the API instance after the first call', () => {
      (mockedVscode.extensions.getExtension as Mock).mockReturnValue(mockGitExtension as any);
      gitService.getGitApi(); // First call
      gitService.getGitApi(); // Second call
      expect(mockedVscode.extensions.getExtension).toHaveBeenCalledTimes(1);
    });
  });

  describe('getChangedFiles', () => {
    it('should return a list of changed files if a repository is found', () => {
      (mockedVscode.extensions.getExtension as Mock).mockReturnValue(mockGitExtension as any);
      const result = gitService.getChangedFiles();
      expect(result).toEqual({
        files: ['/test/file1.ts', '/test/file2.ts', '/test/file3.ts'],
      });
    });

    it('should show an error and return undefined if no repository is found', () => {
      const emptyApi = { repositories: [] };
      const emptyGitExtension = { exports: { getAPI: vi.fn().mockReturnValue(emptyApi) } };
      (mockedVscode.extensions.getExtension as Mock).mockReturnValue(emptyGitExtension as any);

      const result = gitService.getChangedFiles();
      expect(result).toBeUndefined();
      expect(mockedVscode.window.showErrorMessage).toHaveBeenCalledWith('No Git repository found.');
    });

    it('should show an error and return undefined if the Git extension is not found', () => {
      (mockedVscode.extensions.getExtension as Mock).mockReturnValue(undefined);
      const result = gitService.getChangedFiles();
      expect(result).toBeUndefined();
      expect(mockedVscode.window.showErrorMessage).toHaveBeenCalledWith('Git extension not found.');
    });
  });
});
