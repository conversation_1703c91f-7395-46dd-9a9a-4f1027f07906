import type { IGitService } from './git-service.interface';
import type { API as GitApi, GitExtension } from '@/typings/git';
import * as vscode from 'vscode';

export class GitService implements IGitService {
  private _apiInstance: GitApi | undefined;

  getGitApi() {
    if (!this._apiInstance) {
      const gitExtension = vscode.extensions.getExtension<GitExtension>('vscode.git')?.exports;
      if (!gitExtension) {
        vscode.window.showErrorMessage('Git extension not found.');
        return undefined;
      }

      this._apiInstance = gitExtension.getAPI(1);
    }
    return this._apiInstance;
  }

  getChangedFiles(): { files: string[] } | undefined {
    const gitExtension = vscode.extensions.getExtension<GitExtension>('vscode.git')?.exports;
    if (!gitExtension) {
      vscode.window.showErrorMessage('Git extension not found.');
      return undefined;
    }

    // Get the Git API
    const git = this.getGitApi();
    if (!git?.repositories.length) {
      vscode.window.showErrorMessage('No Git repository found.');
      return undefined;
    }

    // Get the first repository (or iterate over git.repositories for multiple repos)
    const repo = git.repositories[0];

    // Get the status of the repository
    const changedFiles: string[] = [];

    // Collect changed files (staged, unstaged, and untracked)
    for (const change of repo.state.workingTreeChanges) {
      changedFiles.push(change.uri.fsPath);
    }
    for (const change of repo.state.indexChanges) {
      changedFiles.push(change.uri.fsPath);
    }
    for (const change of repo.state.mergeChanges) {
      changedFiles.push(change.uri.fsPath);
    }

    return {
      files: changedFiles,
    };
  }
}
