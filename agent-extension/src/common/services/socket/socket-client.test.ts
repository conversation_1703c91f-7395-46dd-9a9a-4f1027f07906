import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';
import type { FWS, FrontierMessageEvent, CustomErrorEvent, CustomCloseEvent } from '@byted/frontier-web-sdk';
import { SocketClientImpl } from './socket-client';

// Mock FWS type
type MockFws = {
  [K in keyof FWS]: FWS[K] extends (...args: any[]) => any ? Mock<ReturnType<FWS[K]>> : FWS[K];
};

describe('SocketClientImpl', () => {
  let mockFws: MockFws;
  let socketClient: SocketClientImpl;
  // Store the event listeners passed to addEventListener
  const listeners: Record<string, (event: any) => void> = {};

  beforeEach(() => {
    vi.clearAllMocks();

    mockFws = {
      addEventListener: vi.fn((event, listener) => {
        listeners[event] = listener;
      }),
      removeEventListener: vi.fn(),
      send: vi.fn(),
    } as unknown as MockFws;

    socketClient = new SocketClientImpl(mockFws as unknown as FWS);
  });

  it('should add event listeners to FWS on construction', () => {
    expect(mockFws.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    expect(mockFws.addEventListener).toHaveBeenCalledWith('error', expect.any(Function));
    expect(mockFws.addEventListener).toHaveBeenCalledWith('open', expect.any(Function));
    expect(mockFws.addEventListener).toHaveBeenCalledWith('close', expect.any(Function));
  });

  it('should fire onMessage when FWS dispatches a message event', () => {
    const messageHandler = vi.fn();
    socketClient.onMessage(messageHandler);

    const mockMessageEvent: FrontierMessageEvent = {
      message: {
        method: 1,
        textPayload: 'hello world',
      },
    } as FrontierMessageEvent;

    // Trigger the captured message listener
    listeners.message(mockMessageEvent);

    expect(messageHandler).toHaveBeenCalledWith({
      method: 1,
      value: 'hello world',
    });
  });

  it('should fire onError when FWS dispatches an error event', () => {
    const errorHandler = vi.fn();
    socketClient.onError(errorHandler);

    const mockErrorEvent: CustomErrorEvent = {
      code: 500,
      message: 'Server Error',
      error: new Error('details'),
    } as CustomErrorEvent;

    listeners.error(mockErrorEvent);

    expect(errorHandler).toHaveBeenCalledWith({
      code: 500,
      message: 'Server Error',
      error: expect.any(Error),
    });
  });

  it('should fire onOpen when FWS dispatches an open event', () => {
    const openHandler = vi.fn();
    socketClient.onOpen(openHandler);

    listeners.open({} as unknown as Event);

    expect(openHandler).toHaveBeenCalled();
  });

  it('should fire onClose when FWS dispatches a close event', () => {
    const closeHandler = vi.fn();
    socketClient.onClose(closeHandler);

    const mockCloseEvent: CustomCloseEvent = { code: 1000, reason: 'Normal closure' } as CustomCloseEvent;
    listeners.close(mockCloseEvent);

    expect(closeHandler).toHaveBeenCalled();
  });

  it('should call fws.send when send is called', () => {
    const message = 'test message';
    const options = { method: 2, service: 99 };
    socketClient.send(message, options);
    expect(mockFws.send).toHaveBeenCalledWith(message, options);
  });

  it('should remove event listeners on dispose', () => {
    socketClient.dispose();
    expect(mockFws.removeEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    expect(mockFws.removeEventListener).toHaveBeenCalledWith('error', expect.any(Function));
    expect(mockFws.removeEventListener).toHaveBeenCalledWith('open', expect.any(Function));
    expect(mockFws.removeEventListener).toHaveBeenCalledWith('close', expect.any(Function));
  });
});
