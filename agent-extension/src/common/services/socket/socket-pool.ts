export interface PooledResource<T> {
  resource: T;
  refCount: number;
}

export class SocketPool<T> {
  private _pool: Map<string, PooledResource<T>> = new Map();
  private _createResource: (key: string) => T;
  private _disposer: (resource: T) => void;

  constructor(createResource: (key: string) => T, disposer: (resource: T) => void) {
    this._createResource = createResource;
    this._disposer = disposer;
  }

  getOrCreate(key: string): T {
    const pooled = this._pool.get(key);
    if (pooled) {
      pooled.refCount++;
      return pooled.resource;
    }
    const resource = this._createResource(key);
    this._pool.set(key, { resource, refCount: 1 });
    return resource;
  }

  release(resource: T) {
    let foundKey: string | undefined;
    for (const [key, pooled] of this._pool.entries()) {
      if (pooled.resource === resource) {
        foundKey = key;
        break;
      }
    }
    if (!foundKey) return;

    const pooled = this._pool.get(foundKey);
    if (!pooled) return;

    pooled.refCount--;
    if (pooled.refCount <= 0) {
      this._disposer?.(pooled.resource);
      this._pool.delete(foundKey);
    }
  }
}
