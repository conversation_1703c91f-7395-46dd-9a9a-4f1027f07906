import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { Event } from '@byted-image/lv-bedrock/event';

export interface MessageEvent {
  method: number;
  value: string;
}

export interface ErrorEvent {
  code: number;
  message: string;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  error: any;
}

export interface SocketClient {
  dispose: () => void;
  send: (message: string, options?: { method?: number; service?: number }) => void;
  onMessage: Event<[MessageEvent]>;
  onError: Event<[ErrorEvent]>;
  onOpen: Event<[]>;
  onClose: Event<[]>;
}

export interface ConnectParams {
  // 服务ID
  serviceId: number;
  // 会话ID
  conversationId: string;
  // 是否调试
  debug?: boolean;
  // 环境
  env?: {
    xUseEnv: 'ppe' | 'boe';
    xTTEnv: string;
  };
}

export interface ISocketService {
  _serviceBrand: undefined;

  connect: (params: ConnectParams) => SocketClient;

  release: (socketClient: SocketClient) => void;
}

export const ISocketService = createDecorator<ISocketService>('socket-service');
