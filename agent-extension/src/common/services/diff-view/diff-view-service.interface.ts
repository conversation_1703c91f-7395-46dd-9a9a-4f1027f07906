import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { Event } from '@byted-image/lv-bedrock/event';
import type { Disposable } from 'vscode'; // INFO: 仅依赖类型

export interface IDiffViewService {
  _serviceBrand: undefined;

  editType?: 'create' | 'modify';
  isEditing: boolean;
  originalContent: string | undefined;

  open(relPath: string): Promise<void>;

  update(accumulatedContent: string, isFinal: boolean): Promise<void>;

  saveChanges(autoCloseAllDiffViews: boolean): Promise<{
    newProblemsMessage: string | undefined;
    userEdits: string | undefined;
    autoFormattingEdits: string | undefined;
    finalContent: string | undefined;
  }>;

  openDiffViewAfterSave(): Promise<void>;

  revertChanges(autoCloseAllDiffViews: boolean): Promise<void>;

  scrollToFirstDiff(): void;

  reset(): Promise<void>;

  closeAllDiffViews(): Promise<void>;

  /* 对于vscode来说是 workspace.registerTextDocumentContentProvider */
  registerDiffViewContentProvider(): Disposable;

  onChangeIsEditing: Event<[]>;
}

export const IDiffViewService = createDecorator<IDiffViewService>('diff-view');
