import * as http from 'node:http';
import { Emitter } from '@byted-image/lv-bedrock/event';
import { workspace } from 'vscode';
import { type ILocalServerRequest, ILocalServerService } from './local-server-service.interface';
import { defer } from '@byted-image/lv-bedrock/promise';

export class LocalServerService implements ILocalServerService {
  public readonly _serviceBrand: undefined;
  private _server: http.Server | null = null;
  private _retryCount = 0;
  private _maxRetries = 20; // 应该能兼容多开N个IDE的情况了
  private _retryDelay = 1000; // 1秒
  private _port: number = workspace.getConfiguration('localServer').get('port') || 40001;
  private _initDefer = defer();

  private readonly _onRequest = new Emitter<[ILocalServerRequest]>();

  public get onRequest() {
    return this._onRequest.event;
  }

  public get port() {
    return this._port;
  }

  constructor() {
    this._initialize();
  }

  public initialize() {
    return this._initDefer.promise;
  }

  public dispose() {
    this._server?.close();
    this._server = null;
  }

  private async _initialize() {
    while (this._retryCount < this._maxRetries) {
      try {
        await this._startServer();
        this._retryCount = 0; // 重置重试计数
        this._initDefer.resolve();
        return;
      } catch (error) {
        this._retryCount++;
        if (this._retryCount >= this._maxRetries) {
          this._initDefer.reject(error);
          return;
        }
        console.log(`Retrying to start server (${this._retryCount}/${this._maxRetries})...`);
        await new Promise((resolve) => setTimeout(resolve, this._retryDelay));
      }
    }
  }

  private _startServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this._server = http.createServer((req, res) => {
          const url = new URL(req.url || '', `http://${req.headers.host}`);
          const path = url.pathname;
          const query = Object.fromEntries(url.searchParams);
          let body = {};
          if (req.method === 'POST' || req.method === 'PUT') {
            let data = '';
            req.on('data', (chunk) => {
              data += chunk;
            });
            req.on('end', () => {
              try {
                body = JSON.parse(data);
              } catch (e) {
                body = {};
              }
            });
          }

          this._onRequest.fire({
            method: req.method,
            path: path,
            headers: req.headers,
            query: query,
            body: body,
          });

          // 目前只有登录模块在用，写死返回一个html，内部自动关闭窗口
          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(`
              <html>
                <body>
                  <script>window.close();</script>
                </body>
              </html>
            `);
        });

        this._server.listen(this._port, () => {
          console.log(`Server is running on port ${this._port}`);
          resolve();
        });

        this._server.on('error', (error) => {
          if (error instanceof Error && 'code' in error && error.code === 'EADDRINUSE') {
            const oldPort = this._port;
            this._port += 1; // 下次自动换一个端口
            reject(new Error(`Port ${oldPort} is already in use, trying ${this._port}`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
