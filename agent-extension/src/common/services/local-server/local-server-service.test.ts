import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as http from 'node:http';
import { EventEmitter } from 'node:events';
import { workspace } from 'vscode';
import { LocalServerService } from './local-server-service';

// Mock vscode API
vi.mock('vscode', () => ({
  workspace: {
    getConfiguration: vi.fn(),
  },
}));

// Mock http module
vi.mock('node:http', () => ({
  createServer: vi.fn(),
}));

const mockedHttp = vi.mocked(http);
const mockedWorkspace = vi.mocked(workspace);

describe('LocalServerService', () => {
  let mockServer: {
    listen: ReturnType<typeof vi.fn>;
    on: ReturnType<typeof vi.fn>;
    close: ReturnType<typeof vi.fn>;
  };
  let serverEmitter: EventEmitter;

  beforeEach(() => {
    vi.resetAllMocks();
    vi.spyOn(console, 'log').mockImplementation(() => {});

    mockedWorkspace.getConfiguration.mockReturnValue({
      get: vi.fn().mockReturnValue(40001),
    } as any);

    serverEmitter = new EventEmitter();
    mockServer = {
      listen: vi.fn(),
      on: vi.fn((event, callback) => {
        serverEmitter.on(event, callback);
      }),
      close: vi.fn((callback) => {
        if (callback) callback();
      }),
    };
    mockedHttp.createServer.mockReturnValue(mockServer as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize and start the server on the configured port', async () => {
      mockServer.listen.mockImplementation((_port, callback) => {
        if (callback) callback();
      });
      const service = new LocalServerService();
      await service.initialize();

      expect(mockServer.listen).toHaveBeenCalledOnce();
      expect(service.port).toBe(40001);
      service.dispose();
    });

    it('should use the fallback port if configuration is not set', async () => {
      mockedWorkspace.getConfiguration.mockReturnValue({
        get: vi.fn().mockReturnValue(undefined), // Simulate no port configured
      } as any);

      mockServer.listen.mockImplementation((_port, callback) => {
        if (callback) callback();
      });

      const service = new LocalServerService();
      await service.initialize();

      expect(service.port).toBe(40001);
      expect(mockServer.listen).toHaveBeenCalledWith(40001, expect.any(Function));
      service.dispose();
    });

    it('should try the next port if the current one is in use', async () => {
      const initialPort = 40001;

      mockServer.listen
        .mockImplementationOnce((port) => {
          process.nextTick(() => {
            const error: any = new Error(`Port ${port} is already in use`);
            error.code = 'EADDRINUSE';
            serverEmitter.emit('error', error);
          });
        })
        .mockImplementationOnce((_port, callback) => {
          process.nextTick(() => {
            if (callback) callback();
          });
        });

      const service = new LocalServerService();
      await service.initialize();

      expect(mockServer.listen).toHaveBeenCalledTimes(2);
      expect(service.port).toBe(initialPort + 1);
      service.dispose();
    }, 10000);

    it('should reject initialization after max retries', async () => {
      mockServer.listen.mockImplementation((port) => {
        process.nextTick(() => {
          const error: any = new Error(`Port ${port} is already in use`);
          error.code = 'EADDRINUSE';
          serverEmitter.emit('error', error);
        });
      });

      const service = new LocalServerService();
      const promise = service.initialize();
      promise.catch(() => {});
      await expect(promise).rejects.toThrow();

      expect(mockServer.listen).toHaveBeenCalledTimes(20);
      service.dispose();
    }, 22000);

    it('should reject initialization on a generic server error', async () => {
      const genericError = new Error('Something went wrong');
      mockServer.listen.mockImplementation(() => {
        process.nextTick(() => {
          serverEmitter.emit('error', genericError);
        });
      });

      const service = new LocalServerService();
      await expect(service.initialize()).rejects.toThrow(genericError);
      expect(mockServer.listen).toHaveBeenCalledTimes(20);
      service.dispose();
    }, 22000);

    it('should reject initialization if createServer throws a synchronous error', async () => {
      const syncError = new Error('Create server failed');
      mockedHttp.createServer.mockImplementation(() => {
        throw syncError;
      });

      const service = new LocalServerService();
      await expect(service.initialize()).rejects.toThrow(syncError);
      expect(mockedHttp.createServer).toHaveBeenCalledTimes(20);
      service.dispose();
    }, 22000);
  });

  describe('Request Handling', () => {
    let service: LocalServerService;
    let requestListener: (req: any, res: any) => void;
    let mockResponse: {
      writeHead: ReturnType<typeof vi.fn>;
      end: ReturnType<typeof vi.fn>;
    };

    beforeEach(async () => {
      mockServer.listen.mockImplementation((_port, callback) => {
        if (callback) callback();
      });
      service = new LocalServerService();
      await service.initialize();
      requestListener = mockedHttp.createServer.mock.calls[0][0] as (req: any, res: any) => void;
      mockResponse = {
        writeHead: vi.fn(),
        end: vi.fn(),
      };
    });

    it('should fire onRequest event when a request is received', () => {
      const requestSpy = vi.fn();
      service.onRequest(requestSpy);
      const mockRequest = { method: 'GET', url: '/test?a=1', headers: { host: 'localhost' } };
      requestListener(mockRequest, mockResponse);
      expect(requestSpy).toHaveBeenCalledOnce();
    });

    it('should parse request details and fire them in the event', () => {
      const requestSpy = vi.fn();
      service.onRequest(requestSpy);
      const mockRequest = {
        method: 'POST',
        url: '/submit/data?token=123',
        headers: { host: 'localhost', 'content-type': 'application/json' },
        on: vi.fn((event, callback) => {
          if (event === 'data') callback(JSON.stringify({ key: 'value' }));
          if (event === 'end') callback();
        }),
      };
      requestListener(mockRequest, mockResponse);
      expect(requestSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          path: '/submit/data',
          query: { token: '123' },
          body: { key: 'value' },
        }),
      );
    });

    it('should handle non-JSON body gracefully', () => {
      const requestSpy = vi.fn();
      service.onRequest(requestSpy);
      const mockRequest = {
        method: 'POST',
        url: '/submit/bad-data',
        headers: { host: 'localhost', 'content-type': 'application/json' },
        on: vi.fn((event, callback) => {
          if (event === 'data') callback('this is not json');
          if (event === 'end') callback();
        }),
      };
      requestListener(mockRequest, mockResponse);
      expect(requestSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          body: {},
        }),
      );
    });

    it('should handle requests with an undefined url', () => {
      const requestSpy = vi.fn();
      service.onRequest(requestSpy);
      const mockRequest = { method: 'GET', url: undefined, headers: { host: 'localhost' } };
      requestListener(mockRequest, mockResponse);
      expect(requestSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          path: '/',
        }),
      );
    });
  });

  describe('Disposal', () => {
    it('should close the server when disposed', async () => {
      mockServer.listen.mockImplementation((_port, callback) => {
        if (callback) callback();
      });
      const service = new LocalServerService();
      await service.initialize();
      const closeSpy = vi.spyOn(mockServer, 'close');
      service.dispose();
      expect(closeSpy).toHaveBeenCalledOnce();
    });
  });
});
