import type { IDisposable } from '@byted-image/lv-bedrock/dispose';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import { Event } from '@byted-image/lv-bedrock/event';

export interface ILocalServerRequest {
  method?: string;
  path: string;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  body: Record<string, any>;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  query: Record<string, any>;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  headers: Record<string, any>;
}

export interface ILocalServerService extends IDisposable {
  readonly _serviceBrand: undefined;

  onRequest: Event<[ILocalServerRequest]>;

  // server 端口
  port: number;

  initialize(): Promise<void>;
}

export const ILocalServerService = createDecorator<ILocalServerService>('local-server-service');
