import { type NetworkClientInstance, type NetworkRequestConfig, createClient } from '@byted-image/lv-bedrock/network';
import { IAccountService } from '../account/account-service.interface';
import type { INetworkClientFactoryService } from './network-client-factory-service.interface';

const codinApiHost = /capcut-devops.byted.org/;
function isCodinApi(url: string) {
  return codinApiHost.test(url);
}

export class NetworkClientFactoryService implements INetworkClientFactoryService {
  public readonly _serviceBrand: undefined;

  private _ppeEnv: string | undefined = NETWORK_PPE_ENV;

  constructor(@IAccountService private readonly _accountService: IAccountService) {}

  public build(config: NetworkRequestConfig) {
    const instance = createClient(config);

    this._appendAccountJwt(instance);
    this._appendPpeEnv(instance);

    return instance;
  }

  public setPpeEnv(ppeEnv: string | undefined) {
    this._ppeEnv = ppeEnv;
  }

  public getSocketEnv() {
    if (!this._ppeEnv) {
      return;
    }
    return {
      xUseEnv: 'ppe' as const,
      xTTEnv: this._ppeEnv,
    };
  }

  private _appendAccountJwt(instance: NetworkClientInstance) {
    // 如果是codin的后端，统一配置 jwt
    instance.interceptors.request.use((requestConfig) => {
      if (isCodinApi(requestConfig.url ?? '')) {
        const jwt = this._accountService.getJwt();
        if (jwt) {
          requestConfig.headers.set('x-jwt-token', jwt);
        }
      }
      return requestConfig;
    });
  }

  private _appendPpeEnv(instance: NetworkClientInstance) {
    // 如果是codin的后端，统一配置 ppe
    instance.interceptors.request.use((requestConfig) => {
      if (isCodinApi(requestConfig.url ?? '')) {
        if (this._ppeEnv) {
          requestConfig.headers.set('x-use-ppe', '1');
          requestConfig.headers.set('x-tt-env', this._ppeEnv);
        }
      }
      return requestConfig;
    });
  }
}
