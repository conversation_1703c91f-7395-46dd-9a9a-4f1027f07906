import { IAccountService } from '@/common/services/account/account-service.interface';
import { createClient } from '@byted-image/lv-bedrock/network';
import { afterEach, beforeEach, describe, expect, it, vi, type Mock } from 'vitest';
import { NetworkClientFactoryService } from './network-client-factory-service';

// Mock the external dependency
vi.mock('@byted-image/lv-bedrock/network', () => ({
  createClient: vi.fn(() => ({
    interceptors: {
      request: {
        use: vi.fn(),
      },
    },
  })),
}));

describe('NetworkClientFactoryService', () => {
  let service: NetworkClientFactoryService;
  let mockAccountService: IAccountService;
  const mockJwt = 'test-jwt-token';
  const mockPpeEnv = 'test-ppe-env';

  beforeEach(() => {
    // Stub the global variable used in the service
    vi.stubGlobal('NETWORK_PPE_ENV', undefined);

    mockAccountService = {
      _serviceBrand: undefined,
      initialize: vi.fn(),
      hasLogin: true,
      onDidLogin: vi.fn(),
      onDidLogout: vi.fn(),
      getJwt: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
      getUserInfo: vi.fn(),
    };
    service = new NetworkClientFactoryService(mockAccountService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should build a network client instance and append interceptors', () => {
    const mockInstance = {
      interceptors: {
        request: {
          use: vi.fn(),
        },
      },
    };
    (createClient as Mock).mockReturnValue(mockInstance);

    const instance = service.build({});
    expect(createClient).toHaveBeenCalledWith({});
    expect(instance).toBe(mockInstance);
    // Should append two interceptors: one for JWT, one for PPE
    expect(mockInstance.interceptors.request.use).toHaveBeenCalledTimes(2);
  });

  it('should set and get PPE environment correctly', () => {
    expect(service.getSocketEnv()).toBeUndefined();
    service.setPpeEnv(mockPpeEnv);
    expect(service.getSocketEnv()).toEqual({
      xUseEnv: 'ppe',
      xTTEnv: mockPpeEnv,
    });
    service.setPpeEnv(undefined);
    expect(service.getSocketEnv()).toBeUndefined();
  });

  describe('Interceptors Logic', () => {
    let requestInterceptors: ((config: any) => any)[];

    beforeEach(() => {
      requestInterceptors = [];
      const mockInstance = {
        interceptors: {
          request: {
            // Capture all interceptor functions
            use: vi.fn((fn) => {
              requestInterceptors.push(fn);
            }),
          },
        },
      };
      (createClient as Mock).mockReturnValue(mockInstance);
    });

    it('should add JWT token for Codin API requests', () => {
      (mockAccountService.getJwt as Mock).mockReturnValue(mockJwt);
      service.build({}); // This sets up the interceptors
      const jwtInterceptor = requestInterceptors[0];

      const config = {
        url: 'https://capcut-devops.byted.org/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = jwtInterceptor(config);

      expect(newConfig.headers.get('x-jwt-token')).toBe(mockJwt);
    });

    it('should not add JWT token if not available', () => {
      (mockAccountService.getJwt as Mock).mockReturnValue(undefined);
      service.build({});
      const jwtInterceptor = requestInterceptors[0];

      const config = {
        url: 'https://capcut-devops.byted.org/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = jwtInterceptor(config);

      expect(newConfig.headers.has('x-jwt-token')).toBe(false);
    });

    it('should not add JWT token for non-Codin API requests', () => {
      (mockAccountService.getJwt as Mock).mockReturnValue(mockJwt);
      service.build({});
      const jwtInterceptor = requestInterceptors[0];

      const config = {
        url: 'https://other-api.com/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = jwtInterceptor(config);

      expect(newConfig.headers.has('x-jwt-token')).toBe(false);
    });

    it('should add PPE headers for Codin API requests when PPE env is set', () => {
      service.setPpeEnv(mockPpeEnv);
      service.build({}); // Build to register interceptors
      const ppeInterceptor = requestInterceptors[1];

      const config = {
        url: 'https://capcut-devops.byted.org/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = ppeInterceptor(config);

      expect(newConfig.headers.get('x-use-ppe')).toBe('1');
      expect(newConfig.headers.get('x-tt-env')).toBe(mockPpeEnv);
    });

    it('should not add PPE headers when PPE env is not set', () => {
      service.setPpeEnv(undefined);
      service.build({});
      const ppeInterceptor = requestInterceptors[1];

      const config = {
        url: 'https://capcut-devops.byted.org/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = ppeInterceptor(config);

      expect(newConfig.headers.has('x-use-ppe')).toBe(false);
      expect(newConfig.headers.has('x-tt-env')).toBe(false);
    });

    it('should add PPE headers for Codin API requests when PPE env is set', () => {
      // Need to re-setup interceptors to test the second one
      const interceptors: ((config: any) => any)[] = [];
      const mockInstance = {
        interceptors: {
          request: {
            use: vi.fn((fn) => interceptors.push(fn)),
          },
        },
      };
      (createClient as Mock).mockReturnValue(mockInstance);

      service.setPpeEnv(mockPpeEnv);
      service.build({}); // Build to register interceptors
      const ppeInterceptor = interceptors[1];

      const config = {
        url: 'https://capcut-devops.byted.org/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = ppeInterceptor(config);

      expect(newConfig.headers.get('x-use-ppe')).toBe('1');
      expect(newConfig.headers.get('x-tt-env')).toBe(mockPpeEnv);
    });

    it('should not add PPE headers when PPE env is not set', () => {
      const interceptors: ((config: any) => any)[] = [];
      const mockInstance = {
        interceptors: {
          request: {
            use: vi.fn((fn) => interceptors.push(fn)),
          },
        },
      };
      (createClient as Mock).mockReturnValue(mockInstance);

      service.setPpeEnv(undefined);
      service.build({});
      const ppeInterceptor = interceptors[1];

      const config = {
        url: 'https://capcut-devops.byted.org/api/v1/some-endpoint',
        headers: new Map(),
      };
      const newConfig = ppeInterceptor(config);

      expect(newConfig.headers.has('x-use-ppe')).toBe(false);
      expect(newConfig.headers.has('x-tt-env')).toBe(false);
    });
  });
});
