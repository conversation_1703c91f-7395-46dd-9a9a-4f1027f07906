import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { NetworkClientInstance, NetworkRequestConfig } from '@byted-image/lv-bedrock/network';

export interface INetworkClientFactoryService {
  _serviceBrand: undefined;

  build(config: NetworkRequestConfig): NetworkClientInstance;

  setPpeEnv(ppeEnv: string | undefined): void;

  getSocketEnv():
    | {
        xUseEnv: 'ppe' | 'boe';
        xTTEnv: string;
      }
    | undefined;
}

export const INetworkClientFactoryService = createDecorator<INetworkClientFactoryService>('network-client-factory');
