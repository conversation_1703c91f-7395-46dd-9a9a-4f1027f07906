import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { RpcService } from './rpc-service';
import type * as vscode from 'vscode';
import { RpcRequest } from './types';

describe('RpcService', () => {
  let rpcService: RpcService;
  let mockWebview: vscode.Webview;

  beforeEach(() => {
    rpcService = new RpcService();
    mockWebview = {
      onDidReceiveMessage: vi.fn(),
      postMessage: vi.fn(),
      asWebviewUri: vi.fn(),
      cspSource: '',
      html: '',
      options: {},
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should not resolve initialize promise before setWebview is called', async () => {
      const initPromise = rpcService.initialize();
      let isResolved = false;
      initPromise.then(() => {
        isResolved = true;
      });
      await new Promise((resolve) => setTimeout(resolve, 0)); // wait for microtasks
      expect(isResolved).toBe(false);
    });

    it('should resolve initialize promise after setWebview is called', async () => {
      const initPromise = rpcService.initialize();
      rpcService.setWebview(mockWebview);
      await expect(initPromise).resolves.toBeUndefined();
    });

    it('should register message listener when setWebview is called', () => {
      rpcService.setWebview(mockWebview);
      expect(mockWebview.onDidReceiveMessage).toHaveBeenCalledOnce();
    });
  });

  describe('Handler Registration', () => {
    it('should register a handler for a command', async () => {
      const handler = vi.fn().mockResolvedValue('test result');
      rpcService.register('testCmd', handler);

      // Simulate receiving a message
      rpcService.setWebview(mockWebview);
      const messageCallback = (mockWebview.onDidReceiveMessage as any).mock.calls[0][0];
      const request: RpcRequest = { type: 'request', id: '1', cmd: 'testCmd', params: { data: 'test' } };

      await messageCallback(request);

      expect(handler).toHaveBeenCalledWith({ data: 'test' });
      expect(mockWebview.postMessage).toHaveBeenCalledWith({
        type: 'response',
        id: '1',
        data: 'test result',
      });
    });

    it('should warn when a handler is overwritten', () => {
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const handler1 = vi.fn();
      const handler2 = vi.fn();

      rpcService.register('testCmd', handler1);
      rpcService.register('testCmd', handler2);

      expect(consoleWarnSpy).toHaveBeenCalledWith('RPC handler for command "testCmd" is being overwritten.');
      consoleWarnSpy.mockRestore();
    });
  });

  describe('Message Handling', () => {
    beforeEach(() => {
      rpcService.setWebview(mockWebview);
    });

    it('should handle request messages', async () => {
      const handler = vi.fn().mockResolvedValue('success');
      rpcService.register('doWork', handler);
      const messageCallback = (mockWebview.onDidReceiveMessage as any).mock.calls[0][0];
      const request: RpcRequest = { type: 'request', id: '2', cmd: 'doWork', params: {} };

      await messageCallback(request);

      expect(handler).toHaveBeenCalled();
      expect(mockWebview.postMessage).toHaveBeenCalled();
    });

    it('should ignore non-request messages', async () => {
      const messageCallback = (mockWebview.onDidReceiveMessage as any).mock.calls[0][0];
      const notification = { type: 'notification', event: 'someEvent' };

      await messageCallback(notification);

      expect(mockWebview.postMessage).not.toHaveBeenCalled();
    });

    it('should respond with an error if no handler is found', async () => {
      const messageCallback = (mockWebview.onDidReceiveMessage as any).mock.calls[0][0];
      const request: RpcRequest = { type: 'request', id: '3', cmd: 'nonExistent', params: {} };

      await messageCallback(request);

      expect(mockWebview.postMessage).toHaveBeenCalledWith({
        type: 'response',
        id: '3',
        error: 'No handler found for command: nonExistent',
      });
    });

    it('should respond with an error if handler throws an exception', async () => {
      const error = new Error('Handler failed');
      const handler = vi.fn().mockRejectedValue(error);
      rpcService.register('failCmd', handler);
      const messageCallback = (mockWebview.onDidReceiveMessage as any).mock.calls[0][0];
      const request: RpcRequest = { type: 'request', id: '4', cmd: 'failCmd', params: {} };

      await messageCallback(request);

      expect(mockWebview.postMessage).toHaveBeenCalledWith({
        type: 'response',
        id: '4',
        error: 'Handler failed',
      });
    });
  });

  describe('Notifications', () => {
    it('should send a notification to the webview', () => {
      rpcService.setWebview(mockWebview);
      const event = 'testEvent';
      const params = { info: 'data' };

      rpcService.notify(event, params);

      expect(mockWebview.postMessage).toHaveBeenCalledWith({
        type: 'notification',
        event,
        params,
      });
    });
  });

  describe('Disposal', () => {
    it('should dispose all disposables and clear handlers', () => {
      const disposable = { dispose: vi.fn() };
      (rpcService as any)._disposables.push(disposable);
      rpcService.register('cmd', vi.fn());

      expect((rpcService as any)._handlers.size).toBe(1);

      rpcService.dispose();

      expect(disposable.dispose).toHaveBeenCalledOnce();
      expect((rpcService as any)._handlers.size).toBe(0);
    });
  });
});
