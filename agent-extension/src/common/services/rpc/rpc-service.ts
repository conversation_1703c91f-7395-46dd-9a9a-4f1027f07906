/**
 * @file RpcService.ts
 * @description RPC 的服务端实现，旨在运行于 VSCode Extension host。
 * 监听来自 Webview 的请求，将其分发给已注册的处理器，并返回响应。
 */

import { defer } from '@byted-image/lv-bedrock/promise';
import * as vscode from 'vscode';
import { RpcRequest, RpcResponse, RpcNotification, RpcMessagePacket } from './types';
import { IRpcService } from './rpc-service.interface';

type Handler<P, R> = (params: P) => Promise<R> | R;

/**
 * 用于处理来自客户端（如 Webview）的 RPC 调用的服务。
 * @template T - 定义可用命令的 RPC 合同。
 */
export class RpcService implements IRpcService {
  public readonly _serviceBrand: undefined;

  private _handlers = new Map<string, Handler<any, any>>();
  private _webview!: vscode.Webview;
  private _disposables: vscode.Disposable[] = [];
  private _initDefer = defer<void>();

  public initialize(): Promise<void> {
    return this._initDefer.promise;
  }

  public setWebview(webview: vscode.Webview): void {
    this._webview = webview;
    this._webview.onDidReceiveMessage(this._handleMessage.bind(this), null, this._disposables);
    this._initDefer.resolve();
  }

  /**
   * 为指定命令注册处理器。
   * @param cmd - 要注册的命令。
   * @param handler - 当调用该命令时要执行的函数。
   */
  public register<P = any, R = any>(cmd: string, handler: Handler<P, R>): void {
    if (this._handlers.has(String(cmd))) {
      console.warn(`RPC handler for command "${String(cmd)}" is being overwritten.`);
    }
    this._handlers.set(String(cmd), handler);
  }

  /**
   * 向客户端发送单向通知。
   * @param event - 事件名称。
   * @param params - 事件的负载数据。
   */
  public notify<E extends string>(event: E, params: any): void {
    const notification: RpcNotification = {
      type: 'notification',
      event,
      params,
    };
    this._webview.postMessage(notification);
  }

  /**
   * 释放消息监听器。
   */
  public dispose(): void {
    this._disposables.forEach((d) => d.dispose());
    this._handlers.clear();
  }

  /**
   * 处理来自客户端的消息。
   * @param message - 从 webview 接收到的消息。
   */
  private async _handleMessage(message: RpcMessagePacket): Promise<void> {
    if (message.type === 'request') {
      await this._handleRequest(message as RpcRequest);
    }
  }

  /**
   * 处理来自客户端的请求消息。
   * @param request - RPC 请求消息。
   */
  private async _handleRequest(request: RpcRequest): Promise<void> {
    const { id, cmd, params } = request;
    const handler = this._handlers.get(cmd);

    if (!handler) {
      const errorResponse: RpcResponse = {
        type: 'response',
        id,
        error: `No handler found for command: ${cmd}`,
      };
      this._webview.postMessage(errorResponse);
      return;
    }

    try {
      const result = await Promise.resolve(handler(params));
      const response: RpcResponse = {
        type: 'response',
        id,
        data: result,
      };
      this._webview.postMessage(response);
    } catch (e: any) {
      const errorResponse: RpcResponse = {
        type: 'response',
        id,
        error: e.message || String(e),
      };
      this._webview.postMessage(errorResponse);
    }
  }
}
