/**
 * @file types.ts
 * @description Defines the core types and contracts for RPC communication
 * between the VSCode Extension and the Webview.
 */

/**
 * Represents a generic command with parameters and a result.
 * Used to build the RPC contract.
 */
export type RpcCommand = {
  params?: any;
  result?: any;
};

/**
 * Defines the contract for all available RPC commands.
 * This interface should be extended with specific command definitions.
 *
 * @example
 * ```ts
 * interface MyRpcContract extends RpcContract {
 *   'module.command': {
 *     params: { arg1: string };
 *     result: { success: boolean };
 *   };
 * }
 * ```
 */
export interface RpcContract {
  [command: string]: RpcCommand;
}

/**
 * The basic structure for any message sent between Extension and Webview.
 */
export interface RpcMessage {
  /**
   * Uniquely identifies the message, used for correlating requests and responses.
   */
  id: string;
}

/**
 * Represents a request message sent from the client to the service.
 */
export interface RpcRequest extends RpcMessage {
  type: 'request';
  /**
   * The command to be executed, e.g., 'module.command'.
   */
  cmd: string;
  /**
   * The parameters for the command.
   */
  params: any;
}

/**
 * Represents a response message sent from the service back to the client.
 */
export interface RpcResponse extends RpcMessage {
  type: 'response';
  /**
   * The data returned by the command execution.
   */
  data?: any;
  /**
   * An error object if the command execution failed.
   */
  error?: any;
}

/**
 * Represents a notification message sent one-way, typically from service to client.
 */
export interface RpcNotification {
  type: 'notification';
  /**
   * The name of the event being notified.
   */
  event: string;
  /**
   * The payload of the notification.
   */
  params: any;
}

/**
 * A union type representing all possible message types.
 */
export type RpcMessagePacket = RpcRequest | RpcResponse | RpcNotification;

/**
 * Extracts the parameter type for a given command from the RPC contract.
 */
export type Params<T extends RpcContract, K extends keyof T> = T[K]['params'];

/**
 * Extracts the result type for a given command from the RPC contract.
 */
export type Result<T extends RpcContract, K extends keyof T> = T[K]['result'];

/**
 * Options for an RPC call.
 */
export interface RpcCallOptions {
  /**
   * Timeout for the call in milliseconds.
   * If not provided, a default timeout will be used.
   */
  timeout?: number;
}
