import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { Webview } from 'vscode';

export interface IRpcService {
  _serviceBrand: undefined;

  initialize: () => Promise<void>;

  register: (command: string, callback: (params: any) => void) => void;

  notify: (command: string, params: any) => void;

  setWebview: (webview: Webview) => void;

  dispose: () => void;
}

export const IRpcService = createDecorator<IRpcService>('rpc-service');
