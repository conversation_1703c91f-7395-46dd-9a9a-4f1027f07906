import { format } from 'date-fns';
import { ILoggerService, ILoggerFactoryService } from './logger.interface';

/**
 * ILogger 的一个具体实现，它会将一个前缀添加到所有日志消息中。
 */
class LoggerService implements ILoggerService {
  constructor(private readonly prefix: string) {}

  private _formatMessage(type: 'INFO' | 'WARN' | 'ERROR') {
    const timestamp = format(new Date(), 'HH:mm:ss.SSS');
    return `${timestamp} [${this.prefix}][${type}]`;
  }

  public info(...args: string[]): void {
    console.log(this._formatMessage('INFO'), ...args);
  }

  public warn(...args: string[]): void {
    console.warn(this._formatMessage('WARN'), ...args);
  }

  public error(msg: string, error: Error | unknown, ...args: string[]): void {
    console.error(this._formatMessage('ERROR'), msg, error, ...args);
  }
}

/**
 * 将在 DI 容器中注册为单例的工厂实现。
 */
export class LoggerFactoryService implements ILoggerFactoryService {
  public _serviceBrand: undefined;

  public create(prefix: string): ILoggerService {
    return new LoggerService(prefix);
  }
}
