import { createDecorator } from '@byted-image/lv-bedrock/di';

export interface ILoggerService {
  info: (...args: any[]) => void;

  warn: (...args: any[]) => void;

  error: (msg: string, error: Error | unknown, ...args: any[]) => void;
}

export interface ILoggerFactoryService {
  _serviceBrand: undefined;

  /**
   * 创建一个带有特定前缀的新的日志记录器实例。
   * @param prefix 日志记录器的前缀, 例如 'CodesearchChatService'.
   */
  create(prefix: string): ILoggerService;
}

export const ILoggerFactoryService = createDecorator<ILoggerFactoryService>('logger-factory-service');
