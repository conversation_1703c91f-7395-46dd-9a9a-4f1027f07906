import type { Int64, UserInput } from '@/bam/namespaces/userinput';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';

export enum AskMessageRole {
  User = 0,
  Tool = 1,
}

export interface UserMessage {
  cid?: string;
  role: AskMessageRole.User;
  userContent: UserInput;
}

export interface ToolMessage {
  cid?: string;
  role: AskMessageRole.Tool;
  toolsData: {
    content: string;
    requestId: string;
  }[];
}

export type AskMessage = UserMessage | ToolMessage;

export interface IConversationContextState {
  cid: string;
  parentCid?: string;
  parentMessageVersion?: Int64;
  messages: ClientMessage[];
  childrenContexts: IConversationContextState[];
}

export interface IMessageChangeInfo {
  messages: ClientMessage[];
  type: 'add' | 'update';
  cid: string;
  parentCid?: string;
  parentMessageVersion?: Int64;
}
