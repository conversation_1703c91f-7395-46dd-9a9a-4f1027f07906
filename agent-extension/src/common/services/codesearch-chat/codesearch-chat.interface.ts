import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import { Event } from '@byted-image/lv-bedrock/event';
import { AskMessage, type IConversationContextState, type IMessageChangeInfo } from '../base-chat/types';
import { ModelType } from '@/common/constants/model-types';
import type { Int64 } from '@/bam';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import type { BaseConversationContext } from '../conversation/base-conversation-context/base-conversation-context';
import type { CodeSearchConversationContext } from '../conversation/codesearch-conversation-context/codesearch-conversation-context';

export const DefaultModelType = ModelType.Seed1_6;

export interface ICodesearchChatService {
  _serviceBrand: undefined;

  currentCid: string;

  getMessages(): ClientMessage[];

  addMessageListener(context: BaseConversationContext): void;

  // 发送消息
  sendMessage(message: AskMessage): void;

  resetMessageReceiverAndSocket(): void;

  switchCurrentConversation(cid?: string): Promise<ILvErrorOr<string>>;

  onSendMessageError: Event<ILvErrorOr<void>[]>;

  onConversationMessageChange: Event<[IMessageChangeInfo]>;

  onPresentAssistantMessage: Event<[]>;

  createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>>;

  getCurrentContextState(): Promise<IConversationContextState>;

  getConversationContext(cid: string): CodeSearchConversationContext | null;
}

export const ICodesearchChatService = createDecorator<ICodesearchChatService>('codesearch-chat-service');
