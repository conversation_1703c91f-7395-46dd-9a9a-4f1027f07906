# BaseConversationService

`BaseConversationService` 是一个抽象类，主要用于管理会话（Conversation）的生命周期和消息流转。下面分别介绍其 public 方法和 before/after 生命周期相关方法。

---

## 一、public 方法

1. **get id()**
   - 获取当前会话 id。

2. **get parentId()**
   - 获取当前会话的父 id。

3. **getMessages()**
   - 返回当前会话的所有消息数组。

4. **getMessageById(id: string)**
   - 根据消息 id 查找并返回对应的消息。

5. **appendMessages(messages: ClientMessage[])**
   - 向会话中添加一组消息，并触发相关事件和自动保存。

6. **updateMessage(message: ClientMessage)**
   - 更新指定 id 的消息内容，并自动保存。

7. **presentAssistantMessage()**
   - 触发 assistant 消息展示事件。

8. **changeConversationStatus(status: ConversationStatus)**
   - 更改会话状态（如 idle/processing），并触发状态变更事件。

9. **getConversationStatus()**
   - 获取当前会话状态。

10. **switchConversation(cid: string)**
    - 切换到指定 id 的会话，自动保存当前会话并加载新会话内容。

11. **reset()**
    - 重置会话状态（清空 id、父 id、消息等），并保存当前会话。

12. **createConversation(options?: { parentConversationId: string; parentMessageVersion: Int64 })**
    - 创建新会话（可指定父会话），并切换到新会话。

13. **save()**
    - 手动保存当前会话到历史记录。

---

## 二、before/after 生命周期方法

这些方法大多为 protected，主要用于在关键操作前后做扩展或钩子处理，便于子类重写。

### 1. 切换会话相关

- **protected async _beforeSwitchConversation(cid)**
  - 切换会话前调用。会判断当前会话是否有新消息，有则自动保存。

- **protected _afterSwitchConversation(cid)**
  - 切换会话后调用。默认什么都不做，子类可扩展。

### 2. 重置会话相关

- **protected _beforeReset()**
  - 重置会话前调用。会自动保存当前会话。

- **protected _afterReset()**
  - 重置会话后调用。默认什么都不做。

### 3. 创建会话相关

- **protected async _beforeCreateConversation()**
  - 创建新会话前调用。会自动保存当前会话。

- **protected _afterCreateConversation(cid, parentCid?)**
  - 创建新会话后调用。默认什么都不做。

---

## 总结

- public 方法主要用于会话的增删查改、状态切换、保存等操作。
- before/after 生命周期方法为关键操作（切换、重置、创建）前后提供扩展点，便于子类自定义行为，核心如自动保存、日志、校验等。
