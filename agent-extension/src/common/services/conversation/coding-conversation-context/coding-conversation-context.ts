import { base, type Int64 } from '@/bam';
import type { AgentInferenceParams } from '@/bam/namespaces/agentserver';
import type { RetryCodeAgentRequest } from '@/bam/namespaces/agw';
import { ModelType } from '@/bam/namespaces/base';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { getSystemEnv } from '@/common/env/system';
import { type ClientMessage, ClientMessageType } from '@/conversation/client-message/abstract-message';
import type { ClientToolMessage } from '@/conversation/client-message/tool-message';
import { AskUserTool } from '@/tools/ask-user/ask-user';
import type { Tool } from '@/tools/base';
import { ExecuteCommandTool } from '@/tools/execute-command';
import { FetchRuleTool } from '@/tools/fetch-rule';
import { GetErrorsTool } from '@/tools/get-errors';
import { GrepSearchTool } from '@/tools/grep-search';
import { ListDirectoryTool } from '@/tools/list-directory';
import { ReadFileTool } from '@/tools/read-file';
import { ReadPlanTool } from '@/tools/read-plan/read-plan';
import { ReplaceFileTool } from '@/tools/replace-file';
import { TechnicalPlanningTool } from '@/tools/technical-planning/technical-planning';
import { TypeCheckTool } from '@/tools/type-check';
import { UnderstandRequirementTool } from '@/tools/understand-requirement';
import { WriteFileTool } from '@/tools/write-file';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type ILvErrorOr, makeError, makeErrorBy, makeOk } from '@byted-image/lv-bedrock/error';
import { Emitter } from '@byted-image/lv-bedrock/event';
import * as vscode from 'vscode';
import { ChatErrorCode } from '../../base-chat/chat-error';
import { type AskMessage, AskMessageRole } from '../../base-chat/types';
import { DefaultModelType } from '../../coding-chat/coding-chat-service.interface';
import { D2C_SETTINGS_STORAGE_KEY } from '../../d2c-chat/settings';
import { IFileLoggerService } from '../../file-logger/file-logger-service.interface';
import { IMcpHubService } from '../../mcp-hub/mcp-hub-service.interface';
import { INetworkClientFactoryService } from '../../network-client-factory/network-client-factory-service.interface';
import { IRulesService, RuleType } from '../../rules/rules-service.interface';
import { ISocketService } from '../../socket/socket-service.interface';
import { IStorageService } from '../../storage/storage-service.interface';
import { IWorkspaceService } from '../../workspace/workspace.interface';
import { BaseConversationContext } from '../base-conversation-context/base-conversation-context';
import { AgentId } from '../const';
import type { ClientMultiPartMessage } from '@/conversation/client-message/multi-part-message';
import { CODING_MODEL_KEY } from '@/common/constants/storage';

export class CodingConversationContext extends BaseConversationContext {
  protected readonly _conversationType = ConversationTypeEnum.Coding;
  protected readonly _agentType = AgentId.Coding;
  public readonly _onSendMessageError = new Emitter<ILvErrorOr<void>[]>();

  private _settings: any = {};

  get onSendMessageError() {
    return this._onSendMessageError.event;
  }

  get messages() {
    return this._messagesManager.getMessages();
  }

  constructor(
    options: { cid: string; parentCid?: string; parentMessageVersion?: Int64 },
    initMessages: ClientMessage[],
    @IInstantiationService _instantiationService: IInstantiationService,
    @ISocketService _socketService: ISocketService,
    @IStorageService protected readonly _storageService: IStorageService,
    @IMcpHubService private readonly _mcpHubService: IMcpHubService,
    @IRulesService private readonly _rulesService: IRulesService,
    @INetworkClientFactoryService _networkClientFactoryService: INetworkClientFactoryService,
    @IWorkspaceService private readonly _workspaceService: IWorkspaceService,
    @IFileLoggerService
    protected readonly _fileLoggerService: IFileLoggerService,
  ) {
    super(
      options,
      initMessages,
      _instantiationService,
      _socketService,
      _networkClientFactoryService,
      _storageService,
      _fileLoggerService,
    );
  }

  public async bootstrap(): Promise<void> {
    await super.bootstrap();
    this._messagesManager.onAppendMessages((messages) => {
      const toolMessages = messages.filter((message) => message.type === ClientMessageType.Tool);
      this._handleToolMessages(toolMessages as ClientToolMessage[]);
    });
    const settings = await this._storageService.get(D2C_SETTINGS_STORAGE_KEY);
    if (!settings) {
      return;
    }
    this._settings = JSON.parse(settings);
  }

  async sendMessage(message: AskMessage) {
    // 应该立即展示用户消息
    let userMsgIns: ClientMultiPartMessage | null = null;
    if (message.role === AskMessageRole.User) {
      userMsgIns = this.sendUserMessage(message);
    }
    this._fileLoggerService.info(`${this._conversationType} sendMessage start`);
    const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);
    const repoInfos = Array.from(this._workspaceService.getAllRepoInfo().values());

    const modelType = await this._getCurrentModel();
    const business = await this.getBusiness();
    const askParams = {
      params: {
        id: this._options.cid,
        client_env: {
          system_env: getSystemEnv(repoInfos),
          tools: (await this._getTools()).filter((tool) => tool.enable()).map((tool) => tool.getInfo()),
          project_rules: alwaysRulesContent,
          figma_token: this._settings.figma_token,
          repo_infos: repoInfos.map((info) => ({
            branch: info.branch,
            path: info.path,
            repo_name: info.repoName,
            path_list: this._workspaceService.getBusinessPathList(info.repoName, business),
          })),
          did: this._workspaceService.getDid(),
          work_mode: await this.getMode(),
        },
        model_type: modelType,
      } as AgentInferenceParams,
    };
    if (message.role === AskMessageRole.User) {
      askParams.params.user_message = {
        content: message.userContent,
      };
    } else {
      askParams.params.tool_messages = message.toolsData.map((data) => ({
        content: data.content,
        request_id: data.requestId,
      }));
    }

    console.log('coding askParams', askParams);

    try {
      this._fileLoggerService.info(`${this._conversationType} sendMessage request: ${JSON.stringify(askParams)}`);
      const resp = await this._networkClient('https://capcut-devops.byted.org/conversation/code', {
        method: 'POST',
        data: JSON.stringify(askParams),
      });

      if (resp.data.code !== 0) {
        vscode.window.showErrorMessage(`send message error: [${resp.data.code}]-${resp.data.message}`);
        const sendMessageError = makeError(
          ChatErrorCode.SendMessageFailed,
          `send message failed: ${resp.data.message}`,
        );
        this._onSendMessageError.fire(sendMessageError);
        this._fileLoggerService.error(`${this._conversationType} sendMessage error: ${JSON.stringify(resp)}`);
        return sendMessageError;
      }
      if (userMsgIns) {
        userMsgIns.logID = resp.headers['x-tt-logid'];
        this.messagesManager.updateMessage(userMsgIns);
      }
    } catch (e) {
      console.error('send message error: ', e);
      const sendMessageError = makeErrorBy(ChatErrorCode.SendMessageFailed, 'send message failed', e as Error);
      this._onSendMessageError.fire(sendMessageError);
      this._fileLoggerService.error(`${this._conversationType} sendMessage error: ${JSON.stringify(e)}`);
      return sendMessageError;
    }
    return makeOk();
  }

  protected async _getModelQueue(): Promise<ModelType[]> {
    const defaultModel = await this._getDefaultModelType();
    return [defaultModel, ModelType.Claude, ModelType.Kimi_K2, ModelType.DeepSeek];
  }

  private async _getDefaultModelType() {
    try {
      const modelSetting = await this._storageService.get(CODING_MODEL_KEY);
      if (!modelSetting) {
        return DefaultModelType;
      }
      const modelType = Number(JSON.parse(modelSetting).modelType ?? DefaultModelType);
      return modelType;
    } catch {
      return DefaultModelType;
    }
  }

  private async _getTools(): Promise<Tool[]> {
    const mode = await this.getMode();

    if (mode === base.AgentWorkMode.PlanAct) {
      return [
        new ExecuteCommandTool(),
        new GetErrorsTool(),
        new TypeCheckTool(),
        this._instantiationService.createInstance(ListDirectoryTool),
        this._instantiationService.createInstance(ReadFileTool),
        this._instantiationService.createInstance(GrepSearchTool),
        this._instantiationService.createInstance(WriteFileTool),
        this._instantiationService.createInstance(ReplaceFileTool),
        this._instantiationService.createInstance(FetchRuleTool),
        this._instantiationService.createInstance(AskUserTool),
        this._instantiationService.createInstance(UnderstandRequirementTool),
        this._instantiationService.createInstance(TechnicalPlanningTool),
        this._instantiationService.createInstance(ReadPlanTool),
        // TODO 等codesearch本地完全支持后再打开
        // this._instantiationService.createInstance(CodeSearchTool),
        ...this._mcpHubService.formatToToolInfos(),
      ];
    }

    return [
      new ExecuteCommandTool(),
      new GetErrorsTool(),
      this._instantiationService.createInstance(ListDirectoryTool),
      this._instantiationService.createInstance(ReadFileTool),
      this._instantiationService.createInstance(GrepSearchTool),
      this._instantiationService.createInstance(WriteFileTool),
      this._instantiationService.createInstance(ReplaceFileTool),
      this._instantiationService.createInstance(FetchRuleTool),
      ...this._mcpHubService.formatToToolInfos(),
    ];
  }

  private async _handleToolMessages(messages: ClientToolMessage[]) {
    this._fileLoggerService.info(`${this._conversationType} _handleToolMessages ${JSON.stringify(messages)}`);
    const clientToolRunners: Promise<{ content: string; requestId: string; name: string }>[] = [];

    for (const toolMessage of messages) {
      const tool = (await this._getTools()).find((tool) => tool.getInfo().name === toolMessage.name);
      if (!tool) continue;

      const runner = async () => {
        // 创建进度回调来实时更新工具消息
        const progressCallback = {
          updateProgress: (progress: number, message?: string) => {
            // 发出工具进度事件
            this._messagesManager.fireToolProgress({
              toolId: toolMessage.id,
              toolName: toolMessage.name,
              progress,
              message,
            });

            console.log(`Tool ${toolMessage.name} progress: ${progress}%`, message);
          },
          updateOutput: (partialOutput: string) => {
            // 实时更新工具消息的输出
            const currentOutput = toolMessage.output || '';
            toolMessage.output = currentOutput + partialOutput;
          },
        };

        const result = await tool.run(
          toolMessage.input ?? '',
          toolMessage.id,
          this._options.cid,
          toolMessage.version,
          progressCallback,
        );
        return {
          content: result.value,
          requestId: toolMessage.id,
          name: tool.getInfo().name,
        };
      };

      clientToolRunners.push(runner());
    }

    if (clientToolRunners.length === 0) return;

    const toolsData = await Promise.all(clientToolRunners);
    for (const tool of toolsData) {
      this._fileLoggerService.info(`${this._conversationType} _handleToolMessages toolsData ${JSON.stringify(tool)}`);
    }

    this.sendMessage({
      cid: this._options.cid,
      role: AskMessageRole.Tool,
      toolsData,
    });
  }

  protected async _retryWhenMessageError(): Promise<void> {
    try {
      this._fileLoggerService.info(`${this._conversationType} retryWhenMessageError start`);
      const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);
      const retryParams = {
        params: {
          id: this._options.cid,
          model_type: await this._getCurrentModel(),
          client_env: {
            system_env: getSystemEnv(),
            tools: (await this._getTools()).filter((tool) => tool.enable()).map((tool) => tool.getInfo()),
            project_rules: alwaysRulesContent,
            figma_token: this._settings.figma_token,
          },
        },
      } as RetryCodeAgentRequest;

      const resp = await this._networkClient('https://capcut-devops.byted.org/conversation/retry-code', {
        method: 'POST',
        data: JSON.stringify(retryParams),
      });
      this._fileLoggerService.info(`${this._conversationType} retryWhenMessageError response: ${JSON.stringify(resp)}`);
      if (resp.data.code !== 0) {
        vscode.window.showErrorMessage(`retry message error: [${resp.data.code}]-${resp.data.message}`);
        console.log('retry message error: ', resp);
        const sendMessageError = makeError(
          ChatErrorCode.SendMessageFailed,
          `retry message failed: ${resp.data.message}`,
        );
        this._onSendMessageError.fire(sendMessageError);
      }
    } catch (e) {
      console.error('retry message error: ', e);
      this._fileLoggerService.error(`${this._conversationType} retryWhenMessageError error: ${JSON.stringify(e)}`);
      const sendMessageError = makeErrorBy(ChatErrorCode.SendMessageFailed, 'retry message failed', e as Error);
      this._onSendMessageError.fire(sendMessageError);
    }
  }
}
