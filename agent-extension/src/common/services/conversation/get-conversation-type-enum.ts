import { ConversationType } from '@/bam/namespaces/conversation';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';

export function getConversationTypeEnum(conversationType: ConversationType): ConversationTypeEnum {
  switch (conversationType) {
    case ConversationType.Understanding:
      return ConversationTypeEnum.Understanding;
    case ConversationType.Arch:
      return ConversationTypeEnum.TechDocs;
    case ConversationType.Coding:
      return ConversationTypeEnum.Coding;
    case ConversationType.D2c:
      return ConversationTypeEnum.D2c;
    default:
      // TODO: chezongshao 类型体操暂时不想做了
      return conversationType as any;
  }
}
