import { type ILvErrorOr, makeErrorBy, makeOkWith } from '@byted-image/lv-bedrock/error';
import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import type { Memento } from 'vscode';

import type { Int64 } from '@/bam';
import { Message as SchemaMessage, type SubConversation } from '@/bam/namespaces/message';
import { IExtensionContextService } from '@/common/services/commands/extension-context.interface';
import { BaseJsonMessage, type ClientMessage } from '@/conversation/client-message/abstract-message';
import { MessageSerializer } from '@/conversation/message-serializer';
import { INetworkClientFactoryService } from '@/common/services/network-client-factory/network-client-factory-service.interface';
import {
  EGetConversationError,
  type ConversationChangeEvent,
  type ConversationDetail,
  type IConversationRelation,
  type ConversationSummary,
  IConversationHistoryService,
  type PaginatedConversations,
} from './conversation-history.interface';
import { CONVERSATIONS_KEY, CONVERSATION_RELATION_KEY, MESSAGE_KEY_PREFIX } from './constants';
import { convertMessage } from '@/conversation/utils/convert-message';
import { GetConversationResponse } from '@/bam/namespaces/asset';
import type { AxiosResponse } from 'axios';
import { ClientContentMessage } from '@/conversation/client-message/content-message';
import { getAgentId } from '../get-agent-id';
import { getConversationTypeEnum } from '../get-conversation-type-enum';

export class RemoteStorageHistoryService implements IConversationHistoryService {
  public readonly _serviceBrand: undefined;

  private readonly _onConversationChange = new Emitter<[ConversationChangeEvent]>();
  public readonly onConversationChange: Event<[ConversationChangeEvent]> = this._onConversationChange.event;

  private readonly _networkClient: NetworkClientInstance;

  private readonly _storage: Memento;
  private readonly _messageSerializer = new MessageSerializer();

  constructor(
    @INetworkClientFactoryService networkClientFactory: INetworkClientFactoryService,
    @IExtensionContextService private readonly _extensionContextService: IExtensionContextService,
  ) {
    this._networkClient = networkClientFactory.build({});
    this._storage = this._extensionContextService.context.globalState;
  }

  /**
   * 获取所有会话列表
   * @param cursor
   * @param limit
   * @returns
   */
  public async fetchConversations(cursor?: string, limit = 20): Promise<ILvErrorOr<PaginatedConversations>> {
    try {
      const conversations = await this._getAllConversations();

      // 按时间倒序排序
      conversations.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());

      // 处理分页
      const startIndex = cursor ? Number.parseInt(cursor, 10) : 0;
      const endIndex = startIndex + limit;
      const paginatedItems = conversations.slice(startIndex, endIndex);

      const result: PaginatedConversations = {
        conversations: paginatedItems,
        hasMore: endIndex < conversations.length,
        nextCursor: endIndex < conversations.length ? endIndex.toString() : undefined,
        total: conversations.length,
      };

      return makeOkWith(result);
    } catch (error) {
      return makeErrorBy(-1, '获取会话列表失败', error as Error);
    }
  }

  /**
   * 获取指定会话的详细信息
   * @param cid
   * @returns
   */
  public async getConversation(cid: string): Promise<ILvErrorOr<ConversationDetail>> {
    try {
      const conversations = await this._getAllConversations();
      const summary = conversations.find((c) => c.id === cid);

      if (!summary) {
        return makeErrorBy(EGetConversationError.ConversationNotFound, '会话不存在', new Error('会话不存在'));
      }

      const messages = await this._getConversationMessages(cid);

      const detail: ConversationDetail = {
        ...summary,
        messages,
      };

      return makeOkWith(detail);
    } catch (error) {
      return makeErrorBy(EGetConversationError.Unknown, '获取会话详情失败', error as Error);
    }
  }

  /**
   * 保存会话
   * @param conversation
   * @returns
   */
  public async saveConversation(conversation: ConversationDetail): Promise<ILvErrorOr<void>> {
    if (!conversation.id) {
      return makeErrorBy(-1, '会话ID不能为空', new Error('会话ID不能为空'));
    }
    try {
      // 保存会话摘要
      await this._saveConversationSummary(conversation);

      // 保存消息
      await this._saveConversationMessages(conversation.id, conversation.messages);

      // 触发变更事件
      this._onConversationChange.fire({
        type: 'update',
        conversationId: conversation.id,
        conversation,
      });

      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, '保存会话失败', error as Error);
    }
  }

  /**
   * 删除会话
   * @param cid
   * @returns
   */
  public async deleteConversation(cid: string): Promise<ILvErrorOr<void>> {
    try {
      // 删除会话摘要
      const conversations = await this._getAllConversations();
      const filteredConversations = conversations.filter((c) => c.id !== cid);
      await this._storage.update(CONVERSATIONS_KEY, filteredConversations);

      // 删除消息
      await this._storage.update(MESSAGE_KEY_PREFIX + cid, undefined);

      // 触发变更事件
      this._onConversationChange.fire({
        type: 'delete',
        conversationId: cid,
      });

      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, '删除会话失败', error as Error);
    }
  }

  /**
   * 获取会话消息
   * @param cid
   * @param offset
   * @param limit
   * @returns
   */
  public async getConversationMessages(cid: string, offset = 0, limit = 50): Promise<ILvErrorOr<ClientMessage[]>> {
    try {
      const messages = await this._getConversationMessages(cid);
      const paginatedMessages = messages.slice(offset, offset + limit);
      return makeOkWith(paginatedMessages);
    } catch (error) {
      return makeErrorBy(-1, '获取会话消息失败', error as Error);
    }
  }

  /**
   * 获取会话的父子关系
   * @param parentId
   * @returns
   */
  public getConversationRelation(parentId: string): Promise<ILvErrorOr<IConversationRelation | null>> {
    const allRelations = this._storage.get<IConversationRelation[]>(CONVERSATION_RELATION_KEY) || null;
    if (!allRelations) {
      return Promise.resolve(makeOkWith(null));
    }
    const relation = allRelations.find((r) => r.parentId === parentId);
    return Promise.resolve(makeOkWith(relation || null));
  }

  /**
   * 保存会话的父子关系
   * @param options
   * @returns
   */
  public async saveConversationRelation(options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<void>> {
    const { cid, parentConversationId, parentMessageVersion } = options;
    const allRelations = this._storage.get<IConversationRelation[]>(CONVERSATION_RELATION_KEY) || [];
    const relation = allRelations.find((r) => r.parentId === parentConversationId);
    if (relation) {
      relation.childIds.push({ cid, parentMessageVersion });
    } else {
      allRelations.push({ parentId: parentConversationId, childIds: [{ cid, parentMessageVersion }] });
    }
    this._storage.update(CONVERSATION_RELATION_KEY, allRelations);
    return Promise.resolve(makeOkWith(undefined));
  }

  /**
   * 删除会话的父子关系
   * @param parentId
   * @param childId
   * @returns
   */
  public async deleteConversationRelation(parentId: string, childId: string): Promise<ILvErrorOr<void>> {
    const allRelations = this._storage.get<IConversationRelation[]>(CONVERSATION_RELATION_KEY) || [];
    const relation = allRelations.find((r) => r.parentId === parentId);
    if (relation) {
      relation.childIds = relation.childIds.filter((id) => id.cid !== childId);
    }
    this._storage.update(CONVERSATION_RELATION_KEY, allRelations);
    return Promise.resolve(makeOkWith(undefined));
  }

  private async _syncConversations(conversations: Array<{ id: string }>) {
    for (const conversation of conversations) {
      const response = await this._networkClient.get<unknown, AxiosResponse<{ data: GetConversationResponse }>>(
        `https://capcut-devops.byted.org/conversation/history?id=${conversation.id}&message=true`,
      );
      const body = response.data;
      const serverConversation = body.data.conversation;
      const serverMessages: SchemaMessage[] =
        body.data.messages?.filter((message: SchemaMessage) => message.role !== 'system') ?? [];
      const subConversations: SubConversation[] = body.data.sub_conversations ?? [];

      // 更新会话详情&消息
      const messages = convertMessage(serverMessages);
      const title = serverConversation.title;
      const lastUpdated = serverConversation.updated_at;
      // 获取最后一条消息预览
      const lastMessage = messages[messages.length - 1];
      let preview = '';
      if (lastMessage instanceof ClientContentMessage) {
        const content = lastMessage.content.trim();
        preview = content.length > 100 ? `${content.substring(0, 100)}...` : content;
      }
      const conversationDetail: ConversationDetail = {
        id: conversation.id,
        parentId: undefined,
        title: title ?? '',
        agentType: getAgentId(serverConversation.type),
        lastUpdated: lastUpdated?.toString() ?? '',
        messageCount: messages.length,
        preview,
        messages: [...messages],
        conversationType: getConversationTypeEnum(serverConversation.type),
      };
      await this._saveConversationSummary(conversationDetail);
      await this._saveConversationMessages(conversationDetail.id, conversationDetail.messages);

      // 更新会话relation
      for (const subConversation of subConversations) {
        await this.saveConversationRelation({
          cid: subConversation.id,
          parentConversationId: conversation.id,
          parentMessageVersion: subConversation.version,
        });
      }
      await this._syncConversations(subConversations);
    }
  }

  /**
   * 同步会话数据
   * @returns
   */
  public async syncConversations(): Promise<ILvErrorOr<void>> {
    const conversations = await this._getAllConversations();

    await this._syncConversations(conversations);

    return makeOkWith(undefined);
  }

  /**
   * 合并来自远程的会话数据
   * @param remoteConversations
   */
  public async mergeConversations(remoteConversations: ConversationSummary[]): Promise<ILvErrorOr<void>> {
    try {
      const localConversations = await this._getAllConversations();
      const localConversationMap = new Map(localConversations.map((c) => [c.id, c]));

      const mergedConversations: ConversationSummary[] = [...localConversations];

      // 合并远程会话
      for (const remoteConv of remoteConversations) {
        const localConv = localConversationMap.get(remoteConv.id);
        if (!localConv || new Date(remoteConv.lastUpdated) > new Date(localConv.lastUpdated)) {
          // 更新或添加
          const index = mergedConversations.findIndex((c) => c.id === remoteConv.id);
          if (index >= 0) {
            mergedConversations[index] = remoteConv;
          } else {
            mergedConversations.push(remoteConv);
          }
        }
      }

      await this._storage.update(CONVERSATIONS_KEY, mergedConversations);

      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, '合并会话数据失败', error as Error);
    }
  }

  private async _getAllConversations(): Promise<ConversationSummary[]> {
    const data = this._storage.get<ConversationSummary[]>(CONVERSATIONS_KEY);
    return data || [];
  }

  private async _saveConversationSummary(conversation: ConversationSummary): Promise<void> {
    const conversations = await this._getAllConversations();
    const index = conversations.findIndex((c) => c.id === conversation.id);

    if (index >= 0) {
      conversations[index] = conversation;
    } else {
      conversations.push(conversation);
    }

    await this._storage.update(CONVERSATIONS_KEY, conversations);
  }

  private async _getConversationMessages(cid: string): Promise<ClientMessage[]> {
    const raw = this._storage.get<BaseJsonMessage[]>(MESSAGE_KEY_PREFIX + cid);
    if (!raw || !Array.isArray(raw)) return [];

    try {
      return raw.map((msgData) => this._messageSerializer.deserialize(msgData)).filter(Boolean) as ClientMessage[];
    } catch {
      return [];
    }
  }

  private async _saveConversationMessages(cid: string, messages: ClientMessage[]): Promise<void> {
    const serializedMessages = messages.map((msg) => this._messageSerializer.serialize(msg));
    await this._storage.update(MESSAGE_KEY_PREFIX + cid, serializedMessages);
  }
}
