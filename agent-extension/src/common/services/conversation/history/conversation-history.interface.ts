import type { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { Event } from '@byted-image/lv-bedrock/event';
import type { AgentId } from '../const';
import { ClientMessage } from '@/conversation/client-message/abstract-message';
import type { Int64 } from '@/bam';

/** 会话摘要信息 */
export interface ConversationSummary {
  id: string;
  parentId?: string;
  title: string;
  agentType: AgentId;
  lastUpdated: string;
  messageCount: number;
  /** 最后一条消息预览 */
  preview?: string;
  conversationType?: ConversationTypeEnum;
}

/** 会话详细信息 */
export interface ConversationDetail extends ConversationSummary {
  messages: ClientMessage[];
}

/** 分页会话列表 */
export interface PaginatedConversations {
  conversations: ConversationSummary[];
  hasMore: boolean;
  nextCursor?: string;
  total: number;
}

/** 根据parentId获取会话列表 */
export interface IConversationRelation {
  parentId: string;
  childIds: {
    cid: string;
    parentMessageVersion: Int64;
  }[];
}

/** 会话变更事件 */
export interface ConversationChangeEvent {
  type: 'create' | 'update' | 'delete' | 'sync';
  conversationId: string;
  conversation?: ConversationSummary;
}

export enum EGetConversationError {
  Unknown = -1,
  ConversationNotFound = -2,
}

/** 历史会话管理服务接口 */
export interface IConversationHistoryService {
  _serviceBrand: undefined;

  /** 获取所有会话列表 */
  fetchConversations(cursor?: string, limit?: number): Promise<ILvErrorOr<PaginatedConversations>>;

  /** 获取指定会话的详细信息 */
  getConversation(cid: string): Promise<ILvErrorOr<ConversationDetail>>;

  /** 保存会话 */
  saveConversation(conversation: ConversationDetail): Promise<ILvErrorOr<void>>;

  /** 删除会话 */
  deleteConversation(cid: string): Promise<ILvErrorOr<void>>;

  /** 获取会话消息 */
  getConversationMessages(cid: string, offset?: number, limit?: number): Promise<ILvErrorOr<ClientMessage[]>>;

  /** 同步会话数据 */
  syncConversations(): Promise<ILvErrorOr<void>>;

  /** 获取会话的父子关系 */
  getConversationRelation(parentId: string): Promise<ILvErrorOr<IConversationRelation | null>>;

  /** 保存会话的父子关系 */
  saveConversationRelation(options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<void>>;

  /** 删除会话的父子关系 */
  deleteConversationRelation(parentId: string, childId: string): Promise<ILvErrorOr<void>>;

  /** 会话变更事件 */
  onConversationChange: Event<[ConversationChangeEvent]>;
}

export const IConversationHistoryService = createDecorator<IConversationHistoryService>('conversation-history-service');
