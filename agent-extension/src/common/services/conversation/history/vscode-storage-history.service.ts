import { IExtensionContextService } from '@/common/services/commands/extension-context.interface';
import { type ILvErrorOr, makeErrorBy, makeOkWith } from '@byted-image/lv-bedrock/error';
import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import type { Memento } from 'vscode';

import type { Int64 } from '@/bam';
import { BaseJsonMessage, ClientMessage } from '@/conversation/client-message/abstract-message';
import { MessageSerializer } from '@/conversation/message-serializer';
import {
  EGetConversationError,
  type ConversationChangeEvent,
  type ConversationDetail,
  type IConversationRelation,
  type ConversationSummary,
  type IConversationHistoryService,
  type PaginatedConversations,
} from './conversation-history.interface';
import { CONVERSATIONS_KEY, CONVERSATION_RELATION_KEY, MESSAGE_KEY_PREFIX } from './constants';

export class ExtensionStorageHistoryService implements IConversationHistoryService {
  public readonly _serviceBrand: undefined;
  private readonly _onConversationChange = new Emitter<[ConversationChangeEvent]>();
  public readonly onConversationChange: Event<[ConversationChangeEvent]> = this._onConversationChange.event;

  private readonly _storage: Memento;
  private readonly _messageSerializer = new MessageSerializer();

  constructor(@IExtensionContextService extensionContextService: IExtensionContextService) {
    this._storage = extensionContextService.context.globalState;
  }
  getConversationRelation(parentId: string): Promise<ILvErrorOr<IConversationRelation | null>> {
    // 获取会话的父子关系
    const allRelations = this._storage.get<IConversationRelation[]>(CONVERSATION_RELATION_KEY) || null;
    if (!allRelations) {
      return Promise.resolve(makeOkWith(null));
    }
    const relation = allRelations.find((r) => r.parentId === parentId);
    return Promise.resolve(makeOkWith(relation || null));
  }

  saveConversationRelation(options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<void>> {
    const { cid, parentConversationId, parentMessageVersion } = options;
    const allRelations = this._storage.get<IConversationRelation[]>(CONVERSATION_RELATION_KEY) || [];
    const relation = allRelations.find((r) => r.parentId === parentConversationId);
    if (relation) {
      relation.childIds.push({ cid, parentMessageVersion });
    } else {
      allRelations.push({ parentId: parentConversationId, childIds: [{ cid, parentMessageVersion }] });
    }
    this._storage.update(CONVERSATION_RELATION_KEY, allRelations);
    return Promise.resolve(makeOkWith(undefined));
  }

  deleteConversationRelation(parentId: string, childId: string): Promise<ILvErrorOr<void>> {
    const allRelations = this._storage.get<IConversationRelation[]>(CONVERSATION_RELATION_KEY) || [];
    const relation = allRelations.find((r) => r.parentId === parentId);
    if (relation) {
      relation.childIds = relation.childIds.filter((id) => id.cid !== childId);
    }
    this._storage.update(CONVERSATION_RELATION_KEY, allRelations);
    return Promise.resolve(makeOkWith(undefined));
  }

  async fetchConversations(cursor?: string, limit = 20): Promise<ILvErrorOr<PaginatedConversations>> {
    try {
      const conversations = await this._getAllConversations();

      // 按时间倒序排序
      conversations.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());

      // 处理分页
      const startIndex = cursor ? Number.parseInt(cursor, 10) : 0;
      const endIndex = startIndex + limit;
      const paginatedItems = conversations.slice(startIndex, endIndex);

      const result: PaginatedConversations = {
        conversations: paginatedItems,
        hasMore: endIndex < conversations.length,
        nextCursor: endIndex < conversations.length ? endIndex.toString() : undefined,
        total: conversations.length,
      };

      return makeOkWith(result);
    } catch (error) {
      return makeErrorBy(-1, '获取会话列表失败', error as Error);
    }
  }

  async getConversation(cid: string): Promise<ILvErrorOr<ConversationDetail>> {
    try {
      const conversations = await this._getAllConversations();
      const summary = conversations.find((c) => c.id === cid);

      if (!summary) {
        return makeErrorBy(EGetConversationError.ConversationNotFound, '会话不存在', new Error('会话不存在'));
      }

      const messages = await this._getConversationMessages(cid);

      const detail: ConversationDetail = {
        ...summary,
        messages,
      };

      return makeOkWith(detail);
    } catch (error) {
      return makeErrorBy(EGetConversationError.Unknown, '获取会话详情失败', error as Error);
    }
  }

  async saveConversation(conversation: ConversationDetail): Promise<ILvErrorOr<void>> {
    if (!conversation.id) {
      return makeErrorBy(-1, '会话ID不能为空', new Error('会话ID不能为空'));
    }
    try {
      // 保存会话摘要
      await this._saveConversationSummary(conversation);

      // 保存消息
      await this._saveConversationMessages(conversation.id, conversation.messages);

      // 触发变更事件
      this._onConversationChange.fire({
        type: 'update',
        conversationId: conversation.id,
        conversation,
      });

      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, '保存会话失败', error as Error);
    }
  }

  async deleteConversation(cid: string): Promise<ILvErrorOr<void>> {
    try {
      // 删除会话摘要
      const conversations = await this._getAllConversations();
      const filteredConversations = conversations.filter((c) => c.id !== cid);
      await this._storage.update(CONVERSATIONS_KEY, filteredConversations);

      // 删除消息
      await this._storage.update(MESSAGE_KEY_PREFIX + cid, undefined);

      // 触发变更事件
      this._onConversationChange.fire({
        type: 'delete',
        conversationId: cid,
      });

      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, '删除会话失败', error as Error);
    }
  }

  async getConversationMessages(cid: string, offset = 0, limit = 50): Promise<ILvErrorOr<ClientMessage[]>> {
    try {
      const messages = await this._getConversationMessages(cid);
      const paginatedMessages = messages.slice(offset, offset + limit);
      return makeOkWith(paginatedMessages);
    } catch (error) {
      return makeErrorBy(-1, '获取会话消息失败', error as Error);
    }
  }

  async syncConversations(): Promise<ILvErrorOr<void>> {
    // VSCode存储服务不需要同步
    return makeOkWith(undefined);
  }

  // 合并来自远程的会话数据
  async mergeConversations(remoteConversations: ConversationSummary[]): Promise<ILvErrorOr<void>> {
    try {
      const localConversations = await this._getAllConversations();
      const localConversationMap = new Map(localConversations.map((c) => [c.id, c]));

      const mergedConversations: ConversationSummary[] = [...localConversations];

      // 合并远程会话
      for (const remoteConv of remoteConversations) {
        const localConv = localConversationMap.get(remoteConv.id);
        if (!localConv || new Date(remoteConv.lastUpdated) > new Date(localConv.lastUpdated)) {
          // 更新或添加
          const index = mergedConversations.findIndex((c) => c.id === remoteConv.id);
          if (index >= 0) {
            mergedConversations[index] = remoteConv;
          } else {
            mergedConversations.push(remoteConv);
          }
        }
      }

      await this._storage.update(CONVERSATIONS_KEY, mergedConversations);

      return makeOkWith(undefined);
    } catch (error) {
      return makeErrorBy(-1, '合并会话数据失败', error as Error);
    }
  }

  private async _getAllConversations(): Promise<ConversationSummary[]> {
    const data = this._storage.get<ConversationSummary[]>(CONVERSATIONS_KEY);
    return data || [];
  }

  private async _saveConversationSummary(conversation: ConversationSummary): Promise<void> {
    const conversations = await this._getAllConversations();
    const index = conversations.findIndex((c) => c.id === conversation.id);

    if (index >= 0) {
      conversations[index] = conversation;
    } else {
      conversations.push(conversation);
    }

    await this._storage.update(CONVERSATIONS_KEY, conversations);
  }

  private async _getConversationMessages(cid: string): Promise<ClientMessage[]> {
    const raw = this._storage.get<BaseJsonMessage[]>(MESSAGE_KEY_PREFIX + cid);
    if (!raw || !Array.isArray(raw)) return [];

    try {
      return raw.map((msgData) => this._messageSerializer.deserialize(msgData)).filter(Boolean) as ClientMessage[];
    } catch {
      return [];
    }
  }

  private async _saveConversationMessages(cid: string, messages: ClientMessage[]): Promise<void> {
    const serializedMessages = messages.map((msg) => this._messageSerializer.serialize(msg));
    await this._storage.update(MESSAGE_KEY_PREFIX + cid, serializedMessages);
  }
}
