import { ConversationType } from '@/bam/namespaces/conversation';
import { AgentId } from './const';

export function getAgentId(conversationType: ConversationType): AgentId {
  switch (conversationType) {
    case ConversationType.Understanding:
      return AgentId.Understanding;
    case ConversationType.Arch:
      return AgentId.PrdAgent;
    case ConversationType.Coding:
      return AgentId.Coding;
    case ConversationType.D2c:
      return AgentId.D2c;
    default:
      // TODO: chezongshao 类型体操暂时不想做了
      return conversationType as any;
  }
}
