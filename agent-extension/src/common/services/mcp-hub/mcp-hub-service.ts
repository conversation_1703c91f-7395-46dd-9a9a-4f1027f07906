import type { Int64 } from '@/bam';
import { DataType, type ParameterInfo, type ToolInfo } from '@/bam/namespaces/agentclient';
import { cwd } from '@/common/env/system';
import { type Tool, type ToolProgressCallback, ToolResult, ToolResultStatus } from '@/tools/base';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import {
  CallToolResultSchema,
  ListResourceTemplatesResultSchema,
  ListResourcesResultSchema,
  ListToolsResultSchema,
  ReadResourceResultSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { createDirectoriesForFile, fileExistsAtPath } from '@utils/fs';
import { arePathsEqual } from '@utils/path';
import { secondsToMs } from '@utils/time';
import chokidar, { FSWatcher } from 'chokidar';
import deepEqual from 'fast-deep-equal';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { setTimeout as setTimeoutPromise } from 'node:timers/promises';
import * as vscode from 'vscode';
import { z } from 'zod';
import { IRpcService } from '../rpc/rpc-service.interface';
import {
  DEFAULT_MCP_TIMEOUT_SECONDS,
  MIN_MCP_TIMEOUT_SECONDS,
  McpConnection,
  McpResource,
  McpResourceResponse,
  McpResourceTemplate,
  McpServer,
  McpTool,
  McpToolCallResponse,
} from './common';
import type { IMcpHubService } from './mcp-hub-service.interface';

// Default timeout for internal MCP data requests in milliseconds; is not the same as the user facing timeout stored as DEFAULT_MCP_TIMEOUT_SECONDS
const DEFAULT_REQUEST_TIMEOUT_MS = 5000;

export type McpTransportType = 'stdio' | 'sse' | 'http';

export type McpServerConfig = z.infer<typeof ServerConfigSchema>;

const AutoApproveSchema = z.array(z.string()).default([]);

const BaseConfigSchema = z.object({
  autoApprove: AutoApproveSchema.optional(),
  disabled: z.boolean().optional(),
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS),
});

const SseConfigSchema = BaseConfigSchema.extend({
  url: z.string().url(),
}).transform((config) => ({
  ...config,
  transportType: 'sse' as const,
}));

const StdioConfigSchema = BaseConfigSchema.extend({
  command: z.string(),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
}).transform((config) => ({
  ...config,
  transportType: 'stdio' as const,
}));

const StreamableHTTPConfigSchema = BaseConfigSchema.extend({
  transportType: z.literal('http'),
  url: z.string().url(),
}).transform((config) => ({
  ...config,
  transportType: 'http' as const,
}));

const ServerConfigSchema = z.union([StdioConfigSchema, SseConfigSchema, StreamableHTTPConfigSchema]);

const McpSettingsSchema = z.object({
  mcpServers: z.record(ServerConfigSchema),
});

export class McpHubService implements IMcpHubService {
  public _serviceBrand: undefined;

  private clientVersion: string;

  private disposables: vscode.Disposable[] = [];
  private settingsWatcher?: vscode.FileSystemWatcher;
  private fileWatchers: Map<string, FSWatcher> = new Map();
  connections: McpConnection[] = [];
  isConnecting = false;

  constructor(
    clientVersion: string,
    @IRpcService private readonly _rpcService: IRpcService,
  ) {
    this.clientVersion = clientVersion;
    this.watchMcpSettingsFile();
    this.initializeMcpServers();
  }

  private readonly _mcpServersDirectory = path.join(cwd, '.codin', 'mcp');
  private readonly _mcpConfigPath = path.join(this._mcpServersDirectory, 'mcp.json');
  private async _ensureMcpDirectoryExists() {
    await createDirectoriesForFile(this._mcpConfigPath);
  }

  public async getMcpServersPath() {
    await this._ensureMcpDirectoryExists();
    return this._mcpServersDirectory;
  }

  private readonly _settingsDirectory = path.join(cwd, '.codin', 'settings');
  public readonly settingsFilePath = path.join(this._settingsDirectory, 'codin_mcp_settings.json');
  private async _ensureSettingsDirectoryExists() {
    await createDirectoriesForFile(this.settingsFilePath);
  }

  getServers(): McpServer[] {
    // Only return enabled servers

    return this.connections.filter((conn) => !conn.server.disabled).map((conn) => conn.server);
  }

  async createMcpSettingsFileIfNeeded(): Promise<string> {
    await this._ensureSettingsDirectoryExists();
    const mcpSettingsFilePath = this.settingsFilePath;
    const fileExists = await fileExistsAtPath(mcpSettingsFilePath);
    if (!fileExists) {
      await fs.writeFile(
        mcpSettingsFilePath,
        `{
  "mcpServers": {
    
  }
}`,
      );
    }
    return mcpSettingsFilePath;
  }

  private async readAndValidateMcpSettingsFile(): Promise<z.infer<typeof McpSettingsSchema> | undefined> {
    try {
      const settingsPath = await this.createMcpSettingsFileIfNeeded();
      const content = await fs.readFile(settingsPath, 'utf-8');

      let config: any;

      // Parse JSON file content
      try {
        config = JSON.parse(content);
      } catch (error) {
        vscode.window.showErrorMessage(
          'Invalid MCP settings format. Please ensure your settings follow the correct JSON format.',
        );
        return undefined;
      }

      // Validate against schema
      const result = McpSettingsSchema.safeParse(config);
      if (!result.success) {
        vscode.window.showErrorMessage('Invalid MCP settings schema.');
        return undefined;
      }

      return result.data;
    } catch (error) {
      console.error('Failed to read MCP settings:', error);
      return undefined;
    }
  }

  private async watchMcpSettingsFile(): Promise<void> {
    const settingsPath = this.settingsFilePath;

    this.disposables.push(
      vscode.workspace.onDidCreateFiles(async (event) => {
        for (const file of event.files) {
          if (arePathsEqual(file.fsPath, settingsPath)) {
            await this.updateMCPServer();
          }
        }
      }),
      vscode.workspace.onDidSaveTextDocument(async (document) => {
        if (arePathsEqual(document.uri.fsPath, settingsPath)) {
          await this.updateMCPServer();
        }
      }),
      vscode.workspace.onDidDeleteFiles(async (event) => {
        for (const file of event.files) {
          if (arePathsEqual(file.fsPath, settingsPath)) {
            await this.deleteAllConnections();
          }
        }
      }),
    );
  }

  private async updateMCPServer() {
    const settings = await this.readAndValidateMcpSettingsFile();
    if (settings) {
      try {
        vscode.window.showInformationMessage('Updating MCP servers...');
        await this.updateServerConnections(settings.mcpServers);
        vscode.window.showInformationMessage('MCP servers updated');
      } catch (error) {
        console.error('Failed to process MCP settings change:', error);
      }
    }
  }

  private async initializeMcpServers(): Promise<void> {
    const fileExists = await fileExistsAtPath(this.settingsFilePath);
    if (!fileExists) return;

    const settings = await this.readAndValidateMcpSettingsFile();
    if (settings) {
      await this.updateServerConnections(settings.mcpServers);
    }
  }

  private async connectToServerRPC(
    name: string,
    config:
      | z.infer<typeof StdioConfigSchema>
      | z.infer<typeof SseConfigSchema>
      | z.infer<typeof StreamableHTTPConfigSchema>,
  ): Promise<void> {
    // Remove existing connection if it exists (should never happen, the connection should be deleted beforehand)
    this.connections = this.connections.filter((conn) => conn.server.name !== name);

    try {
      // Each MCP server requires its own transport connection and has unique capabilities, configurations, and error handling. Having separate clients also allows proper scoping of resources/tools and independent server management like reconnection.
      const client = new Client(
        {
          name: 'Codin',
          version: this.clientVersion,
        },
        {
          capabilities: {},
        },
      );

      let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;

      if (config.transportType === 'sse') {
        transport = new SSEClientTransport(new URL(config.url), {});
      } else if (config.transportType === 'http') {
        transport = new StreamableHTTPClientTransport(new URL(config.url), {});
      } else {
        transport = new StdioClientTransport({
          command: config.command,
          args: config.args,
          env: {
            ...config.env,
            ...(process.env.PATH ? { PATH: process.env.PATH } : {}),
            // ...(process.env.NODE_PATH ? { NODE_PATH: process.env.NODE_PATH } : {}),
          },
          stderr: 'pipe', // necessary for stderr to be available
        });
      }

      transport.onerror = async (error: Error) => {
        console.error(`Transport error for "${name}":`, error);
        const connection = this.connections.find((conn) => conn.server.name === name);
        if (connection) {
          connection.server.status = 'disconnected';
          this.appendErrorMessage(connection, error.message);
        }
      };

      transport.onclose = async () => {
        const connection = this.connections.find((conn) => conn.server.name === name);
        if (connection) {
          connection.server.status = 'disconnected';
        }
      };

      const connection: McpConnection = {
        server: {
          name,
          config: JSON.stringify(config),
          status: 'connecting',
          disabled: config.disabled,
        },
        client,
        transport,
      };
      this.connections.push(connection);

      if (config.transportType === 'stdio') {
        // transport.stderr is only available after the process has been started. However we can't start it separately from the .connect() call because it also starts the transport. And we can't place this after the connect call since we need to capture the stderr stream before the connection is established, in order to capture errors during the connection process.
        // As a workaround, we start the transport ourselves, and then monkey-patch the start method to no-op so that .connect() doesn't try to start it again.
        await transport.start();
        const stderrStream = (transport as StdioClientTransport).stderr;
        if (stderrStream) {
          stderrStream.on('data', async (data: Buffer) => {
            const output = data.toString();
            // Check if output contains INFO level log
            const isInfoLog = !/\berror\b/i.test(output);

            if (isInfoLog) {
              // Log normal informational messages
              console.info(`Server "${name}" info:`, output);
            } else {
              // Treat as error log
              console.error(`Server "${name}" stderr:`, output);
              const connection = this.connections.find((conn) => conn.server.name === name);
              if (connection) {
                this.appendErrorMessage(connection, output);
              }
            }
          });
        } else {
          console.error(`No stderr stream for ${name}`);
        }
        transport.start = async () => {}; // No-op now, .connect() won't fail
      }

      // Connect
      await client.connect(transport);

      connection.server.status = 'connected';
      connection.server.error = '';

      // Initial fetch of tools and resources
      connection.server.tools = await this.fetchToolsList(name);
      connection.server.resources = await this.fetchResourcesList(name);
      connection.server.resourceTemplates = await this.fetchResourceTemplatesList(name);
    } catch (error) {
      // Update status with error
      const connection = this.connections.find((conn) => conn.server.name === name);
      if (connection) {
        connection.server.status = 'disconnected';
        this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error));
      }
      throw error;
    }
  }

  private async connectToServer(
    name: string,
    config:
      | z.infer<typeof StdioConfigSchema>
      | z.infer<typeof SseConfigSchema>
      | z.infer<typeof StreamableHTTPConfigSchema>,
  ): Promise<void> {
    // Remove existing connection if it exists (should never happen, the connection should be deleted beforehand)
    this.connections = this.connections.filter((conn) => conn.server.name !== name);

    try {
      // Each MCP server requires its own transport connection and has unique capabilities, configurations, and error handling. Having separate clients also allows proper scoping of resources/tools and independent server management like reconnection.
      const client = new Client(
        {
          name: 'Codin',
          version: this.clientVersion,
        },
        {
          capabilities: {},
        },
      );

      let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;

      if (config.transportType === 'sse') {
        transport = new SSEClientTransport(new URL(config.url), {});
      } else if (config.transportType === 'http') {
        transport = new StreamableHTTPClientTransport(new URL(config.url), {});
      } else {
        transport = new StdioClientTransport({
          command: config.command,
          args: config.args,
          env: {
            ...config.env,
            ...(process.env.PATH ? { PATH: process.env.PATH } : {}),
            // ...(process.env.NODE_PATH ? { NODE_PATH: process.env.NODE_PATH } : {}),
          },
          stderr: 'pipe', // necessary for stderr to be available
        });
      }

      transport.onerror = async (error: Error) => {
        console.error(`Transport error for "${name}":`, error);
        const connection = this.connections.find((conn) => conn.server.name === name);
        if (connection) {
          connection.server.status = 'disconnected';
          this.appendErrorMessage(connection, error.message);
        }
        await this.notifyWebviewOfServerChanges();
      };

      transport.onclose = async () => {
        const connection = this.connections.find((conn) => conn.server.name === name);
        if (connection) {
          connection.server.status = 'disconnected';
        }
        await this.notifyWebviewOfServerChanges();
      };

      const connection: McpConnection = {
        server: {
          name,
          config: JSON.stringify(config),
          status: 'connecting',
          disabled: config.disabled,
        },
        client,
        transport,
      };
      this.connections.push(connection);

      if (config.transportType === 'stdio') {
        // transport.stderr is only available after the process has been started. However we can't start it separately from the .connect() call because it also starts the transport. And we can't place this after the connect call since we need to capture the stderr stream before the connection is established, in order to capture errors during the connection process.
        // As a workaround, we start the transport ourselves, and then monkey-patch the start method to no-op so that .connect() doesn't try to start it again.
        await transport.start();
        const stderrStream = (transport as StdioClientTransport).stderr;
        if (stderrStream) {
          stderrStream.on('data', async (data: Buffer) => {
            const output = data.toString();
            // Check if output contains INFO level log
            const isInfoLog = !/\berror\b/i.test(output);

            if (isInfoLog) {
              // Log normal informational messages
              console.info(`Server "${name}" info:`, output);
            } else {
              // Treat as error log
              // here: chezongshao
              console.error(`Server "${name}" stderr:`, output);
              const connection = this.connections.find((conn) => conn.server.name === name);
              if (connection) {
                this.appendErrorMessage(connection, output);
                // Only notify webview if server is already disconnected
                if (connection.server.status === 'disconnected') {
                  await this.notifyWebviewOfServerChanges();
                }
              }
            }
          });
        } else {
          console.error(`No stderr stream for ${name}`);
        }
        transport.start = async () => {}; // No-op now, .connect() won't fail
      }

      // Connect
      await client.connect(transport);

      connection.server.status = 'connected';
      connection.server.error = '';

      // Initial fetch of tools and resources
      connection.server.tools = await this.fetchToolsList(name);
      connection.server.resources = await this.fetchResourcesList(name);
      connection.server.resourceTemplates = await this.fetchResourceTemplatesList(name);
    } catch (error) {
      // Update status with error
      const connection = this.connections.find((conn) => conn.server.name === name);
      if (connection) {
        connection.server.status = 'disconnected';
        this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error));
      }
      throw error;
    }
  }

  private appendErrorMessage(connection: McpConnection, error: string) {
    const newError = connection.server.error ? `${connection.server.error}\n${error}` : error;
    connection.server.error = newError; //.slice(0, 800)
  }

  private async fetchToolsList(serverName: string): Promise<McpTool[]> {
    try {
      const connection = this.connections.find((conn) => conn.server.name === serverName);

      if (!connection) {
        throw new Error(`No connection found for server: ${serverName}`);
      }

      const response = await connection.client.request({ method: 'tools/list' }, ListToolsResultSchema, {
        timeout: DEFAULT_REQUEST_TIMEOUT_MS,
      });

      // Get autoApprove settings
      const settingsPath = await this.createMcpSettingsFileIfNeeded();
      const content = await fs.readFile(settingsPath, 'utf-8');
      const config = JSON.parse(content);
      const autoApproveConfig = config.mcpServers[serverName]?.autoApprove || [];

      // Mark tools as always allowed based on settings
      const tools = (response?.tools || []).map((tool: any) => ({
        ...tool,
        autoApprove: autoApproveConfig.includes(tool.name),
      }));

      return tools;
    } catch (error) {
      console.error(`Failed to fetch tools for ${serverName}:`, error);
      return [];
    }
  }

  private async fetchResourcesList(serverName: string): Promise<McpResource[]> {
    try {
      const response = await this.connections
        .find((conn) => conn.server.name === serverName)
        ?.client.request({ method: 'resources/list' }, ListResourcesResultSchema, {
          timeout: DEFAULT_REQUEST_TIMEOUT_MS,
        });
      return response?.resources || [];
    } catch (error) {
      // console.error(`Failed to fetch resources for ${serverName}:`, error)
      return [];
    }
  }

  private async fetchResourceTemplatesList(serverName: string): Promise<McpResourceTemplate[]> {
    try {
      const response = await this.connections
        .find((conn) => conn.server.name === serverName)
        ?.client.request({ method: 'resources/templates/list' }, ListResourceTemplatesResultSchema, {
          timeout: DEFAULT_REQUEST_TIMEOUT_MS,
        });

      return response?.resourceTemplates || [];
    } catch (error) {
      // console.error(`Failed to fetch resource templates for ${serverName}:`, error)
      return [];
    }
  }

  async deleteConnection(name: string): Promise<void> {
    const connection = this.connections.find((conn) => conn.server.name === name);
    if (connection) {
      try {
        await connection.transport.close();
        await connection.client.close();
      } catch (error) {
        console.error(`Failed to close transport for ${name}:`, error);
      }
      this.connections = this.connections.filter((conn) => conn.server.name !== name);
    }
  }

  async deleteAllConnections() {
    for (const connection of this.connections) {
      try {
        await connection.transport.close();
        await connection.client.close();
      } catch (error) {
        console.error(`Failed to close transport for ${name}:`, error);
      }
    }
    this.connections = [];
  }

  async updateServerConnectionsRPC(newServers: Record<string, McpServerConfig>): Promise<void> {
    this.isConnecting = true;
    this.removeAllFileWatchers();
    const currentNames = new Set(this.connections.map((conn) => conn.server.name));
    const newNames = new Set(Object.keys(newServers));

    // Delete removed servers
    for (const name of currentNames) {
      if (!newNames.has(name)) {
        await this.deleteConnection(name);
        console.log(`Deleted MCP server: ${name}`);
      }
    }

    // Update or add servers
    for (const [name, config] of Object.entries(newServers)) {
      const currentConnection = this.connections.find((conn) => conn.server.name === name);

      if (!currentConnection) {
        // New server
        try {
          if (config.transportType === 'stdio') {
            this.setupFileWatcher(name, config);
          }
          await this.connectToServer(name, config);
        } catch (error) {
          console.error(`Failed to connect to new MCP server ${name}:`, error);
        }
      } else if (!deepEqual(JSON.parse(currentConnection.server.config), config)) {
        // Existing server with changed config
        try {
          if (config.transportType === 'stdio') {
            this.setupFileWatcher(name, config);
          }
          await this.deleteConnection(name);
          await this.connectToServer(name, config);
          console.log(`Reconnected MCP server with updated config: ${name}`);
        } catch (error) {
          console.error(`Failed to reconnect MCP server ${name}:`, error);
        }
      }
      // If server exists with same config, do nothing
    }

    this.isConnecting = false;
  }

  async updateServerConnections(newServers: Record<string, McpServerConfig>): Promise<void> {
    this.isConnecting = true;
    this.removeAllFileWatchers();
    const currentNames = new Set(this.connections.map((conn) => conn.server.name));
    const newNames = new Set(Object.keys(newServers));

    // Delete removed servers
    for (const name of currentNames) {
      if (!newNames.has(name)) {
        await this.deleteConnection(name);
        console.log(`Deleted MCP server: ${name}`);
      }
    }

    // Update or add servers
    for (const [name, config] of Object.entries(newServers)) {
      const currentConnection = this.connections.find((conn) => conn.server.name === name);

      if (!currentConnection) {
        // New server
        try {
          if (config.transportType === 'stdio') {
            this.setupFileWatcher(name, config);
          }
          await this.connectToServer(name, config);
        } catch (error) {
          console.error(`Failed to connect to new MCP server ${name}:`, error);
        }
      } else if (!deepEqual(JSON.parse(currentConnection.server.config), config)) {
        // Existing server with changed config
        try {
          if (config.transportType === 'stdio') {
            this.setupFileWatcher(name, config);
          }
          await this.deleteConnection(name);
          await this.connectToServer(name, config);
          console.log(`Reconnected MCP server with updated config: ${name}`);
        } catch (error) {
          console.error(`Failed to reconnect MCP server ${name}:`, error);
        }
      }
      // If server exists with same config, do nothing
    }
    await this.notifyWebviewOfServerChanges();
    this.isConnecting = false;
  }

  private setupFileWatcher(name: string, config: Extract<McpServerConfig, { transportType: 'stdio' }>) {
    const filePath = config.args?.find((arg: string) => arg.includes('build/index.js'));
    if (filePath) {
      // we use chokidar instead of onDidSaveTextDocument because it doesn't require the file to be open in the editor. The settings config is better suited for onDidSave since that will be manually updated by the user or Codin (and we want to detect save events, not every file change)
      const watcher = chokidar.watch(filePath, {
        // persistent: true,
        // ignoreInitial: true,
        // awaitWriteFinish: true, // This helps with atomic writes
      });

      watcher.on('change', () => {
        console.log(`Detected change in ${filePath}. Restarting server ${name}...`);
        this.restartConnection(name);
      });

      this.fileWatchers.set(name, watcher);
    }
  }

  private removeAllFileWatchers() {
    // biome-ignore lint/complexity/noForEach: <explanation>
    this.fileWatchers.forEach((watcher) => watcher.close());
    this.fileWatchers.clear();
  }

  async restartConnectionRPC(serverName: string): Promise<McpServer[]> {
    this.isConnecting = true;

    // Get existing connection and update its status
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    const inMemoryConfig = connection?.server.config;
    if (inMemoryConfig) {
      connection.server.status = 'connecting';
      connection.server.error = '';
      await setTimeoutPromise(500); // artificial delay to show user that server is restarting
      try {
        await this.deleteConnection(serverName);
        // Try to connect again using existing config
        await this.connectToServerRPC(serverName, JSON.parse(inMemoryConfig));
      } catch (error) {
        console.error(`Failed to restart connection for ${serverName}:`, error);
      }
    }

    this.isConnecting = false;

    const config = await this.readAndValidateMcpSettingsFile();
    if (!config) {
      throw new Error('Failed to read or validate MCP settings');
    }

    const serverOrder = Object.keys(config.mcpServers || {});
    return this.getSortedMcpServers(serverOrder);
  }

  async restartConnection(serverName: string): Promise<void> {
    this.isConnecting = true;

    // Get existing connection and update its status
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    const config = connection?.server.config;
    if (config) {
      vscode.window.showInformationMessage(`Restarting ${serverName} MCP server...`);
      connection.server.status = 'connecting';
      connection.server.error = '';
      await this.notifyWebviewOfServerChanges();
      await setTimeoutPromise(500); // artificial delay to show user that server is restarting
      try {
        await this.deleteConnection(serverName);
        // Try to connect again using existing config
        await this.connectToServer(serverName, JSON.parse(config));
        vscode.window.showInformationMessage(`${serverName} MCP server connected`);
      } catch (error) {
        console.error(`Failed to restart connection for ${serverName}:`, error);
        vscode.window.showErrorMessage(`Failed to connect to ${serverName} MCP server`);
      }
    }

    await this.notifyWebviewOfServerChanges();
    this.isConnecting = false;
  }

  /**
   * Gets sorted MCP servers based on the order defined in settings
   * @param serverOrder Array of server names in the order they appear in settings
   * @returns Array of McpServer objects sorted according to settings order
   */
  private getSortedMcpServers(serverOrder: string[]): McpServer[] {
    return [...this.connections]
      .sort((a, b) => {
        const indexA = serverOrder.indexOf(a.server.name);
        const indexB = serverOrder.indexOf(b.server.name);
        return indexA - indexB;
      })
      .map((connection) => connection.server);
  }

  private async notifyWebviewOfServerChanges(): Promise<void> {
    // servers should always be sorted in the order they are defined in the settings file
    const settingsPath = await this.createMcpSettingsFileIfNeeded();
    const content = await fs.readFile(settingsPath, 'utf-8');
    const config = JSON.parse(content);
    const serverOrder = Object.keys(config.mcpServers || {});
    await this._rpcService.initialize();
    this._rpcService.notify('mcpServers', {
      mcpServers: this.getSortedMcpServers(serverOrder),
    });
  }

  async sendLatestMcpServers() {
    const fileExists = await fileExistsAtPath(this.settingsFilePath);
    if (!fileExists) return;
    await this.notifyWebviewOfServerChanges();
  }

  // Using server

  // Public methods for server management

  public async toggleServerDisabledRPC(serverName: string, disabled: boolean): Promise<McpServer[]> {
    try {
      const config = await this.readAndValidateMcpSettingsFile();
      if (!config) {
        throw new Error('Failed to read or validate MCP settings');
      }

      if (config.mcpServers[serverName]) {
        config.mcpServers[serverName].disabled = disabled;

        const settingsPath = await this.createMcpSettingsFileIfNeeded();
        await fs.writeFile(settingsPath, JSON.stringify(config, null, 2));

        const connection = this.connections.find((conn) => conn.server.name === serverName);
        if (connection) {
          connection.server.disabled = disabled;
        }

        const serverOrder = Object.keys(config.mcpServers || {});
        return this.getSortedMcpServers(serverOrder);
      }
      console.error(`Server "${serverName}" not found in MCP configuration`);
      throw new Error(`Server "${serverName}" not found in MCP configuration`);
    } catch (error) {
      console.error('Failed to update server disabled state:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
      }
      vscode.window.showErrorMessage(
        `Failed to update server state: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async readResource(serverName: string, uri: string): Promise<McpResourceResponse> {
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      throw new Error(`No connection found for server: ${serverName}`);
    }
    if (connection.server.disabled) {
      throw new Error(`Server "${serverName}" is disabled`);
    }

    return await connection.client.request(
      {
        method: 'resources/read',
        params: {
          uri,
        },
      },
      ReadResourceResultSchema,
    );
  }

  async callTool(
    serverName: string,
    toolName: string,
    toolArguments?: Record<string, unknown>,
  ): Promise<McpToolCallResponse> {
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      throw new Error(
        `No connection found for server: ${serverName}. Please make sure to use MCP servers available under 'Connected MCP Servers'.`,
      );
    }

    if (connection.server.disabled) {
      throw new Error(`Server "${serverName}" is disabled and cannot be used`);
    }

    let timeout = secondsToMs(DEFAULT_MCP_TIMEOUT_SECONDS); // sdk expects ms

    try {
      const config = JSON.parse(connection.server.config);
      const parsedConfig = ServerConfigSchema.parse(config);
      timeout = secondsToMs(parsedConfig.timeout);
    } catch (error) {
      console.error(`Failed to parse timeout configuration for server ${serverName}: ${error}`);
    }

    const result = await connection.client.request(
      {
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: toolArguments,
        },
      },
      CallToolResultSchema,
      {
        timeout,
      },
    );

    return {
      ...result,
      content: result.content ?? [],
    };
  }

  /**
   * RPC variant of toggleToolAutoApprove that returns the updated servers instead of notifying the webview
   * @param serverName The name of the MCP server
   * @param toolNames Array of tool names to toggle auto-approve for
   * @param shouldAllow Whether to enable or disable auto-approve
   * @returns Array of updated MCP servers
   */
  async toggleToolAutoApproveRPC(serverName: string, toolNames: string[], shouldAllow: boolean): Promise<McpServer[]> {
    try {
      const settingsPath = await this.createMcpSettingsFileIfNeeded();
      const content = await fs.readFile(settingsPath, 'utf-8');
      const config = JSON.parse(content);

      // Initialize autoApprove if it doesn't exist
      if (!config.mcpServers[serverName].autoApprove) {
        config.mcpServers[serverName].autoApprove = [];
      }

      const autoApprove = config.mcpServers[serverName].autoApprove;
      for (const toolName of toolNames) {
        const toolIndex = autoApprove.indexOf(toolName);

        if (shouldAllow && toolIndex === -1) {
          // Add tool to autoApprove list
          autoApprove.push(toolName);
        } else if (!shouldAllow && toolIndex !== -1) {
          // Remove tool from autoApprove list
          autoApprove.splice(toolIndex, 1);
        }
      }

      await fs.writeFile(settingsPath, JSON.stringify(config, null, 2));

      // Update the tools list to reflect the change
      const connection = this.connections.find((conn) => conn.server.name === serverName);
      if (connection?.server.tools) {
        // Update the autoApprove property of each tool in the in-memory server object
        connection.server.tools = connection.server.tools.map((tool) => ({
          ...tool,
          autoApprove: autoApprove.includes(tool.name),
        }));
      }

      // Return sorted servers without notifying webview
      const serverOrder = Object.keys(config.mcpServers || {});
      return this.getSortedMcpServers(serverOrder);
    } catch (error) {
      console.error('Failed to update autoApprove settings:', error);
      throw error; // Re-throw to ensure the error is properly handled
    }
  }

  async toggleToolAutoApprove(serverName: string, toolNames: string[], shouldAllow: boolean): Promise<void> {
    try {
      const settingsPath = await this.createMcpSettingsFileIfNeeded();
      const content = await fs.readFile(settingsPath, 'utf-8');
      const config = JSON.parse(content);

      // Initialize autoApprove if it doesn't exist
      if (!config.mcpServers[serverName].autoApprove) {
        config.mcpServers[serverName].autoApprove = [];
      }

      const autoApprove = config.mcpServers[serverName].autoApprove;
      for (const toolName of toolNames) {
        const toolIndex = autoApprove.indexOf(toolName);

        if (shouldAllow && toolIndex === -1) {
          // Add tool to autoApprove list
          autoApprove.push(toolName);
        } else if (!shouldAllow && toolIndex !== -1) {
          // Remove tool from autoApprove list
          autoApprove.splice(toolIndex, 1);
        }
      }

      await fs.writeFile(settingsPath, JSON.stringify(config, null, 2));

      // Update the tools list to reflect the change
      const connection = this.connections.find((conn) => conn.server.name === serverName);
      if (connection?.server.tools) {
        // Update the autoApprove property of each tool in the in-memory server object
        connection.server.tools = connection.server.tools.map((tool) => ({
          ...tool,
          autoApprove: autoApprove.includes(tool.name),
        }));
        await this.notifyWebviewOfServerChanges();
      }
    } catch (error) {
      console.error('Failed to update autoApprove settings:', error);
      vscode.window.showErrorMessage('Failed to update autoApprove settings');
      throw error; // Re-throw to ensure the error is properly handled
    }
  }

  public async addRemoteServer(serverName: string, serverUrl: string): Promise<McpServer[]> {
    try {
      const settings = await this.readAndValidateMcpSettingsFile();
      if (!settings) {
        throw new Error('Failed to read MCP settings');
      }

      if (settings.mcpServers[serverName]) {
        throw new Error(`An MCP server with the name "${serverName}" already exists`);
      }

      const urlValidation = z.string().url().safeParse(serverUrl);
      if (!urlValidation.success) {
        throw new Error(`Invalid server URL: ${serverUrl}. Please provide a valid URL.`);
      }

      const serverConfig = {
        url: serverUrl,
        disabled: false,
        autoApprove: [],
      };

      const parsedConfig = ServerConfigSchema.parse(serverConfig);

      settings.mcpServers[serverName] = parsedConfig;
      const settingsPath = await this.createMcpSettingsFileIfNeeded();

      // We don't write the zod-transformed version to the file.
      // The above parse() call adds the transportType field to the server config
      // It would be fine if this was written, but we don't want to clutter up the file with internal details

      // ToDo: We could benefit from input / output types reflecting the non-transformed / transformed versions
      await fs.writeFile(
        settingsPath,
        JSON.stringify({ mcpServers: { ...settings.mcpServers, [serverName]: serverConfig } }, null, 2),
      );

      await this.updateServerConnectionsRPC(settings.mcpServers);

      const serverOrder = Object.keys(settings.mcpServers || {});
      return this.getSortedMcpServers(serverOrder);
    } catch (error) {
      console.error('Failed to add remote MCP server:', error);
      throw error;
    }
  }

  /**
   * RPC variant of deleteServer that returns the updated server list directly
   * @param serverName The name of the server to delete
   * @returns Array of remaining MCP servers
   */
  public async deleteServerRPC(serverName: string): Promise<McpServer[]> {
    try {
      const settingsPath = await this.createMcpSettingsFileIfNeeded();
      const content = await fs.readFile(settingsPath, 'utf-8');
      const config = JSON.parse(content);
      if (!config.mcpServers || typeof config.mcpServers !== 'object') {
        config.mcpServers = {};
      }

      if (config.mcpServers[serverName]) {
        delete config.mcpServers[serverName];
        const updatedConfig = {
          mcpServers: config.mcpServers,
        };
        await fs.writeFile(settingsPath, JSON.stringify(updatedConfig, null, 2));
        await this.updateServerConnectionsRPC(config.mcpServers);

        // Get the servers in their correct order from settings
        const serverOrder = Object.keys(config.mcpServers || {});
        return this.getSortedMcpServers(serverOrder);
      }
      throw new Error(`${serverName} not found in MCP configuration`);
    } catch (error) {
      console.error(`Failed to delete MCP server: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  public async updateServerTimeoutRPC(serverName: string, timeout: number): Promise<McpServer[]> {
    try {
      // Validate timeout against schema
      const setConfigResult = BaseConfigSchema.shape.timeout.safeParse(timeout);
      if (!setConfigResult.success) {
        throw new Error(`Invalid timeout value: ${timeout}. Must be at minimum ${MIN_MCP_TIMEOUT_SECONDS} seconds.`);
      }

      const settingsPath = await this.createMcpSettingsFileIfNeeded();
      const content = await fs.readFile(settingsPath, 'utf-8');
      const config = JSON.parse(content);

      if (!config.mcpServers?.[serverName]) {
        throw new Error(`Server "${serverName}" not found in settings`);
      }

      config.mcpServers[serverName] = {
        ...config.mcpServers[serverName],
        timeout,
      };

      await fs.writeFile(settingsPath, JSON.stringify(config, null, 2));

      await this.updateServerConnectionsRPC(config.mcpServers);

      const serverOrder = Object.keys(config.mcpServers || {});
      return this.getSortedMcpServers(serverOrder);
    } catch (error) {
      console.error('Failed to update server timeout:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
      }
      vscode.window.showErrorMessage(
        `Failed to update server timeout: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  private _formatSchemaTypeToDataType(schemaType: any) {
    if (schemaType === 'string') {
      return DataType.String;
    }
    if (schemaType === 'number') {
      return DataType.Number;
    }
    if (schemaType === 'integer') {
      return DataType.Integer;
    }
    if (schemaType === 'boolean') {
      return DataType.Boolean;
    }
    if (schemaType === 'array') {
      return DataType.Array;
    }
    if (schemaType === 'object') {
      return DataType.Object;
    }
    if (schemaType === 'null') {
      return DataType.Null;
    }
    return schemaType;
  }

  public formatToToolInfos(): Tool[] {
    return this.getServers()
      .filter((server) => server.status === 'connected')
      .flatMap((server) => {
        const tools: Tool[] = (server.tools || []).map((tool) => {
          const params: Record<string, ParameterInfo> = {};
          const properties = tool.inputSchema?.properties || {};
          // biome-ignore lint/complexity/noForEach: <explanation>
          // biome-ignore lint/suspicious/noExplicitAny: <explanation>
          Object.entries(properties).forEach(([key, schema]: [string, any]) => {
            params[key] = {
              type: this._formatSchemaTypeToDataType(schema.type),
              desc: schema.description || '',
              required: tool.inputSchema.required?.includes(key) || false,
            };
          });

          const toolInfo: ToolInfo = {
            name: `mcp_${server.name}_${tool.name}`,
            desc: tool.description || '',
            params,
          };

          const result: Tool = {
            enable() {
              return true;
            },
            getInfo() {
              return toolInfo;
            },
            run: async (
              inputStr: string,
              _toolId: string,
              _conversationId: string,
              _version: Int64,
              _progressCallback?: ToolProgressCallback,
            ) => {
              const input = JSON.parse(inputStr);
              const mcp_arguments: string | undefined = input.arguments;
              let parsedArguments: Record<string, unknown> | undefined;
              if (mcp_arguments) {
                try {
                  parsedArguments = JSON.parse(mcp_arguments);
                } catch (error) {
                  console.warn('Failed to parse arguments:', error);
                  // return "tried to use use_mcp_tool without value for required parameter 'arguments'. Retrying...";
                }
              }
              const toolResult = await this.callTool(server.name, tool.name, parsedArguments);

              const toolResultText =
                (toolResult?.isError ? 'Error:\n' : '') +
                  toolResult?.content
                    .map((item) => {
                      if (item.type === 'text') {
                        return item.text;
                      }
                      if (item.type === 'resource') {
                        const { blob, ...rest } = item.resource;
                        return JSON.stringify(rest, null, 2);
                      }
                      return '';
                    })
                    .filter(Boolean)
                    .join('\n\n') || '(No response)';

              return new ToolResult(
                toolResult?.isError ? ToolResultStatus.AutoRunError : ToolResultStatus.AutoRunSuccess,
                toolResultText,
              );
            },
          };
          return result;
        });

        return tools;
      });
  }

  async dispose(): Promise<void> {
    this.removeAllFileWatchers();
    for (const connection of this.connections) {
      try {
        await this.deleteConnection(connection.server.name);
      } catch (error) {
        console.error(`Failed to close connection for ${connection.server.name}:`, error);
      }
    }
    this.connections = [];
    if (this.settingsWatcher) {
      this.settingsWatcher.dispose();
    }
    for (const d of this.disposables) {
      d.dispose();
    }
  }
}
