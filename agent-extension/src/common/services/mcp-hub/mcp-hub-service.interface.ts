import type { Tool } from '@/tools/base';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { McpConnection, McpResourceResponse, McpServer, McpToolCallResponse } from './common';

export interface IMcpHubService {
  _serviceBrand: undefined;

  getServers: () => McpServer[];

  isConnecting: boolean;

  connections: McpConnection[];

  callTool: (
    serverName: string,
    toolName: string,
    toolArguments?: Record<string, unknown>,
  ) => Promise<McpToolCallResponse>;

  readResource: (serverName: string, uri: string) => Promise<McpResourceResponse>;

  getMcpServersPath: () => Promise<string>;

  settingsFilePath: string;

  createMcpSettingsFileIfNeeded: () => Promise<string>;

  formatToToolInfos: () => Tool[];

  sendLatestMcpServers: () => Promise<void>;
}

export const IMcpHubService = createDecorator<IMcpHubService>('mcp-hub-service');
