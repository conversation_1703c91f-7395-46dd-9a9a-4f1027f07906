import { describe, it, expect, beforeEach } from 'vitest';
import { NewFileContentConstructor } from './new-file-content-constructor';

describe('NewFileContentConstructor', () => {
  let originalContent: string;

  beforeEach(() => {
    originalContent = `line 1
line 2
line 3
line 4
`;
  });

  const processLines = (fileContentConstructor: NewFileContentConstructor, lines: string[]) => {
    for (const line of lines) {
      fileContentConstructor.processLine(line);
    }
  };

  it('should perform a simple search and replace', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<<<<<< SEARCH', 'line 2', 'line 3', '=======', 'new line 2', 'new line 3', '>>>>>>> REPLACE'];
    processLines(fileContentConstructor, diff);
    const result = fileContentConstructor.getResult();
    expect(result).toBe('line 1\nnew line 2\nnew line 3\nline 4\n');
  });

  it('should handle insertion into an empty file', () => {
    const fileContentConstructor = new NewFileContentConstructor('', true); // Empty original content
    const diff = ['<<<<<<< SEARCH', '=======', 'inserted line', '>>>>>>> REPLACE'];
    processLines(fileContentConstructor, diff);
    const result = fileContentConstructor.getResult();
    expect(result).toBe('inserted line\n');
  });

  it('should replace the entire file when search block is empty on a non-empty file', () => {
    // Note: This is a special case interpretation. The class treats empty SEARCH
    // on a non-empty file as a full replacement.
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<<<<<< SEARCH', '=======', 'completely new file', '>>>>>>> REPLACE'];
    processLines(fileContentConstructor, diff);
    const result = fileContentConstructor.getResult();
    expect(result).toBe('completely new file\n');
  });

  it('should use line-trimmed fallback match', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<<<<<< SEARCH', '  line 2  ', '    line 3', '=======', 'replaced with trim', '>>>>>>> REPLACE'];
    processLines(fileContentConstructor, diff);
    const result = fileContentConstructor.getResult();
    expect(result).toBe('line 1\nreplaced with trim\nline 4\n');
  });

  it('should use block-anchor fallback match', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = [
      '<<<<<<< SEARCH',
      'line 2',
      'something else',
      'line 4',
      '=======',
      'replaced with anchor',
      '>>>>>>> REPLACE',
    ];
    processLines(fileContentConstructor, diff);
    const result = fileContentConstructor.getResult();
    expect(result).toBe('line 1\nreplaced with anchor\n');
  });

  it('should throw an error if no match is found', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<<<<<< SEARCH', 'non-existent line', '=======', 'wont matter', '>>>>>>> REPLACE'];
    expect(() => processLines(fileContentConstructor, diff)).toThrow(/does not match anything in the file/);
  });

  it('should throw an error for invalid block structure', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['=======', '>>>>>>> REPLACE']; // Missing SEARCH
    expect(() => processLines(fileContentConstructor, diff)).toThrow(/Invalid SEARCH\/REPLACE block structure/);
  });

  it('should throw an error if finalization occurs while still processing', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<<<<<< SEARCH', 'line 2']; // Incomplete block
    processLines(fileContentConstructor, diff);
    expect(() => fileContentConstructor.getResult()).toThrow(/File processing incomplete/);
  });

  it('should process multiple blocks correctly', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = [
      '<<<<<<< SEARCH',
      'line 2',
      '=======',
      'two',
      '>>>>>>> REPLACE',
      '<<<<<<< SEARCH',
      'line 4',
      '=======',
      'four',
      '>>>>>>> REPLACE',
    ];
    processLines(fileContentConstructor, diff);
    const result = fileContentConstructor.getResult();
    expect(result).toBe('line 1\ntwo\nline 3\nfour\n');
  });
});
describe('NewFileContentConstructor - Malformed Block Recovery', () => {
  let originalContent: string;

  beforeEach(() => {
    originalContent = `line 1
line 2
line 3
`;
  });

  const processLines = (fileContentConstructor: NewFileContentConstructor, lines: string[]) => {
    for (const line of lines) {
      fileContentConstructor.processLine(line);
    }
  };

  it('should auto-fix a malformed SEARCH tag', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<< SEARCH', 'line 2', '=======', 'new line 2', '>>>>>>> REPLACE'];
    processLines(fileContentConstructor, diff);
    expect(fileContentConstructor.getResult()).toBe('line 1\nnew line 2\nline 3\n');
  });

  it('should auto-fix a malformed SEARCH tag when separator is found', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    // Here, '<<< SEARCH' and 'line 2' are treated as pending non-standard lines.
    // When '=======' is encountered, the constructor looks back and fixes the block.
    const diff = ['<<< SEARCH', 'line 2', '=======', 'new line 2', '>>>>>>> REPLACE'];
    processLines(fileContentConstructor, diff);
    expect(fileContentConstructor.getResult()).toBe('line 1\nnew line 2\nline 3\n');
  });

  it('should throw an error for a malformed separator inside a search block', () => {
    const fileContentConstructor = new NewFileContentConstructor(originalContent, true);
    const diff = ['<<<<<<< SEARCH', 'line 2', '====', 'new line 2', '>>>>>>> REPLACE'];
    // The '====' is consumed as part of the search string. When '>>>>>>> REPLACE' arrives,
    // it triggers tryFixReplaceBlock which fails because there are no pending lines to fix.
    expect(() => processLines(fileContentConstructor, diff)).toThrow();
  });
});
