import { describe, it, expect } from 'vitest';
import { blockAnchorFallbackMatch } from './block-anchor-fallback-match';

describe('blockAnchorFallbackMatch', () => {
  const originalContent = `
line 1
line 2
line 3
line 4
line 5
line 6
line 7
line 8
  `;

  it('should find a match when start and end anchors are correct', () => {
    const searchContent = `
line 3
something different
line 5
    `.trim();
    // Expected match: "line 3\nline 4\nline 5"
    // Calculation: (line1 + \n) + (line2 + \n) = (7) + (7) = 14
    // Start index is after the second newline
    const expectedStartIndex = 15;
    // End index: 15 + (line3 + \n) + (line4 + \n) + (line5 + \n) = 15 + 7 + 7 + 7 = 36
    const expectedEndIndex = 36;
    expect(blockAnchorFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });

  it('should return false if the block is not found', () => {
    const searchContent = `
line 3
something different
line 6
    `.trim();
    expect(blockAnchorFallbackMatch(originalContent, searchContent, 0)).toBe(false);
  });

  it('should return false for blocks with less than 3 lines', () => {
    const searchContent = 'line 1\nline 2';
    expect(blockAnchorFallbackMatch(originalContent, searchContent, 0)).toBe(false);
  });

  it('should handle search content with trailing newline', () => {
    const searchContent = 'line 3\nline 4\nline 5\n';
    const expectedStartIndex = 15;
    const expectedEndIndex = 36;
    expect(blockAnchorFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });

  it('should respect the startIndex parameter', () => {
    const repeatedContent = `
line 1
line 2
line 3
line 1
line 2
    `;
    const searchContent = 'line 1\nsome content\nline 3';
    // First match starts at index 1. We want to start searching after it.
    // (line1+\n) = 7. Start after index 7.
    expect(blockAnchorFallbackMatch(repeatedContent, searchContent, 8)).toBe(false); // No second match
  });

  it('should match correctly even with different whitespace', () => {
    const searchContent = '  line 3  \n  different content\n  line 5  ';
    const expectedStartIndex = 15;
    const expectedEndIndex = 36;
    expect(blockAnchorFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });
});
