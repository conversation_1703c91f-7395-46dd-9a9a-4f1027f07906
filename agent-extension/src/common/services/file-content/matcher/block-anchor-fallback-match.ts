/**
 * Attempts to match blocks of code by using the first and last lines as anchors.
 * This is a third-tier fallback strategy that helps match blocks where we can identify
 * the correct location by matching the beginning and end, even if the exact content
 * differs slightly.
 *
 * The matching strategy:
 * 1. Only attempts to match blocks of 3 or more lines to avoid false positives
 * 2. Extracts from the search content:
 *    - First line as the "start anchor"
 *    - Last line as the "end anchor"
 * 3. For each position in the original content:
 *    - Checks if the next line matches the start anchor
 *    - If it does, jumps ahead by the search block size
 *    - Checks if that line matches the end anchor
 *    - All comparisons are done after trimming whitespace
 *
 * This approach is particularly useful for matching blocks of code where:
 * - The exact content might have minor differences
 * - The beginning and end of the block are distinctive enough to serve as anchors
 * - The overall structure (number of lines) remains the same
 *
 * @param originalContent - The full content of the original file
 * @param searchContent - The content we're trying to find in the original file
 * @param startIndex - The character index in originalContent where to start searching
 * @returns A tuple of [startIndex, endIndex] if a match is found, false otherwise
 */
export function blockAnchorFallbackMatch(
  originalContent: string,
  searchContent: string,
  startIndex: number,
): [number, number] | false {
  const originalLines = originalContent.split('\n');
  const searchLines = searchContent.split('\n');

  // Only use this approach for blocks of 3+ lines
  if (searchLines.length < 3) {
    return false;
  }

  // Trim trailing empty line if exists
  if (searchLines[searchLines.length - 1] === '') {
    searchLines.pop();
  }

  const firstLineSearch = searchLines[0].trim();
  const lastLineSearch = searchLines[searchLines.length - 1].trim();
  const searchBlockSize = searchLines.length;

  // Find the line number where startIndex falls
  let startLineNum = 0;
  let currentIndex = 0;
  while (currentIndex < startIndex && startLineNum < originalLines.length) {
    currentIndex += originalLines[startLineNum].length + 1;
    startLineNum++;
  }

  // Look for matching start and end anchors
  for (let i = startLineNum; i <= originalLines.length - searchBlockSize; i++) {
    // Check if first line matches
    if (originalLines[i].trim() !== firstLineSearch) {
      continue;
    }

    // Check if last line matches at the expected position
    if (originalLines[i + searchBlockSize - 1].trim() !== lastLineSearch) {
      continue;
    }

    // Calculate exact character positions
    let matchStartIndex = 0;
    for (let k = 0; k < i; k++) {
      matchStartIndex += originalLines[k].length + 1;
    }

    let matchEndIndex = matchStartIndex;
    for (let k = 0; k < searchBlockSize; k++) {
      matchEndIndex += originalLines[i + k].length + 1;
    }

    return [matchStartIndex, matchEndIndex];
  }

  return false;
}
