/**
 * Attempts a line-trimmed fallback match for the given search content in the original content.
 * It tries to match `searchContent` lines against a block of lines in `originalContent` starting
 * from `lastProcessedIndex`. Lines are matched by trimming leading/trailing whitespace and ensuring
 * they are identical afterwards.
 *
 * Returns [matchIndexStart, matchIndexEnd] if found, or false if not found.
 */
export function lineTrimmedFallbackMatch(
  originalContent: string,
  searchContent: string,
  startIndex: number,
): [number, number] | false {
  // Split both contents into lines
  const originalLines = originalContent.split('\n');
  const searchLines = searchContent.split('\n');

  // Trim trailing empty line if exists (from the trailing \n in searchContent)
  if (searchLines[searchLines.length - 1] === '') {
    searchLines.pop();
  }

  // Find the line number where startIndex falls
  let startLineNum = 0;
  let currentIndex = 0;
  while (currentIndex < startIndex && startLineNum < originalLines.length) {
    currentIndex += originalLines[startLineNum].length + 1; // +1 for \n
    startLineNum++;
  }

  // For each possible starting position in original content
  for (let i = startLineNum; i <= originalLines.length - searchLines.length; i++) {
    let matches = true;

    // Try to match all search lines from this position
    for (let j = 0; j < searchLines.length; j++) {
      const originalTrimmed = originalLines[i + j].trim();
      const searchTrimmed = searchLines[j].trim();

      if (originalTrimmed !== searchTrimmed) {
        matches = false;
        break;
      }
    }

    // If we found a match, calculate the exact character positions
    if (matches) {
      // Find start character index
      let matchStartIndex = 0;
      for (let k = 0; k < i; k++) {
        matchStartIndex += originalLines[k].length + 1; // +1 for \n
      }

      // Find end character index
      let matchEndIndex = matchStartIndex;
      for (let k = 0; k < searchLines.length; k++) {
        matchEndIndex += originalLines[i + k].length + 1; // +1 for \n
      }

      return [matchStartIndex, matchEndIndex];
    }
  }

  return false;
}
