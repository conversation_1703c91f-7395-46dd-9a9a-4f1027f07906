import { describe, it, expect } from 'vitest';
import { lineTrimmedFallbackMatch } from './line-trimmed-fallback-match';

describe('lineTrimmedFallbackMatch', () => {
  const originalContent = `
    line 1
      line 2
    line 3
  `;

  it('should find a match when content is identical but whitespace differs', () => {
    const searchContent = `
line 1
line 2
line 3
    `.trim();
    // Expected match: "    line 1\n      line 2\n    line 3\n"
    // Calculation: start after the first newline
    const expectedStartIndex = 1;
    // End index: 1 + (11 for line 1) + (13 for line 2) + (11 for line 3) = 36
    const expectedEndIndex = 36;
    expect(lineTrimmedFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });

  it('should return false if the content does not match', () => {
    const searchContent = 'line 1\nline non-existent\nline 3';
    expect(lineTrimmedFallbackMatch(originalContent, searchContent, 0)).toBe(false);
  });

  it('should find a match with exact content', () => {
    const searchContent = `
    line 1
      line 2
    line 3
    `.trim();
    const expectedStartIndex = 1;
    const expectedEndIndex = 36;
    expect(lineTrimmedFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });

  it('should respect the startIndex parameter', () => {
    const repeatedContent = `
line A
line B
line A
line C
    `.trim();
    const searchContent = 'line A\nline C';
    // First "line A" is at index 0. We want to start searching after it.
    // "line A\nline B\n" = 7 + 7 = 14 characters.
    // The second "line A" starts at index 14.
    const expectedStartIndex = 14;
    // End index: 14 + (lineA+\n) + (lineC+\n) = 14 + 7 + 7 = 28
    const expectedEndIndex = 28;
    expect(lineTrimmedFallbackMatch(repeatedContent, searchContent, 1)).toEqual([expectedStartIndex, expectedEndIndex]);
  });

  it('should handle search content with a trailing newline', () => {
    const searchContent = 'line 1\nline 2\nline 3\n';
    const expectedStartIndex = 1;
    const expectedEndIndex = 36;
    expect(lineTrimmedFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });

  it('should correctly match a single line', () => {
    const searchContent = '  line 2  ';
    // "\n" (1) + "    line 1\n" (11) = 12. Start index is 12.
    const expectedStartIndex = 12;
    // End index: 12 + "      line 2\n" (13) = 25
    const expectedEndIndex = 25;
    expect(lineTrimmedFallbackMatch(originalContent, searchContent, 0)).toEqual([expectedStartIndex, expectedEndIndex]);
  });
});
