import { describe, it, expect, vi, beforeEach } from 'vitest';
import { FileContentService } from './file-content-service';
import { NewFileContentConstructor } from './new-file-content-constructor';

// Mock the NewFileContentConstructor class
const mockProcessLine = vi.fn();
const mockGetResult = vi.fn(() => 'mocked result');
vi.mock('./new-file-content-constructor', () => {
  return {
    NewFileContentConstructor: vi.fn().mockImplementation(() => {
      return {
        processLine: mockProcessLine,
        getResult: mockGetResult,
      };
    }),
  };
});

describe('FileContentService', () => {
  let service: FileContentService;
  const originalContent = 'original content';

  beforeEach(() => {
    vi.clearAllMocks();
    service = new FileContentService();
  });

  it('should process a standard diff content', async () => {
    const diffContent = 'line1\nline2';
    await service.constructNewFileContent(diffContent, originalContent, true);

    expect(NewFileContentConstructor).toHaveBeenCalledWith(originalContent, true);
    expect(mockProcessLine).toHaveBeenCalledTimes(2);
    expect(mockProcessLine).toHaveBeenCalledWith('line1');
    expect(mockProcessLine).toHaveBeenCalledWith('line2');
    expect(mockGetResult).toHaveBeenCalled();
  });

  it('should remove an incomplete marker line at the end', async () => {
    const diffContent = 'line1\n<<<<<<< SEARC';
    await service.constructNewFileContent(diffContent, originalContent, false);

    expect(mockProcessLine).toHaveBeenCalledTimes(1);
    expect(mockProcessLine).toHaveBeenCalledWith('line1');
    expect(mockProcessLine).not.toHaveBeenCalledWith('<<<<<<< SEARC');
  });

  it('should NOT remove a complete marker line at the end', async () => {
    const diffContent = 'line1\n>>>>>>> REPLACE';
    await service.constructNewFileContent(diffContent, originalContent, true);

    expect(mockProcessLine).toHaveBeenCalledTimes(2);
    expect(mockProcessLine).toHaveBeenCalledWith('line1');
    expect(mockProcessLine).toHaveBeenCalledWith('>>>>>>> REPLACE');
  });

  it('should NOT remove a normal line that happens to start with >', async () => {
    const diffContent = 'line1\n> blockquote\nline3';
    await service.constructNewFileContent(diffContent, originalContent, true);

    expect(mockProcessLine).toHaveBeenCalledTimes(3);
    expect(mockProcessLine).toHaveBeenCalledWith('line1');
    expect(mockProcessLine).toHaveBeenCalledWith('> blockquote');
  });

  it("should remove a partial marker but isn't recognized", async () => {
    const diffContent = 'line1\n> blockquote';
    await service.constructNewFileContent(diffContent, originalContent, true);

    expect(mockProcessLine).toHaveBeenCalledTimes(1);
    expect(mockProcessLine).toHaveBeenCalledWith('line1');
    expect(mockProcessLine).not.toHaveBeenCalledWith('> blockquote');
  });

  it('should return the result from the constructor', async () => {
    const result = await service.constructNewFileContent('', originalContent, true);
    expect(result).toBe('mocked result');
  });
});
