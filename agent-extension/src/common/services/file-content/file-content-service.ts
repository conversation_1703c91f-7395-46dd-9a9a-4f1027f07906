import { IFileContentService } from './file-content-service.interface';
import { NewFileContentConstructor } from './new-file-content-constructor';

export class FileContentService implements IFileContentService {
  public _serviceBrand: undefined;

  constructNewFileContent(diffContent: string, originalContent: string, isFinal: boolean): Promise<string> {
    const newFileContentConstructor = new NewFileContentConstructor(originalContent, isFinal);

    const lines = diffContent.split('\n');

    // If the last line looks like a partial marker but isn't recognized,
    // remove it because it might be incomplete.
    const lastLine = lines[lines.length - 1];
    if (
      lines.length > 0 &&
      (lastLine.startsWith('<') || lastLine.startsWith('=') || lastLine.startsWith('>')) &&
      lastLine !== '<<<<<<< SEARCH' &&
      lastLine !== '=======' &&
      lastLine !== '>>>>>>> REPLACE'
    ) {
      lines.pop();
    }

    for (const line of lines) {
      newFileContentConstructor.processLine(line);
    }

    const result = newFileContentConstructor.getResult();
    return Promise.resolve(result);
  }
}
