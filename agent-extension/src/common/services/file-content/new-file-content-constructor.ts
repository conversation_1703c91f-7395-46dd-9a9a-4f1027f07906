import { blockAnchorFallbackMatch } from './matcher/block-anchor-fallback-match';
import { lineTrimmedFallbackMatch } from './matcher/line-trimmed-fallback-match';

enum ProcessingState {
  Idle = 0,
  StateSearch = 1 << 0,
  StateReplace = 1 << 1,
}

export class NewFileContentConstructor {
  private originalContent: string;
  private isFinal: boolean;
  private state: number;
  private pendingNonStandardLines: string[];
  private result: string;
  private lastProcessedIndex: number;
  private currentSearchContent: string;
  private currentReplaceContent: string;
  private searchMatchIndex: number;
  private searchEndIndex: number;

  constructor(originalContent: string, isFinal: boolean) {
    this.originalContent = originalContent;
    this.isFinal = isFinal;
    this.pendingNonStandardLines = [];
    this.result = '';
    this.lastProcessedIndex = 0;
    this.state = ProcessingState.Idle;
    this.currentSearchContent = '';
    this.currentReplaceContent = '';
    this.searchMatchIndex = -1;
    this.searchEndIndex = -1;
  }

  private resetForNextBlock() {
    // Reset for next block
    this.state = ProcessingState.Idle;
    this.currentSearchContent = '';
    this.currentReplaceContent = '';
    this.searchMatchIndex = -1;
    this.searchEndIndex = -1;
  }

  private findLastMatchingLineIndex(regx: RegExp, lineLimit: number) {
    for (let i = lineLimit; i > 0; ) {
      i--;
      if (this.pendingNonStandardLines[i].match(regx)) {
        return i;
      }
    }
    return -1;
  }

  private updateProcessingState(newState: ProcessingState) {
    const isValidTransition =
      (this.state === ProcessingState.Idle && newState === ProcessingState.StateSearch) ||
      (this.state === ProcessingState.StateSearch && newState === ProcessingState.StateReplace);

    if (!isValidTransition) {
      throw new Error(
        'Invalid state transition.\n' +
          'Valid transitions are:\n' +
          '- Idle → StateSearch\n' +
          '- StateSearch → StateReplace',
      );
    }

    this.state |= newState;
  }

  private isStateActive(state: ProcessingState): boolean {
    return (this.state & state) === state;
  }

  private activateReplaceState() {
    this.updateProcessingState(ProcessingState.StateReplace);
  }

  private activateSearchState() {
    this.updateProcessingState(ProcessingState.StateSearch);
    this.currentSearchContent = '';
    this.currentReplaceContent = '';
  }

  private isSearchingActive(): boolean {
    return this.isStateActive(ProcessingState.StateSearch);
  }

  private isReplacingActive(): boolean {
    return this.isStateActive(ProcessingState.StateReplace);
  }

  private hasPendingNonStandardLines(pendingNonStandardLineLimit: number): boolean {
    return this.pendingNonStandardLines.length - pendingNonStandardLineLimit < this.pendingNonStandardLines.length;
  }

  public processLine(line: string) {
    this.internalProcessLine(line, true, this.pendingNonStandardLines.length);
  }

  public getResult() {
    // If this is the final chunk, append any remaining original content
    if (this.isFinal && this.lastProcessedIndex < this.originalContent.length) {
      this.result += this.originalContent.slice(this.lastProcessedIndex);
    }
    if (this.isFinal && this.state !== ProcessingState.Idle) {
      throw new Error('File processing incomplete - SEARCH/REPLACE operations still active during finalization');
    }
    return this.result;
  }

  private internalProcessLine(
    line: string,
    canWritependingNonStandardLines: boolean,
    pendingNonStandardLineLimit: number,
  ): number {
    let removeLineCount = 0;
    if (line === '<<<<<<< SEARCH') {
      removeLineCount = this.trimPendingNonStandardTrailingEmptyLines(pendingNonStandardLineLimit);
      if (removeLineCount > 0) {
        // biome-ignore lint/style/noParameterAssign: 参考代码
        pendingNonStandardLineLimit = pendingNonStandardLineLimit - removeLineCount;
      }
      if (this.hasPendingNonStandardLines(pendingNonStandardLineLimit)) {
        this.tryFixSearchReplaceBlock(pendingNonStandardLineLimit);
        // biome-ignore lint/suspicious/noAssignInExpressions: 参考代码
        canWritependingNonStandardLines && (this.pendingNonStandardLines.length = 0);
      }
      this.activateSearchState();
    } else if (line === '=======') {
      // 校验非标内容
      if (!this.isSearchingActive()) {
        this.tryFixSearchBlock(pendingNonStandardLineLimit);
        // biome-ignore lint/suspicious/noAssignInExpressions: 参考代码
        canWritependingNonStandardLines && (this.pendingNonStandardLines.length = 0);
      }
      this.activateReplaceState();
      this.beforeReplace();
    } else if (line === '>>>>>>> REPLACE') {
      if (!this.isReplacingActive()) {
        this.tryFixReplaceBlock(pendingNonStandardLineLimit);
        // biome-ignore lint/suspicious/noAssignInExpressions: 参考代码
        canWritependingNonStandardLines && (this.pendingNonStandardLines.length = 0);
      }
      this.lastProcessedIndex = this.searchEndIndex;
      this.resetForNextBlock();
    } else {
      // Accumulate content for search or replace
      // (currentReplaceContent is not being used for anything right now since we directly append to result.)
      // (We artificially add a linebreak since we split on \n at the beginning. In order to not include a trailing linebreak in the final search/result blocks we need to remove it before using them. This allows for partial line matches to be correctly identified.)
      // NOTE: search/replace blocks must be arranged in the order they appear in the file due to how we build the content using lastProcessedIndex. We also cannot strip the trailing newline since for non-partial lines it would remove the linebreak from the original content. (If we remove end linebreak from search, then we'd also have to remove it from replace but we can't know if it's a partial line or not since the model may be using the line break to indicate the end of the block rather than as part of the search content.) We require the model to output full lines in order for our fallbacks to work as well.
      if (this.isReplacingActive()) {
        this.currentReplaceContent += `${line}\n`;
        // Output replacement lines immediately if we know the insertion point
        if (this.searchMatchIndex !== -1) {
          this.result += `${line}\n`;
        }
      } else if (this.isSearchingActive()) {
        this.currentSearchContent += `${line}\n`;
      } else {
        const appendToPendingNonStandardLines = canWritependingNonStandardLines;
        if (appendToPendingNonStandardLines) {
          console.log(`unstandard line:${line}`);
          // 处理非标内容
          this.pendingNonStandardLines.push(line);
        }
      }
    }
    return removeLineCount;
  }

  private beforeReplace() {
    // Remove trailing linebreak for adding the === marker
    // if (currentSearchContent.endsWith("\r\n")) {
    // 	currentSearchContent = currentSearchContent.slice(0, -2)
    // } else if (currentSearchContent.endsWith("\n")) {
    // 	currentSearchContent = currentSearchContent.slice(0, -1)
    // }

    if (!this.currentSearchContent) {
      // Empty search block
      if (this.originalContent.length === 0) {
        // New file scenario: nothing to match, just start inserting
        this.searchMatchIndex = 0;
        this.searchEndIndex = 0;
      } else {
        // Complete file replacement scenario: treat the entire file as matched
        this.searchMatchIndex = 0;
        this.searchEndIndex = this.originalContent.length;
      }
    } else {
      // Add check for inefficient full-file search
      // if (currentSearchContent.trim() === originalContent.trim()) {
      // 	throw new Error(
      // 		"The SEARCH block contains the entire file content. Please either:\n" +
      // 			"1. Use an empty SEARCH block to replace the entire file, or\n" +
      // 			"2. Make focused changes to specific parts of the file that need modification.",
      // 	)
      // }
      // Exact search match scenario
      const exactIndex = this.originalContent.indexOf(this.currentSearchContent, this.lastProcessedIndex);
      if (exactIndex !== -1) {
        this.searchMatchIndex = exactIndex;
        this.searchEndIndex = exactIndex + this.currentSearchContent.length;
      } else {
        // Attempt fallback line-trimmed matching
        const lineMatch = lineTrimmedFallbackMatch(
          this.originalContent,
          this.currentSearchContent,
          this.lastProcessedIndex,
        );
        if (lineMatch) {
          [this.searchMatchIndex, this.searchEndIndex] = lineMatch;
        } else {
          // Try block anchor fallback for larger blocks
          const blockMatch = blockAnchorFallbackMatch(
            this.originalContent,
            this.currentSearchContent,
            this.lastProcessedIndex,
          );
          if (blockMatch) {
            [this.searchMatchIndex, this.searchEndIndex] = blockMatch;
          } else {
            throw new Error(
              `The SEARCH block:\n${this.currentSearchContent.trimEnd()}\n... does not match anything in the file. Please re-read the file before making modifications.`,
            );
          }
        }
      }
    }
    if (this.searchMatchIndex < this.lastProcessedIndex) {
      throw new Error(
        `The SEARCH block:\n${this.currentSearchContent.trimEnd()}\n...matched an incorrect content in the file.`,
      );
    }
    // Output everything up to the match location
    this.result += this.originalContent.slice(this.lastProcessedIndex, this.searchMatchIndex);
  }

  private tryFixSearchBlock(lineLimit: number): number {
    let removeLineCount = 0;
    if (lineLimit < 0) {
      // biome-ignore lint/style/noParameterAssign: 参考代码
      lineLimit = this.pendingNonStandardLines.length;
    }
    if (!lineLimit) {
      throw new Error('Invalid SEARCH/REPLACE block structure - no lines available to process');
    }
    const searchTagRegexp = /^[<]{3,} SEARCH$/;
    const searchTagIndex = this.findLastMatchingLineIndex(searchTagRegexp, lineLimit);
    if (searchTagIndex !== -1) {
      const fixLines = this.pendingNonStandardLines.slice(searchTagIndex, lineLimit);
      fixLines[0] = '<<<<<<< SEARCH';
      for (const line of fixLines) {
        removeLineCount += this.internalProcessLine(line, false, searchTagIndex);
      }
    } else {
      throw new Error(
        `Invalid REPLACE marker detected - could not find matching SEARCH block starting from line ${searchTagIndex + 1}`,
      );
    }
    return removeLineCount;
  }

  private tryFixReplaceBlock(lineLimit: number): number {
    let removeLineCount = 0;
    if (lineLimit < 0) {
      // biome-ignore lint/style/noParameterAssign: 参考代码
      lineLimit = this.pendingNonStandardLines.length;
    }
    if (!lineLimit) {
      throw new Error();
    }
    const replaceBeginTagRegexp = /^[=]{3,}$/;
    const replaceBeginTagIndex = this.findLastMatchingLineIndex(replaceBeginTagRegexp, lineLimit);
    if (replaceBeginTagIndex !== -1) {
      // // 校验非标内容
      // if (!this.isSearchingActive()) {
      // 	removeLineCount += this.tryFixSearchBlock(replaceBeginTagIndex)
      // }
      const fixLines = this.pendingNonStandardLines.slice(
        replaceBeginTagIndex - removeLineCount,
        lineLimit - removeLineCount,
      );
      fixLines[0] = '=======';
      for (const line of fixLines) {
        removeLineCount += this.internalProcessLine(line, false, replaceBeginTagIndex - removeLineCount);
      }
    } else {
      throw new Error(`Malformed REPLACE block - missing valid separator after line ${replaceBeginTagIndex + 1}`);
    }
    return removeLineCount;
  }

  private tryFixSearchReplaceBlock(lineLimit: number): number {
    let removeLineCount = 0;
    if (lineLimit < 0) {
      // biome-ignore lint/style/noParameterAssign: 参考代码
      lineLimit = this.pendingNonStandardLines.length;
    }
    if (!lineLimit) {
      throw new Error();
    }

    const replaceEndTagRegexp = /^[>]{3,} REPLACE$/;
    const replaceEndTagIndex = this.findLastMatchingLineIndex(replaceEndTagRegexp, lineLimit);
    const likeReplaceEndTag = replaceEndTagIndex === lineLimit - 1;
    if (likeReplaceEndTag) {
      // // 校验非标内容
      // if (!this.isReplacingActive()) {
      // 	removeLineCount += this.tryFixReplaceBlock(replaceEndTagIndex)
      // }
      const fixLines = this.pendingNonStandardLines.slice(
        replaceEndTagIndex - removeLineCount,
        lineLimit - removeLineCount,
      );
      fixLines[fixLines.length - 1] = '>>>>>>> REPLACE';
      for (const line of fixLines) {
        removeLineCount += this.internalProcessLine(line, false, replaceEndTagIndex - removeLineCount);
      }
    } else {
      throw new Error('Malformed SEARCH/REPLACE block structure: Missing valid closing REPLACE marker');
    }
    return removeLineCount;
  }

  /**
   * Removes trailing empty lines from the pendingNonStandardLines array
   * @param lineLimit - The index to start checking from (exclusive).
   *                    Removes empty lines from lineLimit-1 backwards.
   * @returns The number of empty lines removed
   */
  private trimPendingNonStandardTrailingEmptyLines(lineLimit: number): number {
    let removedCount = 0;
    let i = Math.min(lineLimit, this.pendingNonStandardLines.length) - 1;

    while (i >= 0 && this.pendingNonStandardLines[i].trim() === '') {
      this.pendingNonStandardLines.pop();
      removedCount++;
      i--;
    }

    return removedCount;
  }
}
