import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type ILvErrorOr, makeError<PERSON>y, makeOk, makeOkWith } from '@byted-image/lv-bedrock/error';
import { Emitter } from '@byted-image/lv-bedrock/event';

import { AgentId } from '@/common/services/conversation/const';
import { type ClientMessage } from '@/conversation/client-message/abstract-message';

import type { base, Int64 } from '@/bam';
import type { CreateConversationRequest } from '@/bam/namespaces/agw';
import { ConversationType } from '@/bam/namespaces/conversation';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { Disposable } from '@byted-image/lv-bedrock/dispose';
import { AskMessage } from '../base-chat/types';
import type { ToolProgressEvent } from '../conversation/base-conversation-context/messages-manager';
import { CodingConversationContext } from '../conversation/coding-conversation-context/coding-conversation-context';
import { IConversationHistoryService } from '../conversation/history/conversation-history.interface';
import { IFileLoggerService } from '../file-logger/file-logger-service.interface';
import { INetworkClientFactoryService } from '../network-client-factory/network-client-factory-service.interface';
import { ToolProgressManager } from '../tool-progress/tool-progress-manager';
import type { ICodingChatService } from './coding-chat-service.interface';
import type { ConversationStatus } from '../conversation/base-conversation-service';
import { DEFAULT_WORK_MODE } from '@/common/constants/storage';

interface IConversationContextState {
  cid: string;
  parentCid?: string;
  parentMessageVersion?: Int64;
  messages: ClientMessage[];
  childrenContexts: IConversationContextState[];
}

interface IMessageChangeInfo {
  messages: ClientMessage[];
  type: 'add' | 'update';
  cid: string;
  parentCid?: string;
  parentMessageVersion?: Int64;
  /** 当前会话是在进行中，还是已经结束 */
  conversationStatus: ConversationStatus;
}

export class CodingChatService extends Disposable implements ICodingChatService {
  private readonly _conversationType = ConversationTypeEnum.Coding;
  static readonly AgentId = AgentId.Coding;
  public _serviceBrand: undefined;

  public conversationSwitching = false;

  public readonly _onUpdate = new Emitter<[]>();
  public readonly _onPresentAssistantMessage = new Emitter<[]>();
  public readonly _onSendMessageError = new Emitter<[ILvErrorOr<void>]>();
  private _networkClient = this._networkClientFactoryService.build({});
  private readonly _conversationIdToContextMap = new Map<string, CodingConversationContext>();
  private _currentConversationId = '';
  private _onConversationContextChange = new Emitter<[IConversationContextState]>();
  private _onConversationMessageChange = new Emitter<[IMessageChangeInfo]>();
  private _onToolProgress = new Emitter<[ToolProgressEvent]>();

  // 工具进度管理器
  private readonly _toolProgressManager = new ToolProgressManager();

  get currentCid() {
    return this._currentConversationId;
  }

  get onConversationContextChange() {
    return this._onConversationContextChange.event;
  }
  get onConversationMessageChange() {
    return this._onConversationMessageChange.event;
  }
  constructor(
    @INetworkClientFactoryService
    private readonly _networkClientFactoryService: INetworkClientFactoryService,
    @IConversationHistoryService
    private readonly _historyService: IConversationHistoryService,
    @IInstantiationService
    private readonly _instantiationService: IInstantiationService,
    @IFileLoggerService
    private readonly _fileLoggerService: IFileLoggerService,
  ) {
    super();
    // debug log
    try {
      this._fileLoggerService.info('CodingChatService constructor');
    } catch (error) {
      console.error('+++Failed to log info:', error);
    }
  }

  override dispose() {
    super.dispose();
    this._toolProgressManager.dispose();
  }

  async getConversationBusiness(cid: string): Promise<string> {
    await this._conversationSwitched();

    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      this._fileLoggerService.error(`[${this._conversationType}] 获取会话业务线失败: ${cid}`);
      return '';
    }

    const business = await conversationContext.getBusiness();
    this._fileLoggerService.info(`[${this._conversationType}] 获取会话业务线成功: ${cid}, ${business}`);
    return business;
  }

  async setConversationBusiness(cid: string, business: string) {
    await this._conversationSwitched();
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      this._fileLoggerService.error(`[${this._conversationType}] 设置会话业务线失败: ${cid}`);
      return;
    }

    return await conversationContext.setBusiness(business);
  }

  async getConversationMode(cid: string): Promise<base.AgentWorkMode> {
    await this._conversationSwitched();

    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      this._fileLoggerService.error(`[${this._conversationType}] 获取会话模式失败: ${cid}`);
      return DEFAULT_WORK_MODE;
    }

    const mode = await conversationContext.getMode();
    this._fileLoggerService.info(`[${this._conversationType}] 获取会话模式成功: ${cid}, ${mode}`);
    return mode;
  }

  async setConversationMode(cid: string, mode: base.AgentWorkMode) {
    await this._conversationSwitched();

    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      this._fileLoggerService.error(`[${this._conversationType}] 设置会话模式失败: ${cid}`);
      return;
    }

    return await conversationContext.setMode(mode);
  }

  getCurrentContextState() {
    return this._makeConversationContextChangeData(this._currentConversationId);
  }

  getMessages(): ClientMessage[] {
    return this.currentConversationContext?.messagesManager.getMessages() || [];
  }

  public get onUpdate() {
    return this._onUpdate.event;
  }

  public get onPresentAssistantMessage() {
    return this._onPresentAssistantMessage.event;
  }

  public get onSendMessageError() {
    return this._onSendMessageError.event;
  }

  public get onToolProgress() {
    return this._onToolProgress.event;
  }

  public get currentConversationContext() {
    return this._conversationIdToContextMap.get(this._currentConversationId);
  }

  // 获取工具进度管理器
  public get toolProgressManager() {
    return this._toolProgressManager;
  }

  resetMessageReceiverAndSocket() {}

  /** 创建会话的通用逻辑 */
  public async createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>> {
    try {
      console.log('code create conversation', options);
      this._fileLoggerService.info(`${this._conversationType} createConversation: ${JSON.stringify(options)}`);
      const { parentConversationId = '', parentMessageVersion = 0 } = options || {};
      const url = 'https://capcut-devops.byted.org/conversation/create';
      const headers = {};
      const data = this._getConversationData({
        parentConversationId,
        parentMessageVersion,
      });
      console.log(`[${this._conversationType}] 创建会话前: ${JSON.stringify(data)}`);
      this._fileLoggerService.info(`${this._conversationType} createConversation data: ${JSON.stringify(data)}`);
      const response = await this._networkClient(url, {
        method: 'POST',
        headers,
        data,
      });
      console.log(`[${this._conversationType}] 创建会话: ${JSON.stringify(response.headers)}`);
      this._fileLoggerService.info(
        `${this._conversationType} createConversation response: ${JSON.stringify(response.headers)}`,
      );
      const cid = response.data.data.id;
      await this._updateConversationRelation({
        cid,
        parentConversationId,
        parentMessageVersion,
      });
      const conversationContext = this._instantiationService.createInstance(
        CodingConversationContext,
        {
          cid,
          parentCid: parentConversationId,
          parentMessageVersion,
        },
        [],
      );
      await conversationContext.bootstrap();
      this._conversationIdToContextMap.set(cid, conversationContext);
      this._addMessageListener(conversationContext);
      console.log(`[${this._conversationType}] 创建会话成功: ${cid}, 类型: ${this._conversationType}`);
      this._fileLoggerService.info(`${this._conversationType} createConversation success: ${cid}`);
      return makeOkWith(cid);
    } catch (error) {
      console.log(`[${this._conversationType}] 创建会话失败: ${error}`);
      this._fileLoggerService.error(`${this._conversationType} createConversation error: ${error}`);
      return makeErrorBy(-1, '创建会话失败', error as Error);
    }
  }

  private async restoreConversation(cid: string) {
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (conversationContext) {
      return makeOkWith(conversationContext);
    }
    const result = await this._restoreConversation(cid);
    if (!result.ok) {
      return result;
    }
    return result;
  }

  public async sendMessage(message: AskMessage) {
    const { cid } = message;
    if (!cid) {
      this._fileLoggerService.error(`${this._conversationType} sendMessage error: cid is empty`);
      return makeErrorBy(-1, '发送消息失败', new Error('发送消息失败'));
    }
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      console.log('发送消息失败, cid', cid);
      this._fileLoggerService.error(`${this._conversationType} sendMessage error: cid ${cid} not found`);
      return makeErrorBy(-1, '发送消息失败', new Error('发送消息失败'));
    }
    conversationContext.sendMessage(message);
  }

  public async switchCurrentConversation(cid?: string) {
    try {
      this.conversationSwitching = true;
      if (cid) {
        // TODO: 这里临时保证实例只被初始化一次，后续需要解决 webview 触发多次的问题
        if (cid === this._currentConversationId) {
          return makeOkWith(cid);
        }
        this._currentConversationId = cid;
        const conversationContext = this._conversationIdToContextMap.get(cid);
        if (conversationContext) {
          const data = await this._makeConversationContextChangeData(this._currentConversationId);
          if (!data) {
            this._fileLoggerService.error(
              `${this._conversationType} switchCurrentConversation error: ${cid} not found`,
            );
            return makeErrorBy(-1, '切换会话失败', new Error('切换会话失败'));
          }
          this._onConversationContextChange.fire(data);
        } else {
          await this.restoreConversation(cid);
          const data = await this._makeConversationContextChangeData(this._currentConversationId);
          if (!data) {
            this._fileLoggerService.error(
              `${this._conversationType} switchCurrentConversation error: ${cid} not found`,
            );
            return makeErrorBy(-1, '切换会话失败', new Error('切换会话失败'));
          }
          this._onConversationContextChange.fire(data);
        }
        return makeOkWith(cid);
      }
      const result = await this.createConversation();
      if (!result.ok) {
        return result;
      }
      this._currentConversationId = result.value;
      const data = await this._makeConversationContextChangeData(this._currentConversationId);
      if (!data) {
        this._fileLoggerService.error(`${this._conversationType} switchCurrentConversation error: ${cid} not found`);
        return makeErrorBy(-1, '切换会话失败', new Error('切换会话失败'));
      }
      this._onConversationContextChange.fire(data);
      return makeOkWith(result.value);
    } finally {
      this.conversationSwitching = false;
    }
  }

  private async _restoreConversation(
    cid: string,
    parentId?: string,
    parentMessageVersion?: Int64,
  ): Promise<ILvErrorOr<IConversationContextState>> {
    this._fileLoggerService.info(`${this._conversationType} restoreConversation start: ${cid}`);
    const [historyRelation, historyMessage] = await Promise.all([
      this._historyService.getConversationRelation(cid),
      this._historyService.getConversationMessages(cid),
    ]);
    if (!historyRelation.ok || !historyMessage.ok) {
      this._fileLoggerService.error(`${this._conversationType} restoreConversation error: ${cid} not found`);
      return makeErrorBy(
        -1,
        '恢复会话失败',
        historyRelation.cause || historyMessage.cause || new Error('恢复会话失败'),
      );
    }
    // 接下来批量拉取子会话内容
    const messages = historyMessage.value;
    const conversationContext = this._instantiationService.createInstance(
      CodingConversationContext,
      {
        cid,
        parentCid: parentId,
        parentMessageVersion,
      },
      messages,
    );
    const { childIds } = historyRelation.value ?? { childIds: [] };
    await Promise.all(
      childIds.map(async (item) => {
        return this._restoreConversation(item.cid, cid, item.parentMessageVersion);
      }),
    );

    await conversationContext.bootstrap();

    this._conversationIdToContextMap.set(cid, conversationContext);
    this._addMessageListener(conversationContext);
    this._fileLoggerService.info(`${this._conversationType} restoreConversation success: ${cid}`);
    return makeOk();
  }

  public addMessageListener(context: CodingConversationContext) {
    this._addMessageListener(context);
  }

  private _addMessageListener(context: CodingConversationContext) {
    this._register(
      context.messagesManager.onAppendMessages((messages) => {
        if (context.id !== this._currentConversationId && context.parentId !== this._currentConversationId) {
          return;
        }
        const { id, parentId, parentMessageVersion } = context;
        this._onConversationMessageChange.fire({
          messages,
          type: 'add',
          cid: id,
          parentCid: parentId,
          parentMessageVersion,
          conversationStatus: context.messageReceiver.status,
        });
        this._onPresentAssistantMessage.fire();
      }),
    );
    this._register(
      context.messagesManager.onUpdateMessage((message) => {
        if (context.id !== this._currentConversationId && context.parentId !== this._currentConversationId) {
          return;
        }
        const { id, parentId, parentMessageVersion } = context;
        this._onConversationMessageChange.fire({
          messages: [message],
          type: 'update',
          cid: id,
          parentCid: parentId,
          parentMessageVersion,
          conversationStatus: context.messageReceiver.status,
        });
        this._onPresentAssistantMessage.fire();
      }),
    );

    // 监听工具进度事件
    this._register(
      context.messagesManager.onToolProgress((progressEvent) => {
        this._onToolProgress.fire(progressEvent);
        // 将工具进度事件转发给工具进度管理器
        this._toolProgressManager.updateProgress(
          progressEvent.toolId,
          progressEvent.toolName,
          progressEvent.progress,
          progressEvent.message,
        );
      }),
    );
  }

  private async _makeConversationContextChangeData(cid: string): Promise<IConversationContextState> {
    const conversationContext = this._conversationIdToContextMap.get(cid);
    if (!conversationContext) {
      return {
        cid,
        messages: [],
        childrenContexts: [],
      } as IConversationContextState;
    }
    const historyRelation = await this._historyService.getConversationRelation(cid);
    if (!historyRelation.ok) {
      return {
        cid,
        messages: conversationContext.messagesManager.getMessages(),
        childrenContexts: [],
      } as IConversationContextState;
    }
    const { childIds } = historyRelation.value ?? { childIds: [] };
    // 这里通过获取父子关系去装数据
    const { messages, parentId, parentMessageVersion } = conversationContext;
    // 获取子会话
    const children: IConversationContextState[] = [];
    if (childIds.length > 0) {
      for (const item of childIds) {
        const { parentMessageVersion: childParentMessageVersion, cid: childCid } = item;
        const result = await this._historyService.getConversationMessages(childCid);
        if (result.ok) {
          children.push({
            cid: childCid,
            messages: result.value,
            childrenContexts: [],
            parentCid: cid,
            parentMessageVersion: childParentMessageVersion,
          });
        }
      }
    }
    return {
      cid,
      parentCid: parentId,
      parentMessageVersion,
      messages,
      childrenContexts: children,
    };
  }

  private _getConversationData(options: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): CreateConversationRequest {
    const { parentConversationId, parentMessageVersion } = options;
    // @ts-ignore
    const data: CreateConversationRequest = {
      type: ConversationType.Coding,
    };
    if (parentConversationId && parentMessageVersion) {
      data.parent_id = parentConversationId;
      data.parent_version = parentMessageVersion;
    }
    return data;
  }

  private async _updateConversationRelation(options: {
    cid: string;
    parentConversationId: string;
    parentMessageVersion: Int64;
  }) {
    const { cid, parentConversationId, parentMessageVersion } = options;
    if (parentConversationId && parentMessageVersion) {
      try {
        await this._historyService.saveConversationRelation(options);
        console.debug(`[${this._conversationType}] 保存会话关系成功: ${parentConversationId}, ${cid}`);
        this._fileLoggerService.info(
          `${this._conversationType} updateConversationRelation success: ${parentConversationId}, ${cid}`,
        );
      } catch (error) {
        console.error(`[${this._conversationType}] 保存会话关系失败:`, error);
        this._fileLoggerService.error(`${this._conversationType} updateConversationRelation error: ${error}`);
      }
    }
  }

  private _conversationSwitched(): Promise<void> {
    if (this.conversationSwitching) {
      return new Promise((resolve) => {
        this.onConversationContextChange(() => {
          resolve();
        });
      });
    }

    return Promise.resolve();
  }
}
