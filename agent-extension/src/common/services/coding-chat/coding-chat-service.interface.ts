import type { base, Int64 } from '@/bam';
import { ModelType } from '@/common/constants/model-types';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import { Event } from '@byted-image/lv-bedrock/event';
import { AskMessage, type IConversationContextState, type IMessageChangeInfo } from '../base-chat/types';
import type { BaseConversationContext } from '../conversation/base-conversation-context/base-conversation-context';
import type { ToolProgressEvent } from '../conversation/base-conversation-context/messages-manager';

export const DefaultModelType = ModelType.Claude4_Sonnet;

export interface ICodingChatService {
  _serviceBrand: undefined;

  currentCid: string;

  onConversationMessageChange: Event<[IMessageChangeInfo]>;
  onPresentAssistantMessage: Event<[]>;
  onSendMessageError: Event<[ILvErrorOr<void>]>;
  onToolProgress: Event<[ToolProgressEvent]>;

  // 发送消息
  sendMessage(message: AskMessage): void;

  getMessages(): ClientMessage[];

  resetMessageReceiverAndSocket(): void;

  switchCurrentConversation(cid?: string): Promise<ILvErrorOr<string>>;

  createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>>;

  getCurrentContextState(): Promise<IConversationContextState>;

  addMessageListener(context: BaseConversationContext): void;

  getConversationBusiness(cid: string): Promise<string>;

  setConversationBusiness(cid: string, business: string): Promise<void>;

  getConversationMode(cid: string): Promise<base.AgentWorkMode>;

  setConversationMode(cid: string, mode: base.AgentWorkMode): Promise<void>;
}

export const ICodingChatService = createDecorator<ICodingChatService>('coding-chat-service');
