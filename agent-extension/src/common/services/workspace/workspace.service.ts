import vscode from 'vscode';
import { Repository } from '@/typings/git';
import { IAccountService } from '../account/account-service.interface';
import { IGitService } from '../git/git-service.interface';
import { IWorkspaceService } from './workspace.interface';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { machineIdSync } from '@/common/utils/machine';
import { Disposable } from '@byted-image/lv-bedrock/dispose';
import { OutputLogger } from '@/utils/output-logger';
import { extractRepoPath } from './util';
import repoConfig from './repo-config/config.json';
import { CodinIgnoreManager, type ICodinIgnoreManager } from './manager/codin-ignore';

export const WORKSPACE_SERVICE = 'workspace-service';

export interface IRepoInfo {
  userId: string;
  branch: string;
  repoName: string;
  path: string;
  pathList: string[];
}

export class WorkspaceService extends Disposable implements IWorkspaceService {
  public readonly _serviceBrand: undefined;
  private _did: string;
  private _logger: OutputLogger;
  private _disposableMap: Map<string, vscode.Disposable> = new Map();
  private _repoInfoMap: Map<string, IRepoInfo> = new Map();
  private _logChannel: vscode.LogOutputChannel;
  private _accountService: IAccountService;
  private _gitService: IGitService;

  public readonly codinIgnoreManager: ICodinIgnoreManager;

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    super();
    this._logChannel = vscode.window.createOutputChannel('Codin: Workspace', {
      log: true,
    });
    this._logger = new OutputLogger(this._logChannel, '', WORKSPACE_SERVICE);
    this._did = machineIdSync();
    this._accountService = this._instantiationService.invokeFunction((accessor) => accessor.get(IAccountService));
    this._gitService = this._instantiationService.invokeFunction((accessor) => accessor.get(IGitService));
    this.initRepository();
    this.codinIgnoreManager = this._instantiationService.createInstance(CodinIgnoreManager);
  }

  private get _uid() {
    return this._accountService.getUserInfo()?.userId ?? 'guest';
  }

  public get repoConfig() {
    return repoConfig;
  }

  public getBusinessRepoInfo(repoName: string): IRepoInfo[] {
    const repoInfo = this.getRepoInfo(repoName);
    if (!repoInfo) {
      return [];
    }
    const config = repoConfig as Record<string, Record<string, string[]>>;
    return config[repoName]
      ? Object.keys(config[repoName]).map((key) => {
          return {
            ...repoInfo,
            pathList: config[repoName][key] ?? ['/'],
          };
        })
      : [repoInfo];
  }

  public getBusinessList(): string[] {
    const businessList: string[] = [];
    Object.keys(repoConfig).forEach((repoName) => {
      const config = repoConfig as Record<string, Record<string, string[]>>;
      businessList.push(...Object.keys(config[repoName]).flat());
    });
    return businessList;
  }

  public getBusinessPathList(repoName: string, business: string): string[] | undefined {
    const config = repoConfig as Record<string, Record<string, string[]>>;
    return config[repoName]?.[business];
  }

  public getPathList(repoName: string, business: string): string[] {
    const config = repoConfig as Record<string, Record<string, string[]>>;
    return config[repoName][business] ?? ['/'];
  }

  async initRepository(): Promise<void> {
    await this._accountService.initialize();
    const gitApi = this._gitService.getGitApi();
    if (!gitApi) {
      return;
    }
    this._logger.log('[_initialize] workspace service created, start initializing...');
    gitApi.onDidOpenRepository(this._initRepo.bind(this));
    gitApi.onDidCloseRepository(this._removeRepo.bind(this));
    gitApi.repositories.forEach(this._initRepo.bind(this));
  }

  private _removeRepo(repo: Repository) {
    const repoName = extractRepoPath(repo.state.remotes?.[0]?.fetchUrl ?? '');
    this._logger.log('[removeRepo] repoName:', repoName);
    if (!repoName) {
      return;
    }
    this._disposableMap.get(repoName)?.dispose();
    this._disposableMap.delete(repoName);
    this._repoInfoMap.delete(repoName);
  }

  private _initRepo(repo: Repository) {
    if (!repo.state.remotes || repo.state.remotes.length === 0) {
      const repoWatcher = repo.state.onDidChange(() => {
        this._initRepo(repo);
        repoWatcher.dispose();
      });
      return;
    }

    const repoName = extractRepoPath(repo.state.remotes[0].fetchUrl ?? '');
    const branch = repo.state.HEAD?.name ?? 'master';

    this._logger.log('[initRepo] repoName:', repoName);
    if (!repoName) {
      return;
    }
    // todo(liboti)
    // 此处要读取业务仓库的codin配置文件，然后从里面获取pathList
    const config = repoConfig as Record<string, Record<string, string[]>>;
    const pathList = config[repoName] ? Object.values(config[repoName]).flat() : ['/'];

    this._repoInfoMap.set(repoName, {
      userId: this._uid,
      branch: repo.state.HEAD?.name ?? 'master',
      repoName,
      pathList: pathList,
      path: repo.rootUri.path,
    });

    this._logger.log(repoName, repo.state.HEAD?.name ?? 'master', repo.rootUri.path);
    const disposable = repo.state.onDidChange(() => {
      const newBranch = repo.state.HEAD?.name ?? 'master';
      if (newBranch === branch) {
        return;
      }
      this._repoInfoMap.get(repoName)!.branch = newBranch;
      disposable.dispose();
    });
    this._disposableMap.set(repoName, disposable);
  }

  getRepoInfo(repoName: string) {
    return this._repoInfoMap.get(repoName);
  }

  getAllRepoInfo() {
    return this._repoInfoMap;
  }

  getDid() {
    return this._did;
  }

  dispose() {
    this._repoInfoMap.clear();
  }
}
