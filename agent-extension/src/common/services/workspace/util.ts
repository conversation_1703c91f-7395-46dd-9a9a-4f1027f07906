export const extractRepoPath = (repoUrl: string) => {
  const trimmed = repoUrl.trim();
  if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
    try {
      const url = new URL(trimmed);
      let pathname = url.pathname;
      if (pathname.startsWith('/')) {
        pathname = pathname.slice(1);
      }
      if (pathname.endsWith('.git')) {
        pathname = pathname.slice(0, -4);
      }
      return pathname;
    } catch {
      // fallback: 不是合法 URL，返回空字符串
      return '';
    }
  }
  // 兼容原有逻辑
  return trimmed.split(':')[1]?.replace(/\.git$/, '') || '';
};
