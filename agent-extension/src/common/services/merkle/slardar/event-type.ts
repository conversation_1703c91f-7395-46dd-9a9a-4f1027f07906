export const SyncEventName = 'merkle_sync';

export interface SyncEventParams extends Record<string, string | number> {
  status: 'success' | 'fail' | 'skip';
  reason: string;
  chunkCount: number;
  diffCount: number;
  chunkSize: number;
  relationSize: number;
  treeSize: number;
  rootHash: string;
  diffLogId: string;
  uploadLogId: string;
}

export const BuildEventName = 'merkle_build';

export interface BuildEventParams extends Record<string, string | number> {
  status: 'success' | 'fail' | 'skip';
  reason: string;
  rootHash: string;
}

export const UpdateEventName = 'merkle_update';

export interface UpdateEventParams extends Record<string, string | number> {
  status: 'success' | 'fail' | 'skip';
  reason: string;
  diffCount: number;
  rootHash: string;
}
