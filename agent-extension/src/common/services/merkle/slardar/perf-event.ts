import type { OutputLogger } from '@/utils/output-logger';
import { BaseEvent } from './base-event';

interface StagePoint {
  name: string;
  start: number;
  end: number;
}

export class PerfEvent<EventParams extends Record<string, string | number>> extends BaseEvent<EventParams> {
  private _logger: OutputLogger;
  private _stagePoints: Map<string, StagePoint> = new Map();

  constructor(logger: OutputLogger, eventName: string, eventParams: Partial<EventParams> = {}) {
    super(eventName, eventParams);
    this._logger = logger.derive(`[event#${eventName}]`);
    this.stageStart('total');
    this._logger.log('start');
  }

  stageStart(stageName: string, eventParams: Partial<EventParams> | null = null) {
    this.update(eventParams);
    this._stagePoints.set(stageName, {
      name: stageName,
      start: Math.floor(performance.now()),
      end: 0,
    });
    if (stageName !== 'total') {
      this._logger.log(`${stageName} start`, eventParams ?? '');
    }
  }

  stageEnd(stageName: string, eventParams: Partial<EventParams> | null = null) {
    this.update(eventParams);
    const perfStagePoint = this._stagePoints.get(stageName);
    if (!perfStagePoint) {
      this._logger.error(`stage ${stageName} not start`);
      return;
    }
    perfStagePoint.end = Math.floor(performance.now());
    if (stageName !== 'total') {
      this._logger.log(`${stageName} end, duration=${perfStagePoint.end - perfStagePoint.start}ms`, eventParams ?? '');
    }
  }

  emit(eventParams: Partial<EventParams>) {
    const perfParams: Record<string, number> = {};
    this.stageEnd('total');

    for (const [stageName, stagePoint] of this._stagePoints) {
      if (stagePoint.end === 0 || stagePoint.start === 0) {
        this._logger.error(`stage ${stageName} not fullfilled`);
        continue;
      }

      perfParams[`__${stageName}_duration`] = stagePoint.end - stagePoint.start;
      perfParams[`__${stageName}_start`] = stagePoint.start;
      perfParams[`__${stageName}_end`] = stagePoint.end;
    }

    const params = {
      ...eventParams,
      ...perfParams,
    };

    super.emit(params);

    if (eventParams.status === 'fail') {
      this._logger.error('emit', {
        ...this._eventParams,
        ...params,
      });
    } else {
      this._logger.log('emit', {
        ...this._eventParams,
        ...params,
      });
    }
  }
}
