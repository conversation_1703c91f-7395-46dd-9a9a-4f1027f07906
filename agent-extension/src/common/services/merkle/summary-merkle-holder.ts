import { sleep } from '@byted-image/lv-bedrock/async';
import { BuildStatus, type BaseRequest } from './api/base';
import { makeSummaryGetUpdateFilesRequest } from './api/summary-get-update-files';
import { makeSummaryBuildQueryRequest } from './api/summary-build-query';
import { makeSummaryUpdateRequest, type GroupedRelatedFileInfo } from './api/summary-update';
import { compress } from './compress/compress';
import { SyncEventName, type SyncEventParams } from './slardar/event-type';
import { PerfEvent } from './slardar/perf-event';
import type { DiffType } from './type';
import { MerkleHolder, type MerkleHolderOptions } from './merkle-holder';
import fs from 'node:fs/promises';
import path from 'node:path';
// import { cwd } from '@/common/env/system';
import { buildGroupedRelatedFileInfo, type IFileContentMap } from './chunk/get-content-map';
import { getCreate64, hashFileByBufferSync, isIgnoredByMap, updateMerkleTree } from '@byted-image/merkle';
import { MerkleTosConfig, SummaryTosConfig } from './tos/config';
import { getTosClient, type ITosClient } from './tos/tos';
import { INetworkClientFactoryService } from '../network-client-factory/network-client-factory-service.interface';
// import { writeFileSync } from 'node:fs';

/**
 * MerkleHolder 类负责为每个 Git 仓库管理和维护 Merkle 树。
 * 它监听仓库的文件变化和分支切换事件，根据变化更新 Merkle 树，并与服务端同步数据。
 * 由于一个 workspace 可能包括多个 repo，所以每个 repo 都有一个 MerkleHolder
 */
export class SummaryMerkleHolder extends MerkleHolder {
  label = 'summary';

  private _summaryTosClient: ITosClient;

  private _merkleTosClient: ITosClient;
  private _networkClient = this._networkClientFactoryService.build({});

  /**
   * 构造函数，初始化 MerkleHolder 实例。
   * @param options - 初始化所需的配置选项，包括用户 ID、设备 ID、Git 仓库实例和日志记录器。
   */
  constructor(
    options: MerkleHolderOptions,
    @INetworkClientFactoryService
    private readonly _networkClientFactoryService: INetworkClientFactoryService,
  ) {
    super(options, 30 * 60 * 1000);
    this._summaryTosClient = getTosClient(SummaryTosConfig);
    this._merkleTosClient = getTosClient(MerkleTosConfig);
  }

  /**
   * 将 Merkle 树和差异文件块同步到服务端。
   * 内部也会更新 tree
   */
  protected async _doSync(): Promise<boolean> {
    if (!this._tree) {
      this._logger.error('[_doSync] tree is not initialized');
      return false;
    }
    const syncEvent = new PerfEvent<SyncEventParams>(this._logger, SyncEventName);

    const baseRequest: BaseRequest = {
      uid: this._uid,
      did: this._did,
      branch: this.branch,
      repo_name: this.dbName,
      repo_path: this.root,
    };

    this._logger.log(`sync hash=${this._tree?.hash} baseRequest=${JSON.stringify(baseRequest)}`);

    // 1. diff (基于前端上传的树，远端树应该产生的差异)
    syncEvent.stageStart('diff');
    const serializedTree = await this.serializeTree();
    const compressedTree = await compress(serializedTree);
    const treeHash = this._tree.hash;
    syncEvent.stageStart('pre_diff');
    const preDiffStatus = await this._merkleTosClient.upload(treeHash, Buffer.from(compressedTree));
    syncEvent.stageEnd('pre_diff');
    if (!preDiffStatus) {
      syncEvent.emit({
        status: 'fail',
        reason: 'pre diff upload fail',
      });
      return false;
    }

    this._logger.log('[_DoSync] ---------start req: get update files----------');
    // writeFileSync(path.join(this.root, 'tree1.json'), serializedTree);
    const diffResp = await makeSummaryGetUpdateFilesRequest(this._networkClient, {
      ...baseRequest,
      merkle_tree_key: treeHash,
    });
    const diffResult = diffResp.data;
    this._logger.log('[_DoSync] ---------end req: get update files----------');
    this._logger.log('[_DoSync] ------------------summay diff len: ', diffResult.diffs.length);
    this._logger.log(
      '[_DoSync] ------------------summay module len: ',
      diffResult.grouped_related_path_info.module_groups.length,
    );
    this._logger.log(
      '[_DoSync] ------------------summay leaf len: ',
      diffResult.grouped_related_path_info.leaf_groups.length,
    );
    syncEvent.stageEnd('diff', {
      diffLogId: diffResp.log_id,
    });
    let newRootHash = this._tree!.hash;
    let compressedChunk: ArrayBuffer | null = null;
    let compressedTree2: ArrayBuffer = compressedTree;
    // 如果删除的文件就返回空内容
    const isNoFileChange =
      diffResult.grouped_related_path_info.module_groups.length === 0 &&
      diffResult.grouped_related_path_info.leaf_groups.length === 0;
    // 没有diff的话，说明远端和本地一致，或者远端还没有构建索引
    // 如果没有变化，并且有root_merkle_id，说明远端已经构建了索引，直接返回
    // 存在一种情况是用户从master创建新的分支，此时diff为0，且没有rootMerkleId
    // 这个时候需要提前触发用户db索引的copyFrom，方便后续用户可能直接进行语义搜索，搜到的是新的userKnowledgeId
    if (isNoFileChange) {
      this._logger.log('[_DoSync] ---------no file change, query summary status----------');
      const queryResp = await makeSummaryBuildQueryRequest(this._networkClient, { ...baseRequest });
      this._logger.log('[_DoSync] ---------end req: query summary status----------');
      this._logger.log('[_DoSync] ---------query summary status resp: ', queryResp);
      if (queryResp.data.root_merkle_id !== '') {
        syncEvent.emit({
          status: 'skip',
          reason: 'no diff',
        });
        return true;
      }
    }

    // 收集需要上行的文件内容
    if (!isNoFileChange) {
      const create64 = await getCreate64();
      const dirPaths: string[] = [];
      const leafDirPaths: string[] = [];
      // 这里要收集目录下一层的所有文件信息
      diffResult.grouped_related_path_info.module_groups.forEach((moduleGroup) => {
        dirPaths.push(...moduleGroup.sub_dir_paths);
      });
      // 这个要递归的收集目录下的所有信息
      diffResult.grouped_related_path_info.leaf_groups.forEach((leafGroup) => {
        leafDirPaths.push(leafGroup.group_path);
      });
      this._logger.log('[_DoSync] ------dirPaths length: ', dirPaths.length);
      this._logger.log('[_DoSync] ------leafDirPaths length: ', JSON.stringify(diffResult.grouped_related_path_info));
      // 2. get files' content and save to memory
      // const contentMap = await getFullFileContentMap(this.root, dirPaths, leafDirPaths);
      const groupedRelatedFileInfo: GroupedRelatedFileInfo = await buildGroupedRelatedFileInfo(
        this.root,
        diffResult.grouped_related_path_info,
      );
      const contentMap: IFileContentMap = {};
      groupedRelatedFileInfo.module_groups.forEach((moduleGroup) => {
        moduleGroup.sub_file_infos.forEach((subFileInfo) => {
          const buffer = Buffer.from(subFileInfo.file_content, 'utf-8');
          const hash = hashFileByBufferSync(create64, buffer);
          contentMap[subFileInfo.file_path] = {
            hash: hash,
            content: subFileInfo.file_content,
          };
        });
      });
      groupedRelatedFileInfo.leaf_groups.forEach((leafGroup) => {
        leafGroup.sub_file_infos.forEach((subFileInfo) => {
          const buffer = Buffer.from(subFileInfo.file_content, 'utf-8');
          const hash = hashFileByBufferSync(create64, buffer);
          contentMap[subFileInfo.file_path] = {
            hash: hash,
            content: subFileInfo.file_content,
          };
        });
      });

      syncEvent.update({
        diffCount: diffResult.diffs.length,
        chunkCount: dirPaths.length,
        moduleCount: diffResult.grouped_related_path_info.module_groups.length,
        leafCount: diffResult.grouped_related_path_info.leaf_groups.length,
      });
      const diffs = new Map<string, DiffType>();
      Object.keys(contentMap).forEach((key) => {
        if (!isIgnoredByMap(key, this._ignoreMap, this._regexMap)) {
          diffs.set(key, 'modify');
        }
      });

      // 3. update merkle tree & chunks at same time
      const promises: Promise<void>[] = [];

      // merkle tree应该以前端为准，前端不需要更新，前端覆盖后端
      // 3.a update merkle tree
      promises.push(
        (async () => {
          syncEvent.stageStart('tree_update');
          await updateMerkleTree(baseRequest.repo_path, this._tree!, diffs, contentMap);
          newRootHash = this._tree!.hash;
          const serializedTree2 = await this.serializeTree();
          compressedTree2 = await compress(serializedTree2);
          syncEvent.stageEnd('tree_update');
          // writeFileSync(path.join(this.root, 'tree2.json'), serializedTree);
        })(),
      );

      // 3.b chunk files
      promises.push(
        (async () => {
          syncEvent.stageStart('chunk');

          const fileSize =
            groupedRelatedFileInfo.module_groups.reduce((acc, group) => acc + group.sub_file_infos.length, 0) +
            groupedRelatedFileInfo.leaf_groups.reduce((acc, group) => acc + group.sub_file_infos.length, 0);

          this._logger.log('[_DoSync] ------fileSize: ', fileSize);
          // const chunkFilePath = path.join(cwd, `groupedRelatedFileInfo.json`);
          // try {
          //   await fs.writeFile(chunkFilePath, JSON.stringify(groupedRelatedFileInfo, null, 2));
          //   this._logger.log(`成功写入文件 ${chunkFilePath}`);
          // } catch (error) {
          //   this._logger.log(`写入文件 ${chunkFilePath} 失败: ${error}`);
          // }
          this._logger.log('[_DoSync] ------module_groups: ', groupedRelatedFileInfo.module_groups.length);
          this._logger.log('[_DoSync] ------leaf_groups: ', groupedRelatedFileInfo.leaf_groups.length);
          compressedChunk = await compress(JSON.stringify(groupedRelatedFileInfo));
          // compressedChunk = await getChunkFile(baseRequest.repoName, baseRequest.path, contentMap);
          syncEvent.stageEnd('chunk');
        })(),
      );

      await Promise.all(promises);

      if (!compressedChunk) {
        this._logger.log('[_DoSync] no chunk file, skip upload');
        syncEvent.emit({
          status: 'skip',
          reason: 'no chunk file',
        });
        return true;
      }
    }

    // 4. call api to upload chunks and merkle tree
    this._logger.log('[_DoSync] ---------start req: query summary status----------');
    const queryResp = await makeSummaryBuildQueryRequest(this._networkClient, { ...baseRequest });
    this._logger.log('[_DoSync] ---------end req: query summary status----------');
    if (queryResp.data.root_merkle_id && queryResp.data.build_status === BuildStatus.Building) {
      syncEvent.emit({
        status: 'skip',
        reason: 'remote db is updating',
      });
      return true;
    }

    syncEvent.stageStart('pre_upload');
    const promises: Promise<boolean>[] = [];
    const groupedRelatedFileInfoKey = `file/${treeHash}`;
    if (compressedChunk) {
      promises.push(this._summaryTosClient.upload(groupedRelatedFileInfoKey, Buffer.from(compressedChunk)));
    }
    promises.push(this._merkleTosClient.upload(treeHash, Buffer.from(compressedTree2)));
    const preUploadStatus = await Promise.all(promises);

    syncEvent.stageEnd('pre_upload');
    if (preUploadStatus.some((status) => status === false)) {
      syncEvent.emit({
        status: 'fail',
        reason: 'pre_upload failed',
      });
      return false;
    }

    syncEvent.stageStart('upload');
    syncEvent.update({
      treeSize: compressedTree2.byteLength,
      chunkSize: (compressedChunk as unknown as ArrayBuffer)?.byteLength ?? 0,
    });
    this._logger.log(
      '------upload req: ',
      (compressedChunk as unknown as ArrayBuffer)?.byteLength,
      compressedTree2.byteLength,
    );
    this._logger.log('[_DoSync] ---------start req: upload summary----------');
    const uploadResp = await makeSummaryUpdateRequest(this._networkClient, {
      ...baseRequest,
      root_merkle_id: newRootHash,
      grouped_related_file_info_key: groupedRelatedFileInfoKey,
      merkle_tree_key: treeHash,
      origin_user_knowledge_id: diffResult.origin_user_knowledge_id,
    });
    this._logger.log('[_DoSync] ---------end req: upload summary----------');
    syncEvent.stageEnd('upload', {
      uploadLogId: uploadResp.log_id,
    });
    if (!uploadResp.data.user_knowledge_id) {
      syncEvent.emit({
        status: 'fail',
        reason: 'upload failed',
      });
      return false;
    }

    // 5. loop query until it's built
    syncEvent.stageStart('query_upload');
    let done = false;
    let success = false;
    while (!done) {
      await sleep(10_000);
      this._logger.log('[_DoSync] ---------start req: query summary status----------');
      const queryResp = await makeSummaryBuildQueryRequest(this._networkClient, { ...baseRequest });
      this._logger.log('[_DoSync] ---------end req: query summary status----------');
      this._logger.log('[_DoSync] query update resp', queryResp.data, queryResp.log_id);
      if (queryResp.data.root_merkle_id !== newRootHash) {
        this._logger.log('[_DoSync] local merkle id: ', newRootHash);
        this._logger.log('[_DoSync] remote merkle id: ', queryResp.data.root_merkle_id);
        syncEvent.emit({
          status: 'fail',
          reason: 'root hash mismatch', // 也可能在 10s 内同步完成了并且外部触发了一次新的更新
        });
        return false;
      }
      if (queryResp.data.build_status !== BuildStatus.Building) {
        done = true;
        if (queryResp.data.build_status === BuildStatus.Success) {
          success = true;
        }
      }
    }
    syncEvent.stageEnd('query_upload');
    let buildStatus = false;
    if (success) {
      syncEvent.emit({
        status: 'success',
        rootHash: newRootHash,
      });
      this._lastSyncHash = newRootHash;
      buildStatus = true;
    } else {
      syncEvent.emit({
        status: 'fail',
        reason: 'query upload failed',
      });
      buildStatus = false;
    }
    return buildStatus;
  }
}

/**
 * 读取分片的 contentMap 文件
 * @param basePath 基础路径
 * @returns 合并后的 contentMap
 */
export async function readChunkedContentMap(basePath: string): Promise<Map<string, string>> {
  const contentMap = new Map<string, string>();

  try {
    // 首先读取索引文件
    const indexFilePath = path.join(basePath, 'contentMap_index.json');
    const indexContent = await fs.readFile(indexFilePath, 'utf-8');
    const index = JSON.parse(indexContent);

    console.log(`读取索引文件，总共 ${index.totalChunks} 个分片文件`);

    // 逐个读取分片文件
    for (const chunkInfo of index.chunks) {
      const chunkFilePath = path.join(basePath, chunkInfo.fileName);
      console.log(`读取分片文件: ${chunkFilePath}`);

      try {
        const chunkContent = await fs.readFile(chunkFilePath, 'utf-8');
        const chunkData = JSON.parse(chunkContent);

        // 将分片数据合并到 contentMap 中
        Object.entries(chunkData).forEach(([key, value]) => {
          contentMap.set(key, value as string);
        });

        console.log(`成功读取分片文件 ${chunkInfo.fileName}，包含 ${Object.keys(chunkData).length} 个文件`);
      } catch (error) {
        console.error(`读取分片文件 ${chunkInfo.fileName} 失败: ${error}`);
      }
    }

    console.log(`总共读取了 ${contentMap.size} 个文件的内容`);
    return contentMap;
  } catch (error) {
    console.error(`读取分片 contentMap 失败: ${error}`);
    return contentMap;
  }
}
