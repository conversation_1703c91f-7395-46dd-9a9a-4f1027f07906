import { slardarLogger } from '../slardar/slardar';

export const apiDomain = 'https://capcut-devops.byted.org';

export interface BaseRequest {
  // 仓库名称
  repo_name: string;
  // 分支
  branch: string;
  // 用户id
  uid: string;
  // 仓库路径
  repo_path: string;
  // 设备id
  did: string;
}

export enum BuildStatus {
  Building = '0',
  Success = '1',
  Failed = '2',
  NoBuild = '-1',
}

export interface BaseResponseWithData<T> {
  data: T;
  log_id: string;
  message: string;
  code: number;
}

export function pickReportCtx(req: Record<string, any>) {
  const ctx: Record<string, string> = {};
  for (const key in req) {
    const val = req[key];
    if (typeof val === 'string') {
      ctx[key] = val;
    }
    if (typeof val === 'number') {
      ctx[key] = val.toString();
    }
  }
  return ctx;
}

export async function handleResponse<T>(
  api: string,
  resp: BaseResponseWithData<T>,
  reportCtx: Record<string, any> = {},
): Promise<BaseResponseWithData<T>> {
  let apiPath = api;
  try {
    const urlObj = new URL(api);
    apiPath = urlObj.pathname;
  } catch (ex) {
    // ignore
  }

  const logid = resp.log_id;
  if (resp.code !== 0) {
    let errmsg = resp.message;
    slardarLogger.error(`${api} response not ok`, {
      api,
      logid,
      status: resp.code.toString(),
      errmsg,
      ...pickReportCtx(reportCtx),
    });
    throw new Error(`[handleResponse#${apiPath}] ${resp.code} (${resp.message}) msg=${errmsg} logid=${logid}`);
  }
  return resp;
}
