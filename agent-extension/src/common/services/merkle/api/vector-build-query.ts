import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import { apiDomain, handleResponse, type BaseRequest, type BaseResponseWithData, type BuildStatus } from './base';

export interface QueryRequest extends BaseRequest {
  uid: string;
  repo_name: string;
  branch: string;
  repo_path: string;
  did: string;
}

export interface QueryResponse
  extends BaseResponseWithData<{
    root_merkle_id: string; // 为空表示云端无构建
    build_status: BuildStatus; // 0: 构建中 1: 已构建 2: 构建失败, -1: 索引不存在
  }> {}

export async function makeQueryRequest(
  networkClient: NetworkClientInstance,
  queryRequest: QueryRequest,
): Promise<QueryResponse> {
  const api = `${apiDomain}/merklet/query-build`;

  const resp = await networkClient<QueryRequest, QueryResponse>(api, {
    method: 'POST',
    data: queryRequest,
  });
  // 这里会多一层data，因为axios包了一层data，但是chat那边都是.data.data，所以不能加插件处理，等后续一波收了
  return handleResponse(api, resp.data as unknown as QueryResponse);
}
