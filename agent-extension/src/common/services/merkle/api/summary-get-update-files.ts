import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import type { DiffType } from '../type';
import { apiDomain, handleResponse, type BaseRequest, type BaseResponseWithData } from './base';

interface SummaryGetUpdateFilesRequest extends BaseRequest {
  // 客户端上行到tos里面的merkle tree key
  merkle_tree_key: string;
}

export interface GroupedRelatedPathInfo {
  module_groups: DirPathGroup[];
  leaf_groups: DirPathGroup[];
}

interface DirPathGroup {
  group_path: string;
  // todo(liboti) 确认一下这里有没有作用
  sub_dir_paths: string[];
}

interface SummaryGetUpdateFilesResponse
  extends BaseResponseWithData<{
    grouped_related_path_info: GroupedRelatedPathInfo;
    diffs: {
      path: string;
      type: DiffType;
      hash: string;
    }[];
    result: string;
    code: string;
    origin_user_knowledge_id: string;
  }> {}

// 上行merkle tree，后台根据merkle tree对比差异，返回需要重新上行的模块信息给后端
// 上行的模块信息是变更文件内容查找上下游module，然后返回的一个最小的完整集合，方便后端重新做module的summary
export async function makeSummaryGetUpdateFilesRequest(
  networkClient: NetworkClientInstance,
  summaryGetUpdateFilesRequest: SummaryGetUpdateFilesRequest,
) {
  const api = `${apiDomain}/summary/get-update-files`;
  const resp = await networkClient<SummaryGetUpdateFilesRequest, SummaryGetUpdateFilesResponse>(api, {
    method: 'POST',
    data: summaryGetUpdateFilesRequest,
  });

  // 这里会多一层data，因为axios包了一层data，但是chat那边都是.data.data，所以不能加插件处理，等后续一波收了
  return handleResponse(api, resp.data as unknown as SummaryGetUpdateFilesResponse);
}
