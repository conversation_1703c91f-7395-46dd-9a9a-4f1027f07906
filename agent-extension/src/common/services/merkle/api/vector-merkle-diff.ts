import type { DiffType } from '../type';
import { apiDomain, handleResponse, type BaseRequest, type BaseResponseWithData } from './base';
import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';

interface DiffRequest extends BaseRequest {
  merkle_tree_key: string;
}

interface DiffResponse
  extends BaseResponseWithData<{
    diffs: {
      path: string;
      type: DiffType;
      hash: string;
    }[];
    origin_user_knowledge_id: string;
  }> {}

export async function makeDiffRequest(
  networkClient: NetworkClientInstance,
  diffRequest: DiffRequest,
): Promise<DiffResponse> {
  const api = `${apiDomain}/merklet/diff`;

  const resp = await networkClient<DiffRequest, DiffResponse>(api, {
    method: 'POST',
    data: diffRequest,
  });
  // 这里会多一层data，因为axios包了一层data，但是chat那边都是.data.data，所以不能加插件处理，等后续一波收了
  return handleResponse(api, resp.data as unknown as DiffResponse);
}
