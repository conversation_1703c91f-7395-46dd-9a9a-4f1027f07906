import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import { apiDomain, handleResponse, type BaseRequest, type BaseResponseWithData } from './base';

interface SummaryUpdateRequest extends BaseRequest {
  // 客户端上行到tos里面的merkle tree key
  merkle_tree_key: string;
  origin_user_knowledge_id: string;
  root_merkle_id: string;
  // 客户端上行到tos里面的代码文件的key
  grouped_related_file_info_key?: string;
}

interface SummaryUpdateResponse
  extends BaseResponseWithData<{
    user_knowledge_id: string;
  }> {}

interface FileInfo {
  file_path: string;
  file_content: string;
}

export interface FileContentGroup {
  group_path: string;
  sub_file_infos: FileInfo[];
}

export interface GroupedRelatedFileInfo {
  module_groups: FileContentGroup[];
  leaf_groups: FileContentGroup[];
}

// 上行最终版的变更文件给后端，后端根据数据进行summary的更新
export async function makeSummaryUpdateRequest(
  networkClient: NetworkClientInstance,
  summaryUpdateRequest: SummaryUpdateRequest,
): Promise<SummaryUpdateResponse> {
  const api = `${apiDomain}/summary/update`;

  const resp = await networkClient<SummaryUpdateRequest, SummaryUpdateResponse>(api, {
    method: 'POST',
    data: summaryUpdateRequest,
  });

  // 这里会多一层data，因为axios包了一层data，但是chat那边都是.data.data，所以不能加插件处理，等后续一波收了
  return handleResponse(api, resp.data as unknown as SummaryUpdateResponse);
}
