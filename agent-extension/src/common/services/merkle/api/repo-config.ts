export interface RepoConfigItem {
  alias: string[];
  branch: string;
  db_name: string;
  git_repo_name: string;
  path_list: string[];
  platform: string;
  project_id: string;
  repo_name: string;
  repo_url: string;
}

export async function getRepoConfig(): Promise<RepoConfigItem[]> {
  const api = 'https://lf3-config.bytetcc.com/obj/tcc-config-web/tcc-v2-data-capcut.devops.tcc-default';
  try {
    const resp = await fetch(api);
    const respJson = await resp.json();
    return JSON.parse(respJson.data.CODESEARCH_REPO_CONFIG);
  } catch (ex) {
    console.error('[getRepoConfig] error', ex);
    return [];
  }
}
