import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';
import { apiDomain, handleResponse, type BaseRequest, type BaseResponseWithData, type BuildStatus } from './base';

interface SummaryBuildQueryRequest extends BaseRequest {
  //
}

export interface SummaryBuildQueryResponse
  extends BaseResponseWithData<{
    root_merkle_id: string; // 为空表示云端无构建
    build_status: BuildStatus; // 0: 构建中 1: 已构建 2: 构建失败 -1: 无构建
    result: string; // 结果
    code: string; // 接口回包
  }> {}

// 查询构建状态
export async function makeSummaryBuildQueryRequest(
  networkClient: NetworkClientInstance,
  queryRequest: SummaryBuildQueryRequest,
): Promise<SummaryBuildQueryResponse> {
  const api = `${apiDomain}/summary/query-build`;

  const resp = await networkClient<SummaryBuildQueryRequest, SummaryBuildQueryResponse>(api, {
    method: 'POST',
    data: queryRequest,
  });

  // 这里会多一层data，因为axios包了一层data，但是chat那边都是.data.data，所以不能加插件处理，等后续一波收了
  return handleResponse(api, resp.data as unknown as SummaryBuildQueryResponse);
}
