import { apiDomain, handleResponse, type BaseRequest, type BaseResponseWithData } from './base';
import type { NetworkClientInstance } from '@byted-image/lv-bedrock/network';

export interface UploadRequest extends BaseRequest {
  chunk_file_key?: string;
  merkle_tree_key: string;
  relations_file_key?: string;
  delete_file_ids: Array<string>;
  origin_user_knowledge_id: string;
  root_merkle_id: string;
}

interface UploadResponse
  extends BaseResponseWithData<{
    id: string;
  }> {}

export async function makeUploadRequest(
  networkClient: NetworkClientInstance,
  uploadRequest: UploadRequest,
): Promise<UploadResponse> {
  const api = `${apiDomain}/merklet/upload`;

  const resp = await networkClient<UploadRequest, UploadResponse>(api, {
    method: 'POST',
    data: uploadRequest,
  });
  // 这里会多一层data，因为axios包了一层data，但是chat那边都是.data.data，所以不能加插件处理，等后续一波收了
  return handleResponse(api, resp.data as unknown as UploadResponse);
}
