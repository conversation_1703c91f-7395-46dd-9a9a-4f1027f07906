import path from 'node:path';
import type { Repository } from '@/typings/git';
import { getErrorInfo } from '@/utils/error';
import type { OutputLogger } from '@/utils/output-logger';
import { Disposable } from '@byted-image/lv-bedrock/dispose';
import vscode from 'vscode';
import {
  buildMerkleTree,
  getIgnorePatternsRecursively,
  isIgnoredByMap,
  updateMerkleTree,
  updateMerkleTreeByNode,
} from '@byted-image/merkle';
import {
  BuildEventName,
  type BuildEventParams,
  SyncEventName,
  type SyncEventParams,
  UpdateEventName,
  type UpdateEventParams,
} from './slardar/event-type';
import { PerfEvent } from './slardar/perf-event';
import { MerkleNode } from './type'; // Renamed to avoid conflict
import type { DiffType } from './type';
import { type IRepoInfo } from '../workspace/workspace.service';
import { Emitter, Event } from '@byted-image/lv-bedrock/event';
import { pBatch } from '@/utils/p-batch';
import { sleep } from '@byted-image/lv-bedrock/async';
import { v4 as uuidv4 } from 'uuid';

export interface MerkleHolderOptions {
  uid: string;
  did: string;
  repo: Repository;
  logger: OutputLogger;
  repoInfo: IRepoInfo;
  hashConcurrency?: number;
  hashInterval?: number;
}

export interface MerkleHolderSyncBuildIndexEvent {
  status: 'success' | 'failed' | 'loading';
  rootHash: string;
  uid: string;
  did: string;
  label: string; // vector/summary
  repoInfo: IRepoInfo;
  lastUpdated: string;
  cost: number;
  buildId: string;
}

/**
 * MerkleHolder 类负责为每个 Git 仓库管理和维护 Merkle 树。
 * 它监听仓库的文件变化和分支切换事件，根据变化更新 Merkle 树，并与服务端同步数据。
 * 由于一个 workspace 可能包括多个 repo，所以每个 repo 都有一个 MerkleHolder
 */
export abstract class MerkleHolder extends Disposable {
  // options
  protected readonly _uid: string;
  protected readonly _did: string;
  protected _logger: OutputLogger;
  protected readonly _repoInfo: IRepoInfo;
  protected readonly _repo: Repository;
  abstract label: string;
  protected readonly _onBuildIndex = this._register(new Emitter<[MerkleHolderSyncBuildIndexEvent]>());
  public readonly onBuildIndex: Event<[MerkleHolderSyncBuildIndexEvent]> = this._onBuildIndex.event;
  protected _tree: MerkleNode | null = null;
  protected _ignoreMap: Map<string, string[]> = new Map();
  protected _regexMap: Map<string, RegExp> = new Map();
  protected _localDiffs: Map<string, DiffType> = new Map(); // 监听距离上一次本地更新树后本地的文件改动
  protected _lastSyncHash: string | null = null; // 记录上一次和服务端同步的 hash

  protected _processing = false;
  protected _repoWatchers: vscode.FileSystemWatcher[] = [];
  protected _updateTimer: NodeJS.Timeout | null = null;
  protected _interval: number = 5 * 60 * 1000;

  protected _hashConcurrency: number;
  protected _hashInterval: number;

  get branch() {
    return this._repoInfo.branch;
  }

  get root() {
    return this._repoInfo.path;
  }

  get dbName() {
    return this._repoInfo.repoName;
  }

  get pathList() {
    return this._repoInfo.pathList;
  }

  /**
   * 构造函数，初始化 MerkleHolder 实例。
   * @param options - 初始化所需的配置选项，包括用户 ID、设备 ID、Git 仓库实例和日志记录器。
   */
  constructor(options: MerkleHolderOptions, interval: number) {
    super();

    this._uid = options.uid;
    this._did = options.did;
    this._logger = options.logger;
    this._repoInfo = options.repoInfo;
    this._repo = options.repo;
    this._interval = interval;
    this._hashConcurrency = options.hashConcurrency ?? -1;
    this._hashInterval = options.hashInterval ?? 10;

    this._logger.log(`[_constructor] branch=${this.branch} pathList=${this.pathList}`);

    this._initialize();
  }

  /**
   * 初始化 MerkleHolder 实例，启动文件变化和分支切换的监听，并更新和同步 Merkle 树。
   */
  protected async _initialize(): Promise<void> {
    await sleep(10_000); // 错峰
    this._initWatchers();
    await this._updateAndSync();
    this._logger.log(`[_initialize] initialized with branch=${this.branch} pathList=${JSON.stringify(this.pathList)}`);
  }

  /**
   * 初始化文件变化和分支切换的监听器。
   */
  protected _initWatchers() {
    this._watchFileChange();
  }

  /**
   * 监听 Git 仓库内的文件变化事件，将变化记录到本地差异映射中，并定期更新和同步 Merkle 树。
   */
  protected _watchFileChange() {
    this._logger.log(`[_watchFileChange] start watch repo ${this.root} pathList=${this.pathList}`);

    for (const watchPath of this.pathList) {
      const watcher = vscode.workspace.createFileSystemWatcher(
        new vscode.RelativePattern(path.join(this.root, watchPath), '**/*'),
      );
      watcher.onDidChange((uri) => {
        this._handleFileChange(uri, 'modify');
      });
      watcher.onDidCreate((uri) => {
        this._handleFileChange(uri, 'add');
      });
      watcher.onDidDelete((uri) => {
        this._handleFileChange(uri, 'delete');
      });
      this._repoWatchers.push(watcher);
    }

    // every 5 mins, update tree
    this._updateTimer = setInterval(() => {
      this._updateAndSync();
    }, this._interval);

    // todo(fangzheng/liboti)
    // 如果是切换分支，那么间隔短一点就执行一次触发
    // 这里后续应该可以只是先触发copyFrom
    let timer: NodeJS.Timeout | null = null;
    this._repo.onDidCheckout(() => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        this._updateAndSync();
      }, 10_000);
    });
  }

  /**
   * 处理文件变化事件。
   * @param uri - 文件的 URI。
   * @param type - 文件变化的类型。
   */
  protected _handleFileChange(uri: vscode.Uri, type: DiffType) {
    const relativePath = path.relative(this.root, uri.fsPath);
    this._addLocalDiff(relativePath, type);
  }

  /**
   * 将文件的本地变化添加到本地差异映射中，忽略 .git 目录和被忽略的文件。
   * @param path - 文件的相对路径。
   * @param type - 文件变化的类型。
   */
  protected _addLocalDiff(path: string, type: DiffType) {
    if (isIgnoredByMap(path, this._ignoreMap, this._regexMap)) {
      return;
    }
    this._logger.log(`[_addLocalDiff] ${type}: ${path}`);
    this._localDiffs.set(path, type);
  }

  /**
   * 更新 Merkle 树，并在必要时与服务端同步数据。
   * buildStatus 为 true 表示同步成功，false 表示同步失败 非fail skip 都算成功
   */
  protected async _updateAndSync() {
    this._logger.log('[_updateAndSync] start');

    const updated = await this._updateTree();
    if (this._tree?.hash && (updated || (this._tree.hash && this._lastSyncHash !== this._tree.hash))) {
      const buildId = uuidv4();
      const startTime = Date.now();
      this._onBuildIndex.fire({
        status: 'loading',
        rootHash: this._tree.hash,
        uid: this._uid,
        did: this._did,
        label: this.label,
        repoInfo: this._repoInfo,
        lastUpdated: new Date().toISOString(),
        cost: 0,
        buildId,
      });
      const buildStatus = await this._sync();
      // 非fail loading 提前return走 就应该是success
      this._onBuildIndex.fire({
        status: buildStatus ? 'success' : 'failed',
        rootHash: this._tree.hash,
        uid: this._uid,
        did: this._did,
        label: this.label,
        repoInfo: this._repoInfo,
        lastUpdated: new Date().toISOString(),
        cost: Date.now() - startTime,
        buildId,
      });
      return;
    }

    this._logger.log('[_updateAndSync] no update, skip sync');
  }

  /**
   * 根据本地差异映射增量更新 Merkle 树，如果树未构建则完全构建一个新树。
   * @returns 指示树是否已更新的布尔值。
   */
  protected async _updateTree(): Promise<boolean> {
    const logger = this._logger.derive('[_updateTree]');
    const updateEvent = new PerfEvent<UpdateEventParams>(logger, UpdateEventName);
    if (this._processing) {
      updateEvent.emit({
        status: 'skip',
        reason: 'merkle tree is processing',
      });
      return false;
    }

    if (!this._tree) {
      updateEvent.emit({
        status: 'skip',
        reason: 'tree not built',
      });
      const tree = await this._buildTree();
      return Boolean(tree);
    }

    if (this._localDiffs.size === 0) {
      updateEvent.emit({
        status: 'skip',
        reason: 'no local diff',
      });
      return false;
    }

    this._processing = true;
    const localDiffs = this._localDiffs;
    this._localDiffs = new Map();
    const oldHash = this._tree.hash;
    await updateMerkleTree(this.root, this._tree, localDiffs, undefined, this._createLimitFn());
    updateEvent.emit({
      status: 'success',
      diffCount: localDiffs.size,
      rootHash: this._tree.hash,
    });
    this._processing = false;
    return this._tree.hash !== oldHash;
  }

  /**
   * 完全构建一个新的 Merkle 树，并更新相关状态。
   * @returns 构建成功的 Merkle 树节点，失败则返回 null。
   */
  protected async _buildTree(): Promise<MerkleNode | null> {
    const logger = this._logger.derive('[_buildTree]');
    const buildEvent = new PerfEvent<BuildEventParams>(logger, BuildEventName);

    if (this._processing) {
      buildEvent.emit({
        status: 'skip',
        reason: 'merkle tree is processing',
      });
      return null;
    }

    try {
      this._processing = true;
      this._ignoreMap.clear();
      this._regexMap.clear();
      const countObj = { trackedFiles: 0, trackedDirs: 0 };
      const root: MerkleNode = {
        path: '.',
        hash: '',
        type: 'dir',
        children: [],
      };
      for (const watchPath of this.pathList) {
        const parentPatterns = await getIgnorePatternsRecursively(this._ignoreMap, this.root, path.dirname(watchPath));
        const treeNode = await buildMerkleTree(
          this.root,
          watchPath,
          parentPatterns,
          this._ignoreMap,
          this._regexMap,
          countObj,
          this._createLimitFn(),
        );
        logger.log(`build merkle tree for path: ${watchPath}, node hash: ${treeNode?.hash}`);
        if (treeNode) {
          await updateMerkleTreeByNode(root, treeNode);
          logger.log(`update merkle tree for path: ${watchPath}, root hash updated:${root?.hash}`);
        }
      }
      if (root.hash && root.children?.length) {
        this._tree = root;
        buildEvent.emit({
          status: 'success',
          ...countObj,
          rootHash: root.hash,
        });
        return this._tree;
      }
      this._tree = null;
      buildEvent.emit({
        status: 'fail',
        reason: 'tree is null',
      });
      return null;
    } catch (error) {
      buildEvent.emit({
        status: 'fail',
        reason: getErrorInfo(error).message,
      });
      return null;
    } finally {
      this._processing = false;
    }
  }

  /**
   * 将 Merkle 树和差异文件块同步到服务端。
   * 内部也会更新 tree
   */
  protected async _sync(): Promise<boolean> {
    const logger = this._logger.derive('[_sync]');
    let buildStatus = false;
    const syncEvent = new PerfEvent<SyncEventParams>(logger, SyncEventName);
    if (this._processing) {
      syncEvent.emit({
        status: 'skip',
        reason: 'merkle tree is processing',
      });
      return buildStatus;
    }

    this._processing = true;
    try {
      buildStatus = await this._doSync();
    } catch (ex) {
      logger.error(ex);
      syncEvent.emit({
        status: 'fail',
        reason: getErrorInfo(ex).message,
      });
    } finally {
      this._processing = false;
    }
    return buildStatus;
  }

  protected abstract _doSync(): Promise<boolean>;

  /**
   * 将当前的 Merkle 树序列化为 JSON 字符串。
   * @returns 序列化后的 Merkle 树字符串，如果树为空则返回空字符串。
   */
  protected async serializeTree(): Promise<string> {
    if (!this._tree) {
      return '';
    }
    return JSON.stringify(this._tree);
  }

  protected _createLimitFn(): (<T>(task: () => Promise<T>) => Promise<T>) | undefined {
    const limit = this._hashConcurrency > 0 ? pBatch(this._hashConcurrency, this._hashInterval) : undefined;
    return limit as unknown as any;
  }

  /**
   * 释放资源，清除定时器并销毁文件系统监听器。
   */
  public dispose() {
    this._logger.log('[_dispose] dispose');
    if (this._updateTimer) {
      clearInterval(this._updateTimer);
      this._updateTimer = null;
    }
    this._repoWatchers.forEach((watcher) => watcher.dispose());
    this._repoWatchers = [];
    this._onBuildIndex.dispose();
  }
}
