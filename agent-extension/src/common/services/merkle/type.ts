export interface MerkleNode {
  hash: string;
  path: string;
  children?: MerkleNode[];
  type: 'file' | 'dir';
}

// 索引DB的信息，注意一个 git repo 可能对应多个 db repo
// db 中的 repo 不代表真实的 git repo
export interface DBRepoInfo {
  gitRepoName: string; // 关联的 git repo name
  dbRepoName: string;
  pathList: string[];
}

export interface GitRepoInfo {
  gitRepoName: string;
  gitRepoRoot: string;
  gitRepoBranch: string;
}

export interface MerkleRepoInfo {
  db: DBRepoInfo;
  git: GitRepoInfo;
}

export type DiffType = 'modify' | 'add' | 'delete';
