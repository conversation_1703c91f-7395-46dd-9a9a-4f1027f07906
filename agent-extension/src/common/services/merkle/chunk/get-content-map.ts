import { getStatsInBatch, hashFileByBuffer } from '@byted-image/merkle';
import fs from 'node:fs/promises';
import path from 'node:path';
import type { GroupedRelatedPathInfo } from '../api/summary-get-update-files';
import type { GroupedRelatedFileInfo } from '../api/summary-update';

export interface IFileContent {
  hash: string;
  content: string;
}

export type IFileContentMap = Record<string, IFileContent>;

export async function getFileContentMap(
  rootPath: string,
  filePaths: string[],
  limit?: <T>(task: () => Promise<T>) => Promise<T>,
): Promise<IFileContentMap> {
  const map: IFileContentMap = {};
  const stats = await getStatsInBatch(rootPath, filePaths);
  const promises = filePaths.map(async (filePath, index) => {
    const stat = stats[index];
    if (!stat) {
      return null;
    }
    if (stat.isDirectory()) {
      return null;
    }
    try {
      const buffer = await fs.readFile(path.join(rootPath, filePath));
      const hash = limit ? await limit(() => hashFileByBuffer(buffer)) : await hashFileByBuffer(buffer);
      map[filePath] = {
        hash,
        content: buffer.toString(),
      };
    } catch (ex) {
      console.error(`[getFileContentMap] read file ${filePath} failed`, ex);
    }
  });
  await Promise.all(promises);
  return map;
}

/**
 * 基于 GroupedRelatedPathInfo 直接构建 GroupedRelatedFileInfo
 * 这个函数会收集每个 module_group 的 group_path 以及 sub_dir_paths 下每个目录的第一层文件和目录（不递归），并合并去重。
 * @param rootPath 根路径
 * @param groupedRelatedPathInfo 分组的相关路径信息
 * @returns 分组的相关文件信息
 */
export async function buildGroupedRelatedFileInfo(
  rootPath: string,
  groupedRelatedPathInfo: GroupedRelatedPathInfo,
): Promise<GroupedRelatedFileInfo> {
  const groupedRelatedFileInfo: GroupedRelatedFileInfo = {
    module_groups: [],
    leaf_groups: [],
  };

  // 处理模块组 - group_path 和 sub_dir_paths 下的每个目录都收集一层内容
  const moduleGroupPromises = groupedRelatedPathInfo.module_groups.map(async (group) => {
    const subFileInfos: Array<{ file_path: string; file_content: string }> = [];
    // 用 Set 去重
    const allDirs = new Set<string>([group.group_path, ...(group.sub_dir_paths || [])]);
    for (const dir of allDirs) {
      try {
        const fullGroupPath = path.join(rootPath, dir);

        // 先检查文件是否存在
        try {
          await fs.access(fullGroupPath);
        } catch (accessEx) {
          // 文件可能被删除了，符合预期
          console.warn(`[buildGroupedRelatedFileInfo] path does not exist: ${dir}`);
          continue;
        }

        const stat = await fs.stat(fullGroupPath);
        if (stat.isFile()) {
          // 如果是文件，直接读取
          const content = await fs.readFile(fullGroupPath);
          subFileInfos.push({
            file_path: dir,
            file_content: content.toString(),
          });
        } else if (stat.isDirectory()) {
          const entries = await fs.readdir(fullGroupPath, { withFileTypes: true });
          const filePromises: Array<Promise<void>> = [];
          entries.forEach((entry) => {
            const filePath = path.join(dir, entry.name);
            if (entry.isFile()) {
              filePromises.push(
                (async () => {
                  const fullFilePath = path.join(rootPath, filePath);
                  try {
                    const content = await fs.readFile(fullFilePath);
                    subFileInfos.push({
                      file_path: filePath,
                      file_content: content.toString(),
                    });
                  } catch (ex) {
                    console.error(`[buildGroupedRelatedFileInfo] read module file ${filePath} failed`, ex);
                    subFileInfos.push({
                      file_path: filePath,
                      file_content: '',
                    });
                  }
                })(),
              );
            } else if (entry.isDirectory()) {
              // 目录也需要记录一下
              subFileInfos.push({
                file_path: filePath + '/',
                file_content: '',
              });
            }
          });
          await Promise.all(filePromises);
        }
      } catch (ex) {
        console.error(`[buildGroupedRelatedFileInfo] read module path ${dir} failed`, ex);
      }
    }
    // 去重 subFileInfos（以 file_path 为 key）
    const uniqueFileInfos: Record<string, { file_path: string; file_content: string }> = {};
    subFileInfos.forEach((info) => {
      uniqueFileInfos[info.file_path] = info;
    });
    return {
      group_path: group.group_path,
      sub_file_infos: Object.values(uniqueFileInfos),
    };
  });

  // 处理叶子组 - 叶子目录下的文件递归读取所有层级
  const leafGroupPromises = groupedRelatedPathInfo.leaf_groups.map(async (group) => {
    // console.log(`[buildGroupedRelatedFileInfo] 处理叶子组: ${group.group_path}`);
    const subFileInfos: Array<{ file_path: string; file_content: string }> = [];

    try {
      const fullGroupPath = path.join(rootPath, group.group_path);
      // console.log(`[buildGroupedRelatedFileInfo] 叶子组完整路径: ${fullGroupPath}`);

      // 先检查文件是否存在
      try {
        await fs.access(fullGroupPath);
      } catch (accessEx) {
        // 文件可能被删除了，符合预期
        console.warn(`[buildGroupedRelatedFileInfo] leaf path does not exist: ${group.group_path}`);
        return {
          group_path: group.group_path,
          sub_file_infos: subFileInfos,
        };
      }

      const stat = await fs.stat(fullGroupPath);

      if (stat.isFile()) {
        // 如果是文件，直接读取
        const content = await fs.readFile(fullGroupPath);
        subFileInfos.push({
          file_path: group.group_path,
          file_content: content.toString(),
        });
      } else if (stat.isDirectory()) {
        subFileInfos.push({
          file_path: group.group_path + '/',
          file_content: '',
        });
        // 如果是目录，递归读取所有层级文件
        await readDirectoryRecursivelyForGroup(rootPath, group.group_path, subFileInfos);
      }
    } catch (ex) {
      console.error(`[buildGroupedRelatedFileInfo] read leaf path ${group.group_path} failed`, ex);
    }

    return {
      group_path: group.group_path,
      sub_file_infos: subFileInfos,
    };
  });

  // 等待所有操作完成
  const [moduleGroups, leafGroups] = await Promise.all([
    Promise.all(moduleGroupPromises),
    Promise.all(leafGroupPromises),
  ]);

  groupedRelatedFileInfo.module_groups = moduleGroups;
  groupedRelatedFileInfo.leaf_groups = leafGroups;
  return groupedRelatedFileInfo;
}

/**
 * 递归读取目录下的所有文件，用于叶子组
 * @param rootPath 根路径
 * @param currentPath 当前相对路径
 * @param subFileInfos 文件信息数组
 */
async function readDirectoryRecursivelyForGroup(
  rootPath: string,
  currentPath: string,
  subFileInfos: Array<{ file_path: string; file_content: string }>,
) {
  try {
    const fullPath = path.join(rootPath, currentPath);
    const entries = await fs.readdir(fullPath, { withFileTypes: true });

    const promises = entries.map(async (entry) => {
      const entryPath = path.join(currentPath, entry.name);

      if (entry.isFile()) {
        // 读取文件内容
        const fullFilePath = path.join(rootPath, entryPath);
        try {
          const content = await fs.readFile(fullFilePath);
          subFileInfos.push({
            file_path: entryPath,
            file_content: content.toString(),
          });
        } catch (ex) {
          console.error(`[readDirectoryRecursivelyForGroup] read file ${entryPath} failed`, ex);
          subFileInfos.push({
            file_path: entryPath,
            file_content: '',
          });
        }
      } else if (entry.isDirectory()) {
        // 递归读取子目录
        await readDirectoryRecursivelyForGroup(rootPath, entryPath, subFileInfos);
      }
    });

    await Promise.all(promises);
  } catch (ex) {
    console.error(`[readDirectoryRecursivelyForGroup] read directory ${currentPath} failed`, ex);
  }
}
