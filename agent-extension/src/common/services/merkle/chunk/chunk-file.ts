import { incrementalUpdate as chunk } from '@byted-image/codin-indexer';
import { compress } from '../compress/compress';
import type { IFileContentMap } from './get-content-map';
import fs from 'node:fs/promises';
import type { Dirent } from 'node:fs';

type SupportedLanguage = 'typescript' | 'golang' | 'swift' | 'kotlin';

async function detectLanguage(rootPath: string): Promise<SupportedLanguage> {
  // 定义每种语言对应的标志文件及优先级
  const languageFiles: { lang: SupportedLanguage; files: string[] }[] = [
    { lang: 'kotlin', files: ['build.gradle', 'build.gradle.kts', 'settings.gradle', 'settings.gradle.kts'] },
    { lang: 'typescript', files: ['tsconfig.json'] },
    { lang: 'golang', files: ['go.mod'] },
    { lang: 'swift', files: ['Podfile'] },
  ];

  let entries: Dirent[];
  try {
    entries = await fs.readdir(rootPath, { withFileTypes: true });
  } catch (e) {
    // 读取目录失败，默认返回 typescript
    return 'typescript';
  }

  for (const { lang, files } of languageFiles) {
    if (entries.some((entry) => entry.isFile() && files.includes(entry.name))) {
      return lang;
    }
  }

  // 如果都没有找到，默认返回 typescript
  return 'typescript';
}

export async function getChunkFile(
  repoName: string,
  rootPath: string,
  contentMap: IFileContentMap,
): Promise<{ chunk: ArrayBuffer | null; relations: ArrayBuffer | null }> {
  const language = await detectLanguage(rootPath);
  console.log('[getChunkFile] language', language);
  const chunkResp = await chunk(rootPath, contentMap, repoName, language);
  if (!chunkResp || chunkResp.chunks.length === 0) {
    return { chunk: null, relations: null };
  }
  const { chunks, relations } = chunkResp;

  const compressedChunk = await compress(JSON.stringify(chunks));
  const compressedRelations = await compress(JSON.stringify(relations));
  return {
    chunk: compressedChunk,
    relations: compressedRelations,
  };
}
