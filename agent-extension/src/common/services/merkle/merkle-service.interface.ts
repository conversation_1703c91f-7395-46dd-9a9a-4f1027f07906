import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { MerkleHolderSyncBuildIndexEvent } from './merkle-holder';
import type { Event } from '@byted-image/lv-bedrock/event';

export interface IMerkleService {
  readonly _serviceBrand: undefined;

  buildRecords: MerkleHolderSyncBuildIndexEvent[];

  onUpdateIndex: Event<[MerkleHolderSyncBuildIndexEvent]>;

  dispose(): void;
}

export const IMerkleService = createDecorator<IMerkleService>('merkle-service');
