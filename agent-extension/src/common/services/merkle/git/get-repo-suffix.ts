/**
 *
 * @param repoURL https://code.byted.org/ies/lvweb.git || ******************:ies/lvweb.git
 * @returns code.byted.org/ies/lvweb
 */
export function getRepoSuffix(repoURL: string) {
  if (repoURL.startsWith('https://')) {
    return repoURL.replace('https://', '').replace('.git', '');
  }
  if (repoURL.startsWith('git@')) {
    return repoURL.replace('git@', '').replace('.git', '').replace(':', '/');
  }
  return repoURL;
}
