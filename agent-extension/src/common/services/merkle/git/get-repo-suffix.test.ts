import { describe, it, expect } from 'vitest';
import { getRepoSuffix } from './get-repo-suffix';

describe('getRepoSuffix', () => {
  it('******************:ies/lvweb.git', async () => {
    const suffix = getRepoSuffix('******************:ies/lvweb.git');
    expect(suffix).toEqual('code.byted.org/ies/lvweb');
  });

  it('https://code.byted.org/ies/lvweb.git', async () => {
    const suffix = getRepoSuffix('https://code.byted.org/ies/lvweb.git');
    expect(suffix).toEqual('code.byted.org/ies/lvweb');
  });
});
