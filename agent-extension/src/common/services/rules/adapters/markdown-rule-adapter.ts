import { readFile, rename, rm } from 'node:fs/promises';
import path from 'node:path';
import { fileExistsAtPath } from '@utils/fs';
import { Uri, commands } from 'vscode';
import { projectRulesDirectory } from '../constants';
import type { IRuleAdapter } from '../rule-adapter.interface';
import type { Rule } from '../rules-service.interface';
import { ensureRuleFile } from '../utils/ensure-rule-file';

export class MarkdownRuleAdapter implements IRuleAdapter {
  public async getRuleContent(rule: Rule): Promise<string> {
    const filePath = this._getRuleAbsoluteFilePath(rule);
    console.log('rulesConfig filePath', filePath);

    await ensureRuleFile(filePath);

    const content = await readFile(filePath, 'utf-8');
    console.log('rulesConfig content', content);
    return content;
  }

  public beforeAddRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }

  public async afterAddRule(rule: Rule): Promise<void> {
    // 本地文件需要打开文件
    await this._openMarkdownRuleFile(rule);
  }

  public async beforeEditRule(oldRule: Rule, newRule: Rule): Promise<void> {
    if (newRule.markdown_file_name !== oldRule.markdown_file_name) {
      // 本地文件重命名
      const oldFilePath = this._getRuleAbsoluteFilePath(oldRule);
      const newFilePath = this._getRuleAbsoluteFilePath(newRule);
      const oldFileExists = await fileExistsAtPath(oldFilePath);
      if (oldFileExists) {
        await rename(oldFilePath, newFilePath);
      }
    }
  }

  public async afterEditRule(rule: Rule): Promise<void> {
    // 本地文件需要打开文件
    await this._openMarkdownRuleFile(rule);
  }

  public beforeDeleteRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }

  public async afterDeleteRule(rule: Rule): Promise<void> {
    // 本地文件需删除
    const filePath = this._getRuleAbsoluteFilePath(rule);
    const fileExists = await fileExistsAtPath(filePath);
    if (fileExists) {
      await rm(filePath);
    }
  }

  private _getRuleAbsoluteFilePath(rule: Rule): string {
    return path.resolve(projectRulesDirectory, rule.markdown_file_name!);
  }

  private async _openMarkdownRuleFile(rule: Rule) {
    const filePath = this._getRuleAbsoluteFilePath(rule);
    await ensureRuleFile(filePath);
    commands.executeCommand('vscode.open', Uri.file(filePath));
  }
}
