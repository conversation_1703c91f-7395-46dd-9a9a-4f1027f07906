import fetch from 'node-fetch-cache';
import type { IRuleAdapter } from '../rule-adapter.interface';
import type { Rule } from '../rules-service.interface';

export class UrlRuleAdapter implements IRuleAdapter {
  public async getRuleContent(rule: Rule): Promise<string> {
    const text = await this._fetchWithCache(rule.url!);
    return text;
  }
  public beforeAddRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }
  public afterAddRule(rule: Rule): Promise<void> {
    // async warmup
    this._fetchWithCache(rule.url!);
    return Promise.resolve();
  }
  public beforeEditRule(_oldRule: Rule, _newRule: Rule): Promise<void> {
    return Promise.resolve();
  }
  public afterEditRule(rule: Rule): Promise<void> {
    // async warmup
    this._fetchWithCache(rule.url!);
    return Promise.resolve();
  }
  public beforeDeleteRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }
  public afterDeleteRule(_rule: Rule): Promise<void> {
    return Promise.resolve();
  }

  private async _fetchWithCache(url: string) {
    const response = await fetch(url);
    return response.text();
  }
}
