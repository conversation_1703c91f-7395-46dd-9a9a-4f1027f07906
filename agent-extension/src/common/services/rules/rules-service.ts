import fs from 'node:fs';
import path from 'node:path';
import { lvAssertNotHere } from '@byted-image/lv-bedrock/assert';
import { Uri, commands } from 'vscode';

import { MarkdownRuleAdapter } from './adapters/markdown-rule-adapter';
import { RelativePathRuleAdapter } from './adapters/relative-path-rule-adapter';
import { UrlRuleAdapter } from './adapters/url-rule-adapter';
import { projectRulesDirectory, rulesConfigJsonPath } from './constants';
import { IRulesService, type Rule, type RuleType } from './rules-service.interface';
import { ensureProjectRuleDirectory } from './utils/ensure-project-rule-directory';
import { ensureRuleFile } from './utils/ensure-rule-file';
import { ensureRulesConfigJson } from './utils/ensure-rules-config-json';
import { isMarkdownRule } from './utils/is-markdown-rule';
import { isRelativePathRule } from './utils/is-relative-path-rule';
import { isUrlRule } from './utils/is-url-rule';

export class RulesService implements IRulesService {
  public _serviceBrand: undefined;

  public readonly rulesConfigPath = rulesConfigJsonPath;

  public readonly projectRulesDirectory = projectRulesDirectory;

  private readonly _markdownRuleAdapter = new MarkdownRuleAdapter();
  private readonly _urlRuleAdapter = new UrlRuleAdapter();
  private readonly _relativePathRuleAdapter = new RelativePathRuleAdapter();

  constructor() {
    ensureProjectRuleDirectory();
  }

  public async ensureRulesConfigJson(): Promise<void> {
    await ensureRulesConfigJson();
  }

  public getRulesConfig(type?: RuleType): Rule[] {
    try {
      const rulesConfig = fs.readFileSync(this.rulesConfigPath, 'utf-8');
      const rules = JSON.parse(rulesConfig);
      console.log('rulesConfig', this.rulesConfigPath, rules);
      if (type) {
        return rules.filter((rule: Rule) => rule.type === type);
      }

      return rules;
    } catch (error) {
      console.info('no rulesConfig', (error as Error)?.message);
      return [];
    }
  }

  private _saveAllRulesConfig(rules: Rule[]) {
    try {
      fs.writeFileSync(this.rulesConfigPath, JSON.stringify(rules, null, 2), 'utf-8');
    } catch (error) {
      console.error('saveAllRulesConfig', error);
      throw error;
    }
  }

  public async addRule(rule: Rule) {
    const allRules = this.getRulesConfig();
    if (allRules.some((item) => item.id === rule.id)) {
      throw new Error(`addRule id conflict. id: ${rule.id}`);
    }

    const adapter = this._getRuleAdapter(rule);
    await adapter.beforeAddRule(rule);

    allRules.push(rule);
    this._saveAllRulesConfig(allRules);

    await adapter.afterAddRule(rule);
  }

  public async editRule(rule: Rule) {
    const allRules = this.getRulesConfig();
    const index = allRules.findIndex((item) => item.id === rule.id);
    if (index < 0) {
      throw new Error(`Cannot find rule. rule: ${rule.name}, id: ${rule.id}`);
    }
    const oldRule = allRules[index];

    const adapter = this._getRuleAdapter(rule);
    await adapter.beforeEditRule(oldRule, rule);

    allRules.splice(index, 1, rule);
    this._saveAllRulesConfig(allRules);

    await adapter.afterEditRule(rule);
  }

  public async deleteRule(rule: Rule) {
    const allRules = this.getRulesConfig();
    const index = allRules.findIndex((item) => item.id === rule.id);
    if (index < 0) {
      throw new Error(`Cannot find rule. rule: ${rule.name}, id: ${rule.id}`);
    }

    const adapter = this._getRuleAdapter(rule);
    await adapter.beforeDeleteRule(rule);

    allRules.splice(index, 1);
    this._saveAllRulesConfig(allRules);

    await adapter.afterDeleteRule(rule);
  }

  public async openMarkdownRuleFile(rule: Rule) {
    const filePath = path.resolve(this.projectRulesDirectory, rule.markdown_file_name!);
    await ensureRuleFile(filePath);
    commands.executeCommand('vscode.open', Uri.file(filePath));
  }

  public async getRuleContentByName(ruleName: string): Promise<string> {
    const rulesConfig = this.getRulesConfig();
    const ruleConfig = rulesConfig.find((r) => r.name === ruleName);
    if (!ruleConfig) {
      return '';
    }

    return this.getRuleContentByConfig(ruleConfig);
  }

  public async getRuleContentByConfig(rule: Rule): Promise<string> {
    try {
      const adapter = this._getRuleAdapter(rule);
      const content = await adapter.getRuleContent(rule);
      return content;
    } catch (error) {
      console.error(error);
      return ''; // INFO: 服务可以接受异常
    }
  }

  public async getRulesContentByType(type: RuleType): Promise<string[]> {
    const rulesConfig = this.getRulesConfig(type);
    console.log('rulesConfig', rulesConfig);
    const rulesContent = await Promise.all(rulesConfig.map((rule) => this.getRuleContentByConfig(rule)));

    return rulesContent.filter((content) => content !== '');
  }

  private _getRuleAdapter(rule: Rule) {
    switch (true) {
      case isMarkdownRule(rule):
        return this._markdownRuleAdapter;
      case isUrlRule(rule):
        return this._urlRuleAdapter;
      case isRelativePathRule(rule):
        return this._relativePathRuleAdapter;
      default:
        lvAssertNotHere(`invalid rule storage type. rule: ${rule.name}, id: ${rule.id}, type: ${rule.storage_type}`);
    }
  }
}
