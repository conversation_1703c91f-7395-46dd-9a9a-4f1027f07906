import { mkdir } from 'node:fs/promises';
import { createDirectoriesForFile, fileExistsAtPath } from '@/utils/fs';
import { projectRulesDirectory } from '../constants';

export async function ensureProjectRuleDirectory() {
  await createDirectoriesForFile(projectRulesDirectory);
  const dirExists = await fileExistsAtPath(projectRulesDirectory);
  if (!dirExists) {
    await mkdir(projectRulesDirectory);
  }
}
