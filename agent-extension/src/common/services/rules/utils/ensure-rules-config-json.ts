import { writeFile } from 'node:fs/promises';
import { createDirectoriesForFile, fileExistsAtPath } from '@/utils/fs';
import { rulesConfigJsonPath } from '../constants';

export async function ensureRulesConfigJson() {
  await createDirectoriesForFile(rulesConfigJsonPath);
  const fileExists = await fileExistsAtPath(rulesConfigJsonPath);
  if (!fileExists) {
    await writeFile(rulesConfigJsonPath, '[]');
  }
}
