import type { Rule } from './rules-service.interface';

export interface IRuleAdapter {
  getRuleContent(rule: Rule): Promise<string>;

  beforeAddRule(rule: Rule): Promise<void>;

  afterAddRule(rule: Rule): Promise<void>;

  beforeEditRule(oldRule: Rule, newRule: Rule): Promise<void>;

  afterEditRule(rule: Rule): Promise<void>;

  beforeDeleteRule(rule: Rule): Promise<void>;

  afterDeleteRule(rule: Rule): Promise<void>;
}
