import { createDecorator } from '@byted-image/lv-bedrock/di';

export enum RuleType {
  Auto = 'auto',
  Manual = 'manual',
  Always = 'always',
}

export enum StorageType {
  Markdown = 'markdown',
  Url = 'url',
  RelativePath = 'relativePath',
}

export interface Rule {
  id: string;
  name: string;
  // type auto only
  description?: string;
  // 规则类型，auto: 自动，manual: 手动，always: 总是执行
  type: RuleType;
  // 存储类型，markdown: 文件, url: url, relativePath: 相对路径
  storage_type: StorageType;
  // markdown文件名
  markdown_file_name?: string;
  // 相对路径
  relative_path?: string;
  // url
  url?: string;
}

export interface MarkdownRule extends Rule {
  storage_type: StorageType.Markdown;
  markdown_file_name: string;
}

export interface UrlRule extends Rule {
  storage_type: StorageType.Url;
  url: string;
}

export interface RelativePathRule extends Rule {
  storage_type: StorageType.RelativePath;
  relative_path: string;
}

export interface IRulesService {
  _serviceBrand: undefined;

  rulesConfigPath: string;

  projectRulesDirectory: string;

  ensureRulesConfigJson(): Promise<void>;

  getRulesConfig(type?: RuleType): Rule[];

  addRule(rule: Rule): Promise<void>;

  editRule(rule: Rule): Promise<void>;

  deleteRule(rule: Rule): Promise<void>;

  openMarkdownRuleFile(rule: Rule): Promise<void>;

  // 获取规则
  getRuleContentByName(ruleName: string): Promise<string>;

  getRuleContentByConfig(rule: Rule): Promise<string>;

  // 获取规则列表
  getRulesContentByType(type: RuleType): Promise<string[]>;
}

export const IRulesService = createDecorator<IRulesService>('rules-service');
