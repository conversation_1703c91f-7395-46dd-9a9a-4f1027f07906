import { IDevService } from './dev-service.interface';

export class DevService implements IDevService {
  public _serviceBrand: undefined;

  private _userSystemprompt?: string;

  constructor() {
    this._checkAndSetFromUrl();
  }

  public get userSystemprompt() {
    return this._userSystemprompt;
  }

  public setUserSystemprompt(val: string) {
    this._userSystemprompt = val;

    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('ssp', '0');
      window.history.replaceState({}, '', url.toString());
    }
  }

  public clearUserSystemprompt() {
    this._userSystemprompt = undefined;

    // 设置URL参数ssp=1
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('ssp', '1');
      window.history.replaceState({}, '', url.toString());
    }
  }

  /**
   * 根据URL查询参数设置userSystemprompt
   * ssp=1: userSystemprompt为undefined
   * ssp=0: userSystemprompt为空字符串
   */
  private _checkAndSetFromUrl() {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      const sspParam = url.searchParams.get('ssp');

      if (sspParam === '1') {
        this._userSystemprompt = undefined;
      } else if (sspParam === '0') {
        this._userSystemprompt = '';
      }
    }
  }
}
