import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { Event } from '@byted-image/lv-bedrock/event';
import type { ISSOUserProfile } from './type';

export interface IAccountService {
  readonly _serviceBrand: undefined;

  /** 服务初始化 */
  initialize: () => Promise<void>;

  /** 是否已登录 */
  hasLogin: boolean;

  /** 读取JWT */
  getJwt: () => string | undefined;

  /**
   * 读取用户信息
   */
  getUserInfo: () => ISSOUserProfile | undefined;

  /** 登录 */
  login: () => Promise<void>;

  /** 登出 */
  logout: () => Promise<void>;

  /** 登录成功后发出的事件 */
  onDidLogin: Event<[]>;

  /** 登出成功后发出的事件 */
  onDidLogout: Event<[]>;
}

export const IAccountService = createDecorator<IAccountService>('account-service');
