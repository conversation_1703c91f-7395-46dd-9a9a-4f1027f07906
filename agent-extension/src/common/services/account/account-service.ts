import { Disposable } from '@byted-image/lv-bedrock/dispose';
import { Emitter, Event } from '@byted-image/lv-bedrock/event';
import { defer } from '@byted-image/lv-bedrock/promise';
import { Uri, env } from 'vscode';
import { convertData, toCamelCase } from '../../../common/utils/spell-parser';
import { ILocalServerService } from '../local-server/local-server-service.interface';
import { IStorageService } from '../storage/storage-service.interface';
import type { IAccountService } from './account-service.interface';
import type { ISSOUserProfile } from './type';

const JWT_STORAGE_KEY = 'codin_lark_sso_jwt';

export class AccountService extends Disposable implements IAccountService {
  public readonly _serviceBrand: undefined;

  private readonly _onDidLogin = this._register(new Emitter<[]>());
  private readonly _onDidLogout = this._register(new Emitter<[]>());
  public readonly onDidLogin: Event<[]> = this._onDidLogin.event;
  public readonly onDidLogout: Event<[]> = this._onDidLogout.event;

  private readonly _initializeDefer = defer<void>();
  private _jwtToken: string | undefined;
  private _userInfo: ISSOUserProfile | undefined;
  private _hasLogin = false;

  constructor(
    @IStorageService private readonly _storageService: IStorageService,
    @ILocalServerService private readonly _localServerService: ILocalServerService,
  ) {
    super();
    this._initialize();
  }

  public initialize() {
    return this._initializeDefer.promise;
  }

  private async _initialize() {
    try {
      await this._safeMigrateJwtStorage();
      const cacheJwtToken = await this._storageService.get(JWT_STORAGE_KEY);

      if (!cacheJwtToken) {
        await this.login();
        await this._getUserInfo();

        return;
      }

      this._jwtToken = cacheJwtToken;

      await this._getUserInfo();
      if (this._userInfo) {
        this._hasLogin = true;
        this._onDidLogin.fire();
      } else {
        this._hasLogin = false;
      }
    } catch (error) {
      console.error('[AccountService] login error', error);
      throw error;
    } finally {
      this._initializeDefer.resolve();
    }
  }

  private async _safeMigrateJwtStorage() {
    try {
      const cacheJwtToken = await this._storageService.secretGet(JWT_STORAGE_KEY);
      if (!cacheJwtToken) {
        return;
      }

      await this._storageService.set(JWT_STORAGE_KEY, cacheJwtToken);
      await this._storageService.secretDelete(JWT_STORAGE_KEY);
    } catch (error) {
      console.error('[AccountService] migrate jwt storage error', error);
    }
  }

  public get hasLogin() {
    return this._hasLogin;
  }

  public getJwt() {
    return this._jwtToken;
  }

  public getUserInfo() {
    return this._userInfo;
  }

  public async login(): Promise<void> {
    const jwtToken = await this._getJwtToken();
    this._jwtToken = jwtToken;
    await this._storageService.set(JWT_STORAGE_KEY, jwtToken);
    this._hasLogin = true;
  }

  public async logout(): Promise<void> {
    this._hasLogin = false;
    this._jwtToken = undefined;
    this._userInfo = undefined;
    await this._storageService.delete(JWT_STORAGE_KEY);
    this._onDidLogout.fire();
  }

  private async _getJwtTokenByCode(code: string) {
    const response = await fetch(this._getJwtTokenUrl(), {
      method: 'POST',
      body: JSON.stringify({
        code,
        redirect_uri: await this._getRedirectUri(),
      }),
    });

    if (!response.ok) {
      console.log('get token error', response);
      throw new Error('get token error');
    }

    if (!response.body) {
      console.error('Response body is null');
      throw new Error('Response body is null');
    }

    const result = await response.json();
    const jwt = result.data.token;
    console.log('get jwt token success', jwt);
    return jwt;
  }

  private async _getAuthorizationCode(): Promise<string> {
    const authConfig = await this._getAuthConfig();

    await this._ensureLocalServer();

    env.openExternal(
      Uri.parse(
        `https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=${authConfig.clientId}&redirect_uri=${encodeURIComponent(await this._getRedirectUri())}&scope=${authConfig.scope}`,
      ),
    );

    const getCodePromise = new Promise<string>((res) => {
      const disposable = this._localServerService.onRequest(async (request) => {
        const { path, query } = request;

        if (path === '/api/account/login' && query.code) {
          res(query.code as string);
          disposable.dispose();
        }
      });
      this._register(disposable);
    });

    return Promise.race([
      getCodePromise,
      new Promise<string>((_, rej) => {
        setTimeout(() => {
          rej(new Error('get code timeout'));
        }, 20000);
      }),
    ]);
  }

  private async _ensureLocalServer() {
    await this._localServerService.initialize();
  }

  private async _getJwtToken() {
    const code = await this._getAuthorizationCode();
    const jwtToken = await this._getJwtTokenByCode(code);

    return jwtToken;
  }

  private async _getAuthConfig() {
    const authConfig = await fetch(this._getAuthConfigUrl(), {
      method: 'post',
    });

    if (authConfig.status !== 200) {
      console.log('get auth config failed', authConfig.status);
      return;
    }

    const result = await authConfig.json();
    const { code, data } = convertData(result, toCamelCase);

    if (code !== 200) {
      console.log('get auth config failed', code);
      return;
    }

    return data;
  }

  private _getJwtTokenUrl() {
    return 'https://capcut-devops.byted.org/user/getjwt';
  }

  private async _getRedirectUri() {
    let uri = Uri.parse(`http://localhost:${this._localServerService.port}/api/account/login`);
    if (typeof env.asExternalUri === 'function') {
      uri = await env.asExternalUri(uri);
    }
    return uri.toString();
  }

  private _getAuthConfigUrl() {
    return 'https://capcut-devops.byted.org/user/auth_config';
  }

  private async _getUserInfo() {
    const res = await fetch('https://capcut-devops.byted.org/user/get_info', {
      method: 'POST',
      headers: {
        'x-jwt-token': this._jwtToken!,
      },
    });

    if (res.status !== 200) {
      console.log('get user info failed', res.status);
      return;
    }

    const result = await res.json();
    const { code, data } = convertData(result, toCamelCase);

    if (code !== 200) {
      console.log('get user info failed', code);
      return;
    }

    const userInfo = data.userInfo;
    this._userInfo = userInfo;
    console.log('get user info success');
  }
}
