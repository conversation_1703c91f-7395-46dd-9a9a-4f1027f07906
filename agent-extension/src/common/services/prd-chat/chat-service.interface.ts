import { ModelType } from '@/common/constants/model-types';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import { Event } from '@byted-image/lv-bedrock/event';
import { AskMessage, type IConversationContextState } from '../base-chat/types';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import type { Int64 } from '@/bam';
import type { PrdConversationContext } from '../conversation/prd-conversation-context/prd-conversation-context';

export const DefaultModelType = ModelType.Claude4_Sonnet;

interface FileOptions {
  key: string;
  name: string;
  bucket: string;
  url: string;
}

export interface SendMessageParams {
  model?: ModelType;
  files?: FileOptions[];
  isSubAgent?: boolean;
}

export interface IPrdChatService {
  _serviceBrand: undefined;

  currentCid: string;

  // 发送消息
  sendMessage(message: AskMessage, params?: SendMessageParams): void;

  getMessages(): ClientMessage[];

  resetMessageReceiverAndSocket(): void;

  onPresentAssistantMessage: Event<[]>;

  onSendMessageError: Event<ILvErrorOr<void>[]>;

  switchCurrentConversation(cid?: string): Promise<ILvErrorOr<string>>;

  getCurrentContextState(): Promise<IConversationContextState>;

  createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>>;

  getConversationContext(cid: string): PrdConversationContext | null;

  getConversationBusiness(cid: string): Promise<string>;

  setConversationBusiness(cid: string, business: string): Promise<void>;
}

export const IPrdChatService = createDecorator<IPrdChatService>('prd-chat-service');
