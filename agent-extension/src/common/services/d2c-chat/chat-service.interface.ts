import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import { Event } from '@byted-image/lv-bedrock/event';
import type { ID2CSettings } from './settings';
import { AskMessage, type IConversationContextState } from '../base-chat/types';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import type { Int64 } from '@/bam';
import type { BaseConversationContext } from '../conversation/base-conversation-context/base-conversation-context';

export interface ID2cChatService {
  _serviceBrand: undefined;

  currentCid: string;

  // 发送消息
  sendMessage(message: AskMessage): void;

  getMessages(): ClientMessage[];

  resetMessageReceiverAndSocket(): void;

  onPresentAssistantMessage: Event<[]>;

  getSettings: () => ID2CSettings;

  saveSettings: (settings: ID2CSettings) => void;

  askUpdateFigmaToken: () => Promise<void>;

  onSendMessageError: Event<ILvErrorOr<void>[]>;

  switchCurrentConversation(cid?: string): Promise<ILvErrorOr<string>>;

  getCurrentContextState(): Promise<IConversationContextState>;

  createConversation(options?: {
    parentConversationId: string;
    parentMessageVersion: Int64;
  }): Promise<ILvErrorOr<string>>;

  getConversationContext(cid: string): BaseConversationContext | null;
}

export const ID2cChatService = createDecorator<ID2cChatService>('d2c-chat-service');
