import { describe, it, expect, vi, beforeEach, afterEach, type Mocked } from 'vitest';
import { RecentFileManager } from './recent-file';
import vscode from 'vscode';
import { IExtensionContextService } from '@/common/services/commands/extension-context.interface';
import { mock } from 'vitest-mock-extended';

const mockedVSCode = vscode as Mocked<typeof vscode>;

describe('RecentFileManager', () => {
  vi.useFakeTimers();

  let manager: RecentFileManager;
  let mockExtensionContextService: Mocked<IExtensionContextService>;
  let statSpy: ReturnType<typeof vi.spyOn>;
  let findFilesSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    vi.resetAllMocks();

    mockExtensionContextService = mock<IExtensionContextService>();
    mockExtensionContextService.context = {
      globalState: {
        get: vi.fn().mockImplementation((_key, defaultValue) => defaultValue),
        update: vi.fn().mockResolvedValue(undefined),
      },
    } as any;

    manager = new RecentFileManager(mockExtensionContextService);

    statSpy = vi.spyOn(mockedVSCode.workspace.fs, 'stat') as unknown as ReturnType<typeof vi.spyOn>;
    statSpy.mockResolvedValue({ type: vscode.FileType.File } as any);

    findFilesSpy = vi.spyOn(mockedVSCode.workspace, 'findFiles') as unknown as ReturnType<typeof vi.spyOn>;
    findFilesSpy.mockResolvedValue([]);

    (mockedVSCode.Uri.file as Mocked<any>).mockImplementation((p: any) => ({ fsPath: p, path: p }) as any);
    (mockedVSCode.Uri.joinPath as Mocked<any>).mockImplementation((base: any, ...parts: any[]) => ({
      ...base,
      fsPath: [base.fsPath, ...parts].join('/'),
      path: [base.path, ...parts].join('/'),
    }));
    vi.spyOn(vscode.workspace, 'asRelativePath').mockImplementation((p: any) => p.fsPath);

    (mockedVSCode.workspace as any).workspaceFolders = [
      { uri: mockedVSCode.Uri.file('/root'), name: 'test-ws', index: 0 },
    ];
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should initialize without errors', async () => {
    await manager.init([]);
    expect(true).toBe(true);
  });

  it('should add a file and its parent directory via handleFileAccess', async () => {
    const fileUri = mockedVSCode.Uri.file('/root/src/index.ts');
    statSpy.mockImplementation(
      async (uri: any) =>
        ({ type: uri.fsPath.includes('.') ? vscode.FileType.File : vscode.FileType.Directory }) as any,
    );

    await (manager as any).handleFileAccess(fileUri);
    await vi.runAllTimersAsync();

    const recentFiles = manager.getRecentFiles();
    expect(recentFiles).toHaveLength(2);
    expect(recentFiles[0].path).toBe('/root/src');
    expect(recentFiles[1].path).toBe('/root/src/index.ts');
  });

  it('should not add ignored files', async () => {
    await manager.init(['/root/src/index.ts']);
    const isIgnored = (manager as any).gitignorePatterns.some((p: any) => '/root/src/index.ts'.includes(p));
    expect(isIgnored).toBe(true);
  });

  it('should load files from storage and remove non-existent ones', async () => {
    const storedFiles = [
      { path: '/root/exists.ts', name: 'exists.ts', relativePath: 'exists.ts', type: 'file' },
      { path: '/root/non-existent.ts', name: 'non-existent.ts', relativePath: 'non-existent.ts', type: 'file' },
    ];
    (mockExtensionContextService.context.globalState.get as Mocked<any>).mockImplementation((key: any) => {
      if (key.includes('recentFiles')) return storedFiles;
      return {};
    });

    statSpy.mockImplementation(async (uri: any) => {
      if (uri.fsPath === '/root/exists.ts') {
        return { type: vscode.FileType.File } as vscode.FileStat;
      }
      throw new Error('File not found');
    });

    await (manager as any).loadFromStorage();
    await vi.runAllTimersAsync();

    const recentFiles = manager.getRecentFiles();
    expect(recentFiles).toHaveLength(1);
    expect(recentFiles[0].path).toBe('/root/exists.ts');
  });

  it('should fill recent files from workspace', async () => {
    // Force reset internal state for this specific test to ensure isolation
    (manager as any).recentFiles = [];
    (manager as any).gitignorePatterns = [];

    const fileUri = mockedVSCode.Uri.file('/root/workspace-file.ts');
    findFilesSpy.mockResolvedValue([fileUri]);

    await (manager as any).fillRecentFilesFromWorkspace();
    await vi.runAllTimersAsync();

    const recentFiles = manager.getRecentFiles();
    expect(recentFiles).toHaveLength(1);
    expect(recentFiles[0].path).toBe('/root/workspace-file.ts');
  });

  it('should handle error when filling from workspace', async () => {
    // Force reset internal state for this specific test to ensure isolation
    (manager as any).recentFiles = [];
    (manager as any).gitignorePatterns = [];

    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    findFilesSpy.mockRejectedValue(new Error('Find files failed'));

    await (manager as any).fillRecentFilesFromWorkspace();
    await vi.runAllTimersAsync();

    expect(consoleErrorSpy).toHaveBeenCalledWith('Error filling recent files from workspace:', expect.any(Error));
    consoleErrorSpy.mockRestore();
  });

  it('should trim the list to max size', async () => {
    const MAX_FILES = 20;
    for (let i = 0; i < MAX_FILES + 5; i++) {
      await (manager as any).addRecentFile({ path: `/f${i}`, name: `f${i}`, relativePath: `f${i}`, type: 'file' });
    }
    await vi.runAllTimersAsync();
    expect(manager.getRecentFiles().length).toBe(MAX_FILES);
  });
});
