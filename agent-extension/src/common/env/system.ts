import os from 'node:os';
import path from 'node:path';
import vscode from 'vscode';
import { getShell } from './shell';
import { execSync } from 'node:child_process';
import type { IRepoInfo } from '../services/workspace/workspace.service';

const localeMap: Record<string, string> = {
  zh_CN: '中文',
  en_US: 'English',
};

function getUserLanguage() {
  const defaultLanguage = localeMap.zh_CN;

  try {
    if (process.platform === 'darwin') {
      return getMacUserLanguage() ?? defaultLanguage;
    }

    return defaultLanguage;
  } catch {
    return defaultLanguage;
  }
}

function getMacUserLanguage() {
  const stdout = execSync('defaults read -g AppleLocale', {
    encoding: 'utf-8',
  });

  return localeMap[stdout.trim()];
}

// may or may not exist but fs checking existence would immediately ask for permission which would be bad UX, need to come up with a better solution
export const cwd =
  vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath).at(0) ?? path.join(os.homedir(), 'Desktop');

export function getSystemEnv(repoInfos?: IRepoInfo[]) {
  let workspaceInfo = '';
  if (repoInfos && repoInfos.length > 0) {
    workspaceInfo += 'Workspace Information:\n';
    workspaceInfo += '  - Workspace Repo(s) Information:\n';
    for (let index = 0; index < repoInfos.length; index++) {
      const repo = repoInfos[index];
      workspaceInfo += `    ${index + 1}. Repository Name: ${repo.repoName},Repository Root Absolute Path: ${repo.path.toPosix()}, Repository Current Branch: ${repo.branch}\n`;
    }
  }

  return `
    User System Language: ${getUserLanguage()}
    Operating System: ${os.platform()} ${os.release()}
    Default Shell: ${getShell()}
    Home Directory: ${os.homedir().toPosix()}
    Current Working Workspace Directory: ${cwd.toPosix()}
    ${workspaceInfo}
  `;
}
