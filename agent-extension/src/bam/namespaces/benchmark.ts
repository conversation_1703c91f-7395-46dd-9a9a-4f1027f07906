// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from "./base";

export type Int64 = string | number;

export enum TaskStatus {
  Generating = 0,
  Success = 1,
  Failed = 2,
  Canceled = 3,
}

/** 批量创建基准测试任务请求项 */
export interface BatchCreateTaskItem {
  /** 需要支持多仓定义 */
  changes: Array<Change>;
  /** RFC描述 */
  rfc: string;
}

/** 批量创建任务结果项 */
export interface BatchCreateTaskResult {
  /** 会话ID（成功时返回） */
  conversation_id?: string;
  /** 错误信息（失败时返回） */
  error_message?: string;
}

/** 基准测试报告 */
export interface BenchmarkReport {
  conversation_id: string;
  /** 任务状态 */
  status: TaskStatus;
  message: string;
  info?: ReportInfo;
}

/** socket响应 */
export interface BenchmarkResponse {
  base_resp?: base.BaseResp;
}

/** 代码变更信息 */
export interface Change {
  /** 仓库地址 */
  repo: string;
  /** 人写的代码commit */
  origin_commit: string;
  /** AI生成的代码commit */
  ai_commit: string;
}

export interface ConversationStats {
  /** 总消息数 */
  total_messages: number;
  /** 用户消息数 */
  user_messages: number;
  /** 助手消息数 */
  assistant_messages: number;
  /** 工具调用次数 */
  tool_calls: number;
  /** 会话时长, 秒单位 */
  session_duration: number;
  /** 开始时间 */
  start_time: Int64;
  /** 结束时间 */
  end_time: Int64;
}

/** 创建基准测试任务响应 */
export interface CreateBenchmarkTaskResponse {
  /** 会话ID */
  conversation_id: string;
  base_resp?: base.BaseResp;
}

/** 获取报告响应 */
export interface GetReportResponse {
  report: Array<BenchmarkReport>;
  base_resp?: base.BaseResp;
}

export interface PerformanceMetrics {
  /** 平均响应时间(ms) */
  response_time?: number;
  /** 成功率(%) */
  success_rate?: number;
  /** 总请求数 */
  total_requests?: number;
  /** 成功请求数 */
  successful_requests?: number;
  /** 失败请求数 */
  failed_requests?: number;
}

export interface ReportInfo {
  /** 会话统计信息 */
  stats: ConversationStats;
  /** 测评 Agent 的性能指标 */
  metrics: PerformanceMetrics;
  /** 报告摘要 */
  summary: string;
  /** 代码改进建议 */
  recommendations: string;
  /** 代码实现覆盖率 */
  coverage_rate: string;
  /** 代码质量 */
  code_quality: string;
}

/** 重试测试评估响应 */
export interface RetryBenchmarkResponse {
  base_resp?: base.BaseResp;
}
/* eslint-enable */
