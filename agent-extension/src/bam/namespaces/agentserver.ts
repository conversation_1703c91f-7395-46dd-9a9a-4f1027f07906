// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from "./base";
import * as agentclient from "./agentclient";
import * as userinput from "./userinput";

export type Int64 = string | number;

/** AgentInferenceParams agent推理中的通用参数 */
export interface AgentInferenceParams {
  /** 会话id */
  id: string;
  user_message?: UserMessage;
  /** 可能一次性多个工具调用，上行的是数组 */
  tool_messages?: Array<ToolMessage>;
  /** 模型类型，默认DeepSeek */
  model_type?: base.ModelType;
  client_env?: ClientEnv;
}

/** AgentRetryParams agent重试中的通用参数 */
export interface AgentRetryParams {
  /** 会话id */
  id: string;
  model_type?: base.ModelType;
  client_env?: ClientEnv;
}

export interface ClientEnv {
  /** 系统环境 */
  system_env?: string;
  tools?: Array<agentclient.ToolInfo>;
  project_rules?: Array<string>;
  figma_token?: string;
  repo_infos?: Array<RepoInfo>;
  did?: string;
  work_mode?: base.AgentWorkMode;
}

export interface RepoInfo {
  branch?: string;
  path?: string;
  path_list?: Array<string>;
  repo_name?: string;
}

export interface ToolMessage {
  content: string;
  request_id: string;
}

export interface UserMessage {
  content: userinput.UserInput;
}
/* eslint-enable */
