// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum GlobalInfoType {
  Image = 0,
  File = 1,
  Folder = 2,
}

export enum PromptItemType {
  Text = 0,
  File = 1,
  Folder = 2,
}

export interface File {
  path: string;
  content: string;
  name?: string;
}

export interface Folder {
  path: string;
  /** ls输出内容 */
  content: string;
  name?: string;
}

export interface GlobalInfo {
  /** 目前以base64编码形式存在 */
  type: GlobalInfoType;
  file?: File;
  folder?: Folder;
  image?: Image;
}

export interface Image {
  base64: string;
  name?: string;
}

/** 根据type -> 不同的结构体 */
export interface PromptItem {
  type: PromptItemType;
  text?: TextPromptItem;
  file?: File;
  folder?: Folder;
}

export interface TextPromptItem {
  text: string;
}

export interface UserInput {
  /** 用户输入框内容 */
  prompt?: UserPrompt;
  global_infos?: Array<GlobalInfo>;
}

/** 用户输入的prompt，因为允许输入多行，所以是二位数组 */
export interface UserPrompt {
  items: Array<Array<PromptItem>>;
}
/* eslint-enable */
