// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AgentWorkMode {
  Unknown = 0,
  PlanAct = 1,
  Act = 2,
}

export enum ModelType {
  DeepSeek = 0,
  Claude = 1,
  DoubaoPro = 2,
  DoubaoThinkingPro = 3,
  DeepSeekR1 = 4,
  GPT4_1 = 5,
  Gemini2_5_Pro = 6,
  Claude4_Sonnet = 7,
  Claude4_Opus = 8,
  GPT_O3_Pro = 9,
  GPT_O4_Mini = 10,
  Seed1_6_Thinking = 11,
  GPT_O3 = 12,
  Seed1_6 = 13,
  Seed1_6_For_Codeindex = 14,
  Seed1_6_For_Codesummary = 15,
  Seed1_6_For_Codesearch = 16,
  Kimi_K2 = 17,
}

export interface BaseResp {
  StatusMessage?: string;
  StatusCode?: number;
  Extra?: Record<string, string>;
}
/* eslint-enable */
