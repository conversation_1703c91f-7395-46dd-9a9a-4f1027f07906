// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from "./base";
import * as conversation from "./conversation";
import * as message from "./message";

export type Int64 = string | number;

/** 计算会话token响应 */
export interface CalcConversationTokensResponse {
  /** 会话id和token数量 */
  token_counts: Record<string, Array<TokenCount>>;
  base_resp?: base.BaseResp;
}

/** 取消正在进行的会话响应 */
export interface CancelConversationResponse {
  base_resp?: base.BaseResp;
}

/** 创建会话响应 */
export interface CreateConversationResponse {
  /** 会话id */
  id: string;
  base_resp?: base.BaseResp;
}

/** 获取历史会话列表响应 */
export interface GetConversationListResponse {
  /** 会话列表 */
  conversations: Array<conversation.Conversation>;
  base_resp?: base.BaseResp;
}

/** 获取历史会话响应 */
export interface GetConversationResponse {
  /** 会话信息 */
  conversation: conversation.Conversation;
  /** 当request中传入了message, 则返回消息列表，不会携带流式消息 */
  messages?: Array<message.Message>;
  /** 子会话列表 */
  sub_conversations?: Array<message.SubConversation>;
  base_resp?: base.BaseResp;
}

/** 补拉历史流式消息响应 */
export interface GetStreamRangeResponse {
  /** 消息列表 */
  messages: Array<message.Message>;
  base_resp?: base.BaseResp;
}

export interface TokenCount {
  /** 输入token数量 */
  input_count: number;
  /** 输出token数量 */
  output_count: number;
  /** 模型名称/接入点 */
  model: string;
}
/* eslint-enable */
