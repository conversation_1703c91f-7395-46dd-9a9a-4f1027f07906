// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 会话中的通用错误码 */
export enum ErrCode {
  Success = 0,
  /** agent启动前的错误
参数错误 */
  ArgumentError = 8000,
  /** 客户端toolresult id异常 */
  ClientToolResultIdError = 8001,
  /** 获取会话异常 */
  GetConversationFailed = 8002,
  /** 获取历史消息异常 */
  GetMessagesFailed = 8003,
  /** 保存用户消息异常 */
  SaveUserMessageFailed = 8004,
  /** 上锁失败 */
  LockFailed = 8005,
  /** 会话不支持重试 */
  ConversationNotSupportRetry = 8006,
  /** 会话最后一条消息不能重试 */
  ConversationLastMessageNotRetry = 8007,
  /** agent推送中的错误
内部错误 */
  InternalError = 9000,
  /** 更新会话状态失败 */
  UpdateConversationStatusFailed = 9001,
  /** 保存流式数据失败 */
  SaveStreamFailed = 9002,
  /** 保存完整消息失败 */
  SaveMessageFailed = 9003,
  /** 取消 */
  Canceled = 9004,
  /** 服务商过载 */
  ServiceOverload = 9005,
  /** 当日token使用量超限 */
  DailyTokenUsageLimitExceeded = 9006,
  /** 当日使用次数超限 */
  DailyUsageLimitExceeded = 9007,
}
/* eslint-enable */
