// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 数据类型枚举 */
export enum DataType {
  /** "object" */
  Object = 0,
  /** "number" */
  Number = 1,
  /** "integer" */
  Integer = 2,
  /** "string" */
  String = 3,
  /** "array" */
  Array = 4,
  /** "null" */
  Null = 5,
  /** "boolean" */
  Boolean = 6,
}

/** 参数信息结构体 */
export interface ParameterInfo {
  type: DataType;
  desc?: string;
  required?: boolean;
  /** The enum values of the parameter, only for string. */
  enum?: Array<string>;
  /** The element type of the parameter, only for array. */
  elem_info?: ParameterInfo;
  /** The sub parameters of the parameter, only for object. */
  sub_params?: Record<string, ParameterInfo>;
}

/** 工具信息结构体 */
export interface ToolInfo {
  name: string;
  desc: string;
  params?: Record<string, ParameterInfo>;
}
/* eslint-enable */
