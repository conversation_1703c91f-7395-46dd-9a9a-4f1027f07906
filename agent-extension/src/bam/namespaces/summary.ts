// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface DirPathGroup {
  /** 组路径（module或subModule路径） */
  group_path: string;
  /** 该组下的所有子文件路径 */
  sub_dir_paths: Array<string>;
}

export interface GroupedRelatedPathInfo {
  /** 模块组列表 */
  module_groups: Array<DirPathGroup>;
  /** 叶子节点组列表 */
  leaf_groups: Array<DirPathGroup>;
}
/* eslint-enable */
