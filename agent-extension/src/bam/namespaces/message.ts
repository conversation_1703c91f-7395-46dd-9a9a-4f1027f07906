// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agenterrcode from "./agenterrcode";

export type Int64 = string | number;

export enum ContentType {
  Content = 0,
  Reasoning = 1,
  Image = 2,
  File = 3,
}

export enum PushType {
  NewMessage = 0,
  Error = 1,
  Eof = 2,
}

export interface ContentPart {
  type: ContentType;
  content?: string;
  reasoning_content?: string;
  image?: ImageContent;
  file?: FileContent;
}

export interface FileContent {
  url: string;
}

export interface ImageContent {
  url: string;
}

/** 完整结构的一条消息 */
export interface Message {
  version: Int64;
  /** 角色：user/assistant/system/tool */
  role: string;
  uid?: string;
  content?: Array<ContentPart>;
  tool_calls?: Array<ToolCall>;
  tool_call_id?: string;
  metadata?: Metadata;
  name?: string;
  /** 如果是垒包的消息，表示开始时间 */
  begin_at?: Int64;
  /** 如果是垒包的消息，表示结束时间，非垒包消息，end=begin */
  end_at?: Int64;
}

export interface Metadata {
  finish_reason?: string;
}

export interface StreamPushData {
  type: PushType;
  message?: Message;
  error_code?: agenterrcode.ErrCode;
  error_message?: string;
  conversation_version?: Int64;
}

export interface SubConversation {
  id: string;
  version: Int64;
}

export interface ToolCall {
  id: string;
  name?: string;
  input?: string;
}
/* eslint-enable */
