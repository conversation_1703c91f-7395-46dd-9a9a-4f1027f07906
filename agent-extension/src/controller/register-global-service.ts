import { CodingChatService } from '@/common/services/coding-chat/coding-chat-service';
import { ICodingChatService } from '@/common/services/coding-chat/coding-chat-service.interface';
import { GitService } from '@/common/services/git/git-service';
import { IGitService } from '@/common/services/git/git-service.interface';
import { D2cChatService } from '@/common/services/d2c-chat/chat-service';
import { ID2cChatService } from '@/common/services/d2c-chat/chat-service.interface';
import { DevService } from '@/common/services/dev/dev-service';
import { IDevService } from '@/common/services/dev/dev-service.interface';
import { FileContentService } from '@/common/services/file-content/file-content-service';
import { IFileContentService } from '@/common/services/file-content/file-content-service.interface';
import { McpHubService } from '@/common/services/mcp-hub/mcp-hub-service';
import { IMcpHubService } from '@/common/services/mcp-hub/mcp-hub-service.interface';
import { RulesService } from '@/common/services/rules/rules-service';
import { IRulesService } from '@/common/services/rules/rules-service.interface';
import { SocketService } from '@/common/services/socket/socket-service';
import { ISocketService } from '@/common/services/socket/socket-service.interface';
import type { ServiceRegistry } from '@byted-image/lv-bedrock/di';
import { SyncDescriptor } from '@byted-image/lv-bedrock/di';
import type { ExtensionContext } from 'vscode';

import { IPrdChatService } from '@/common/services/prd-chat/chat-service.interface';
import { IConversationCommandsService } from '@/common/services/commands/conversation-commands.interface';
import { ConversationCommandsService } from '@/common/services/commands/conversation-commands.service';
import { IExtensionContextService } from '@/common/services/commands/extension-context.interface';
import { ExtensionContextService } from '@/common/services/commands/extension-context.service';
import { IConversationHistoryService } from '@/common/services/conversation/history/conversation-history.interface';
import { VSCodeStorageHistoryService } from '@/common/services/conversation/history';
// import { RemoteStorageHistoryService } from '@/common/services/conversation/history/remote-storage-history.service';

import { PrdChatService } from '@/common/services/prd-chat/chat-service';

import { UnderstandingChatService } from '@/common/services/understanding-chat/understanding-service';
import { IUnderstandingChatService } from '@/common/services/understanding-chat/understanding-service.interface';

import { AccountService } from '@/common/services/account/account-service';
import { IAccountService } from '@/common/services/account/account-service.interface';
import { LocalServerService } from '@/common/services/local-server/local-server-service';
import { ILocalServerService } from '@/common/services/local-server/local-server-service.interface';
import { NetworkClientFactoryService } from '@/common/services/network-client-factory/network-client-factory-service';
import { INetworkClientFactoryService } from '@/common/services/network-client-factory/network-client-factory-service.interface';
import { IStorageService } from '@/common/services/storage/storage-service.interface';
import { VSCodeStorageService } from '@/vscode-integration/services/storage/vscode-storage-service';
import { MerkleService } from '@/common/services/merkle/merkle-service';
import { IMerkleService } from '@/common/services/merkle/merkle-service.interface';
import { IDiffViewService } from '@/common/services/diff-view/diff-view-service.interface';
import { DiffViewService } from '@/vscode-integration/services/diff-view/diff-view-service';
import { IWorkspaceFilesService } from '@/common/services/workspace-files/workspace-files.interface';
import { WorkspaceFilesService } from '@/common/services/workspace-files/workspace-files.service';
import { PrivateConversationService } from '@/private/private-conversation-service';
import { IPrivateConversationService } from '@/private/private-conversation-service.interface';
import { PrivateChatService } from '@/private/private-chat-service';
import { IPrivateChatService } from '@/private/private-chat-service.interface';
import { IRpcService } from '@/common/services/rpc/rpc-service.interface';
import { RpcService } from '@/common/services/rpc/rpc-service';
import { IWorkspaceService } from '@/common/services/workspace/workspace.interface';
import { WorkspaceService } from '@/common/services/workspace/workspace.service';
import { IIndexService } from '@/common/services/index/index-service.interface';
import { IndexService } from '@/common/services/index/index-service';
import { IFileLoggerService } from '@/common/services/file-logger/file-logger-service.interface';
import { FileLoggerService } from '@/common/services/file-logger/file-logger-service';
import { ICodesearchChatService } from '@/common/services/codesearch-chat/codesearch-chat.interface';
import { CodesearchChatService } from '@/common/services/codesearch-chat/codesearch-chat.service';
import { LoggerFactoryService } from '@/common/services/logger/logger.service';
import { ILoggerFactoryService } from '@/common/services/logger/logger.interface';

/**
 * 注册全局服务到DI容器
 * @param serviceRegistry DI服务注册表
 * @param context VSCode扩展上下文（必需）
 */
export function registerGlobalService(serviceRegistry: ServiceRegistry, context: ExtensionContext): void {
  if (!context) {
    throw new Error('ExtensionContext is required for VSCode extension');
  }

  // 基础服务
  const extensionContextService = new ExtensionContextService(context);
  serviceRegistry.registerInstance(IExtensionContextService, extensionContextService);
  serviceRegistry.register(IStorageService, VSCodeStorageService);
  serviceRegistry.register(IAccountService, AccountService);
  serviceRegistry.register(INetworkClientFactoryService, NetworkClientFactoryService);

  // 业务服务
  serviceRegistry.register(IFileLoggerService, FileLoggerService);
  serviceRegistry.register(IDiffViewService, DiffViewService);
  serviceRegistry.register(IPrdChatService, new SyncDescriptor(PrdChatService, [], false));
  serviceRegistry.register(IUnderstandingChatService, new SyncDescriptor(UnderstandingChatService, [], false));
  serviceRegistry.register(ICodingChatService, new SyncDescriptor(CodingChatService, [], false));
  serviceRegistry.register(IGitService, GitService);
  serviceRegistry.register(ID2cChatService, new SyncDescriptor(D2cChatService, [], false));
  serviceRegistry.register(IDevService, DevService);
  serviceRegistry.register(IConversationHistoryService, VSCodeStorageHistoryService);
  serviceRegistry.register(IPrivateConversationService, new SyncDescriptor(PrivateConversationService, [], false));
  serviceRegistry.register(IPrivateChatService, new SyncDescriptor(PrivateChatService, [], false));
  // serviceRegistry.register(IUploadService, UploadService); TODO: register upload service
  serviceRegistry.register(ISocketService, SocketService);
  serviceRegistry.register(IFileContentService, FileContentService);
  serviceRegistry.register(IRulesService, RulesService);
  serviceRegistry.register(IWorkspaceFilesService, WorkspaceFilesService);
  serviceRegistry.register(IIndexService, IndexService);
  serviceRegistry.register(
    IMcpHubService,
    new SyncDescriptor(
      McpHubService,
      [
        '1.0.0', // TODO: chezongshao 先固定版本号
      ],
      false,
    ),
  );
  serviceRegistry.register(IConversationCommandsService, ConversationCommandsService);
  serviceRegistry.register(ILocalServerService, new SyncDescriptor(LocalServerService));
  serviceRegistry.register(IMerkleService, MerkleService);
  serviceRegistry.register(IRpcService, RpcService);
  serviceRegistry.register(IWorkspaceService, WorkspaceService);
  serviceRegistry.register(ILoggerFactoryService, LoggerFactoryService);
  serviceRegistry.register(ICodesearchChatService, CodesearchChatService);
}
