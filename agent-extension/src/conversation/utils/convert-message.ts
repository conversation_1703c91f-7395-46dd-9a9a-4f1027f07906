import { lvAssert } from '@byted-image/lv-bedrock/assert';
import type { Message as SchemaMessage } from '@/bam/namespaces/message';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { ClientToolMessage } from '@/conversation/client-message/tool-message';
import { getContent } from '@/conversation/utils/get-content';
import { ClientStreamingMessage } from '../client-message/streaming-message';

export function convertMessage(
  messages: SchemaMessage[],
  toolMessages = new Map<string, ClientToolMessage>(),
): ClientMessage[] {
  const result: ClientMessage[] = [];

  function convertStreamToolCallMessage(dataParsed: SchemaMessage) {
    lvAssert(dataParsed.tool_call_id, 'tool_call_id is required');
    // 尝试查找是否有未完成的相同工具消息
    const existingToolMessage = toolMessages.get(dataParsed.tool_call_id);
    if (existingToolMessage) {
      // 如果找到未完成的工具消息且当前消息包含输出，则更新现有消息
      existingToolMessage.output = getContent(dataParsed.content![0]).content;
    }
  }

  function convertNewToolCallMessage(dataParsed: SchemaMessage) {
    lvAssert(dataParsed.tool_calls && dataParsed.tool_calls.length > 0, 'tool_calls is required');
    for (const toolCall of dataParsed.tool_calls) {
      const newToolMessage = new ClientToolMessage({
        createdAt: new Date().toString(),
        id: toolCall.id,
        name: toolCall.name ?? '',
        input: toolCall.input ?? '',
        version: dataParsed.version,
        // 这里 output 用于会话还原，所以需要从 content 中获取
        output: dataParsed.content?.[0] ? getContent(dataParsed.content[0]).content : undefined,
      });
      toolMessages.set(toolCall.id, newToolMessage);
      result.push(newToolMessage);
    }
  }

  function convertStreamContentMessage(dataParsed: SchemaMessage) {
    lvAssert(dataParsed.content && dataParsed.content.length > 0, 'content is required');

    const streamingMessage = new ClientStreamingMessage({
      content: '',
      createdAt: new Date().toString(),
      version: dataParsed.version,
    });

    result.push(streamingMessage);

    for (const content of dataParsed.content) {
      streamingMessage.appendChunk(getContent(content), dataParsed.version);
    }
  }

  for (const message of messages) {
    if (message.tool_call_id) {
      convertStreamToolCallMessage(message);
    } else if (message.content && message.content.length > 0) {
      convertStreamContentMessage(message);
    }

    if (message.tool_calls && message.tool_calls.length > 0) {
      convertNewToolCallMessage(message);
    }
  }
  return result;
}
