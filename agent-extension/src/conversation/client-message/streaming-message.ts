import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import { ClientMessageType, Role } from './abstract-message';
import { ClientContentMessage, type ContentMessageJson, type ContentMessageOptions } from './content-message';
import { ContentType } from '@/bam/namespaces/message';

export interface StreamingMessageOptions extends Omit<ContentMessageOptions, 'role'> {
  role: Role;
}

export interface StreamingMessageJson
  extends ContentMessageJson,
    Omit<StreamingMessageOptions, keyof ContentMessageJson> {
  role: Role;
  renderContent: string;
}

interface Chunk {
  content: string;
  type: ContentType;
}

export class ClientStreamingMessage extends ClientContentMessage {
  private _onChunk = new Emitter<[]>();
  private _chunkMap = new Map<string | number, Chunk[]>();
  protected _renderContent = '';

  static fromJSON(data: StreamingMessageJson): ClientStreamingMessage {
    return new ClientStreamingMessage(data);
  }

  constructor(options: Omit<ContentMessageOptions, 'role'>) {
    super({
      ...options,
      role: Role.Assistant,
    });
  }

  public toJSON(): StreamingMessageJson {
    return {
      ...super.toJSON(),
      role: this.role,
      renderContent: this.renderContent,
    };
  }

  public get role() {
    return Role.Assistant;
  }

  public get onChunk(): Event<[]> {
    return this._onChunk.event;
  }

  public get type(): ClientMessageType {
    return ClientMessageType.Streaming;
  }

  public get content(): string {
    if (this._content === '') {
      return '::: waiting';
    }
    return this._content;
  }

  public set content(content: string) {
    this._content = content;
  }

  public get renderContent(): string {
    return this._renderContent || this._content;
  }

  public appendChunk(chunk: Chunk, version: string | number) {
    // 处理每个 content，按 version 去重
    // if (!this._chunkMap.has(version)) {
    //   this._chunkMap.set(version, '');
    // }
    const prevChunks = this._chunkMap.get(version) ?? [];
    this._chunkMap.set(version, [...prevChunks, chunk]); // 同一个version按照调用顺序追加

    // 按 version 排序拼接内容
    const orderedVersions = Array.from(this._chunkMap.keys()).sort((a, b) => {
      // version 可能为 string 或 number，统一转 number 排序
      return Number(a) - Number(b);
    });
    let fullContent = '';
    let renderContent = '';
    for (const [idx, v] of orderedVersions.entries()) {
      const chunks = this._chunkMap.get(v) ?? [];
      fullContent += chunks.map((chunk) => chunk.content).join('');
      renderContent += chunks
        .map((chunk) =>
          chunk.type === ContentType.Reasoning
            ? `${idx === 0 ? '> ' : ''}${chunk.content.replace(/\n/g, '\n> ')}`
            : chunk.content,
        )
        .join('');
    }

    // 更新 content
    this._content = fullContent;
    this._renderContent = renderContent;
    this._onChunk.fire();
  }
}
