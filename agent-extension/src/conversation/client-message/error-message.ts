import { uuid } from '@byted-image/lv-bedrock/uuid';
import { ClientMessage, ClientMessageType, Role, type BaseJsonMessage, type MessageOptions } from './abstract-message';

export interface ErrorMessageOptions extends MessageOptions {
  code: number;
  message: string;
}

export interface ErrorMessageJson extends BaseJsonMessage, Omit<ErrorMessageOptions, keyof MessageOptions> {
  code: number;
  message: string;
}

export class ClientErrorMessage extends ClientMessage {
  protected _id: string = uuid();
  protected _errCode: number;
  protected _errMsg: string;

  static fromJSON(data: ErrorMessageJson): ClientErrorMessage {
    return new ClientErrorMessage(data);
  }

  constructor(options: ErrorMessageOptions) {
    super({
      ...options,
      version: 0,
    });

    this._errCode = options.code;
    this._errMsg = options.message;
  }

  get role() {
    return Role.Assistant;
  }

  get code() {
    return this._errCode;
  }

  get message() {
    return this._errMsg;
  }

  public toJSON(): ErrorMessageJson {
    return {
      ...super.toJSON(),
      code: this._errCode,
      message: this._errMsg,
    };
  }

  get type(): ClientMessageType {
    return ClientMessageType.Error;
  }
}
