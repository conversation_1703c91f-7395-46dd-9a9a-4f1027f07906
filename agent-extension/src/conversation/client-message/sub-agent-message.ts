import { ClientMessage, ClientMessageType } from './abstract-message';

export class SubAgentMessage extends ClientMessage {
  private _originMessage: ClientMessage;
  public readonly subAgent = true;

  static fromJSON(data: ClientMessage): SubAgentMessage {
    return new SubAgentMessage(data);
  }

  constructor(message: ClientMessage) {
    super({
      id: message.id,
      createdAt: message.createdAt,
      version: message.version,
    });
    this._originMessage = message;
  }

  public toJSON() {
    return this._originMessage.toJSON();
  }

  public get type(): ClientMessageType {
    return ClientMessageType.SubAgent;
  }
}
