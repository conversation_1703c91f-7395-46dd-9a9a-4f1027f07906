import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import { ClientMessage, ClientMessageType, Role, type BaseJsonMessage, type MessageOptions } from './abstract-message';

export interface ToolMessageOptions extends MessageOptions {
  name: string;
  input?: string;
  output?: string;
}

export interface ToolMessageJson extends BaseJsonMessage, Omit<ToolMessageOptions, keyof MessageOptions> {
  name: string;
  input?: string;
  output?: string;
  role: Role;
}

export class ClientToolMessage extends ClientMessage {
  public readonly onUpdate: Event<[]>;

  private _name: string;
  private _input?: string;
  private _output?: string;
  private _onUpdate = new Emitter<[]>();

  static fromJSON(data: ToolMessageJson): ClientToolMessage {
    return new ClientToolMessage(data);
  }

  constructor(options: ToolMessageOptions) {
    super(options);

    this.onUpdate = this._onUpdate.event;
    this._name = options.name;
    this._input = options.input;
    this._output = options.output;
  }

  public toJSON(): ToolMessageJson {
    return {
      ...super.toJSON(),
      name: this._name,
      input: this._input,
      output: this._output,
      role: this.role,
    };
  }

  public get type(): ClientMessageType {
    return ClientMessageType.Tool;
  }

  public get role(): Role {
    return Role.Assistant;
  }

  public get name(): string {
    return this._name;
  }

  public get input(): string | undefined {
    return this._input;
  }

  public set input(input: string) {
    this._input = input;
    this._onUpdate.fire();
  }

  public get output(): string | undefined {
    return this._output;
  }

  public set output(output: string) {
    this._output = output;
    this._onUpdate.fire();
  }
}
