import { uuid } from '@byted-image/lv-bedrock/uuid';
import { BaseJsonMessage, ClientMessage, ClientMessageType, type MessageOptions, type Role } from './abstract-message';

export interface Attachment {
  key: string;
  name: string;
  bucket: string;
  url: string;
}

export interface ContentMessageOptions extends MessageOptions {
  role: Role;
  content: string;
  attachments?: Attachment[];
}

export interface ContentMessageJson extends BaseJsonMessage, Omit<ContentMessageOptions, keyof MessageOptions> {
  content: string;
  attachments: Attachment[];
  role: Role;
}

export class ClientContentMessage extends ClientMessage {
  protected _id: string = uuid();
  protected _content: string;
  protected _attachments: Attachment[] = [];
  protected _role: Role;

  static fromJSON(data: ContentMessageJson): ClientContentMessage {
    return new ClientContentMessage(data);
  }

  constructor(options: ContentMessageOptions) {
    super(options);

    this._role = options.role;
    this._content = options.content;
    this._attachments = options.attachments || [];
  }

  public toJSON(): ContentMessageJson {
    return {
      ...super.toJSON(),
      content: this._content,
      attachments: this._attachments,
      role: this._role,
    };
  }

  get type(): ClientMessageType {
    return ClientMessageType.Content;
  }

  get role(): Role {
    return this._role;
  }

  get content(): string {
    return this._content;
  }

  set content(content: string) {
    this._content = content;
  }

  get attachments(): Attachment[] {
    return this._attachments;
  }
}
