import type { Int64 } from '@/bam';
import { uuid } from '@byted-image/lv-bedrock/uuid';

export enum ClientMessageType {
  Content = 0,
  Tool = 1,
  Streaming = 2,
  MultiPart = 3,
  Error = 4,
  SubAgent = 5,
}

export enum Role {
  User = 'user',
  Assistant = 'assistant',
  Tool = 'tool',
}

export interface MessageOptions {
  id?: string;
  createdAt?: string;
  version: Int64;
  roundFinish?: boolean;
}

/**
 * 序列化后的消息格式，继承自 MessageOptions
 */
export interface BaseJsonMessage extends MessageOptions {
  id: string;
  createdAt: string;
  type: ClientMessageType;
  version: Int64;
}

export abstract class ClientMessage {
  protected _id: string;
  protected _createdAt: string;
  protected _version: Int64;
  /** 判断当前消息是否是本轮对话的结束消息 */
  public roundFinish = false;
  public finishReason?: string;

  /**
   * @override 反序列化
   */
  static fromJSON(_data: BaseJsonMessage): ClientMessage {
    throw new Error('Cannot create an instance of an abstract class.');
  }

  constructor(options: MessageOptions) {
    this._id = options.id ?? uuid();
    this._createdAt = options.createdAt ?? new Date().toISOString();
    this._version = options.version;
    this.roundFinish = options.roundFinish ?? false;
  }

  public get id(): string {
    return this._id;
  }

  public get createdAt(): string {
    return this._createdAt;
  }

  public get version(): Int64 {
    return this._version;
  }

  public toJSON(): BaseJsonMessage {
    return {
      id: this._id,
      createdAt: this._createdAt,
      type: this.type,
      version: this._version,
      roundFinish: this.roundFinish,
    };
  }

  abstract get type(): ClientMessageType;
}
