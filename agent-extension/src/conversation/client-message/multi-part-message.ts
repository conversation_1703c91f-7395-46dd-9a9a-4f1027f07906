import { uuid } from '@byted-image/lv-bedrock/uuid';
import {
  ClientMessage,
  ClientMessageType,
  type BaseJsonMessage,
  type MessageOptions,
  type Role,
} from './abstract-message';
import type { UserInput } from '@/bam/namespaces/userinput';

export interface Attachment {
  key: string;
  name: string;
  bucket: string;
  url: string;
}

export interface ContentMessageOptions extends MessageOptions {
  role: Role;
  content: UserInput;
  attachments?: Attachment[];
  logID?: string;
}

export interface MultiPartMessageJson extends BaseJsonMessage, Omit<ContentMessageOptions, keyof MessageOptions> {
  content: UserInput;
  role: Role;
}

export class ClientMultiPartMessage extends ClientMessage {
  protected _id: string = uuid();
  protected _content: UserInput;
  protected _role: Role;
  protected _logID?: string;

  static fromJSON(data: MultiPartMessageJson): ClientMultiPartMessage {
    return new ClientMultiPartMessage(data);
  }

  constructor(options: ContentMessageOptions) {
    super(options);

    this._role = options.role;
    this._content = options.content;
    this._logID = options.logID;
  }

  public toJSON(): MultiPartMessageJson {
    return {
      ...super.toJSON(),
      content: this._content,
      role: this._role,
      logID: this._logID,
    };
  }

  get type(): ClientMessageType {
    return ClientMessageType.MultiPart;
  }

  get role(): Role {
    return this._role;
  }

  get content(): UserInput {
    return this._content;
  }

  set content(content: UserInput) {
    this._content = content;
  }

  get logID(): string | undefined {
    return this._logID;
  }
  set logID(id) {
    this._logID = id;
  }
}
