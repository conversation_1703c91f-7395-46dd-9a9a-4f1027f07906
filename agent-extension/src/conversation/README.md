# MessageReceiver 流式消息时序处理技术方案

## 目标

- chunk 必须按照 Message.version 从小到大排序，保证内容顺序一致性。
- Message.version 可能会跳跃，不能假设是连续自增。

---

## 技术方案设计

### 1. 维护 version 顺序

- 为每个 activeStreamingMessage 维护一个已接收的 chunk 列表（或 map），key 为 version，value 为 chunk 内容。
- 每次收到新的 Message，先判断其 version 是否已存在，若已存在则丢弃（去重）。
- 将 chunk 按 version 插入到 map 中。
- 渲染/append chunk 时，始终按 version 从小到大拼接。

### 2. version 跳跃处理

- 不假设 version 连续递增，只要 version 更大就可以插入。
- 允许 version 跳跃，保证只要 version 有序即可。

### 3. 伪代码流程

```typescript
// 伪代码
if (!activeStreamingMessage) {
    activeStreamingMessage = new ClientStreamingMessage({ ... });
    receivedChunks = new Map();
    conversationService.appendMessage(activeStreamingMessage);
}

if (!receivedChunks.has(dataParsed.version)) {
    receivedChunks.set(dataParsed.version, getContent(dataParsed.content[0]));
}

// 按 version 排序，拼接内容
const orderedVersions = Array.from(receivedChunks.keys()).sort((a, b) => a - b);
let fullContent = '';
for (const v of orderedVersions) {
    fullContent += receivedChunks.get(v);
}
activeStreamingMessage.setContent(fullContent);
```

### 4. 其他注意事项

- 需要考虑消息丢失、乱序、重复等异常情况，保证最终内容一致性。
- 如果有 finish_reason 或其他流式结束信号，可以在此时清理 receivedChunks。
- 如果发现 version 有缺失，可以通过 AssetService 的 GetStreamRange 方法补拉缺失的消息段。

---

## 结论

- 该方案能有效解决流式消息的乱序、重复、丢失（通过补拉）等时序问题，且与现有服务端接口（如 GetStreamRange）配合良好。
- 只需在实现时注意 map 的内存管理和补拉逻辑的触发时机即可。 