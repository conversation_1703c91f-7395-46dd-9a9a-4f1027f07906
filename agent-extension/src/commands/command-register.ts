import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext } from 'vscode';

export abstract class CommandRegister {
  protected readonly _disposables: Disposable[] = [];

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {}

  public abstract register(context: ExtensionContext): void;

  public dispose(): void {
    for (const disposable of this._disposables) {
      disposable.dispose();
    }
    this._disposables.length = 0;
  }
}
