import { setTimeout as setTimeoutPromise } from 'node:timers/promises';
import { IInstantiationService, InstantiationService, ServiceRegistry } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, Uri, ViewColumn, commands, window } from 'vscode';
import { WebviewProvider } from '../providers/webview-provider';
import { CommandRegister } from './command-register';
import { registerGlobalService } from '@/controller/register-global-service';
import { IWorkspaceFilesService } from '@/common/services/workspace-files/workspace-files.interface';
import type { WorkspaceFilesService } from '@/common/services/workspace-files/workspace-files.service';
import { ILocalServerService } from '@/common/services/local-server/local-server-service.interface';
import { IRpcService } from '@/common/services/rpc/rpc-service.interface';

/**
 * Tab命令注册器
 * 负责将Tab相关的命令注册到VSCode扩展系统
 */
export class TabCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    super(_instantiationService);
  }

  /**
   * 注册所有Tab相关命令
   */
  public register(context: ExtensionContext): void {
    const openInEditorDisposable = commands.registerCommand('codin-agent.openInEditor', async () => {
      console.log('Opening in editor...');
      // (this example uses webviewProvider activation event which is necessary to deserialize cached webview, but since we use retainContextWhenHidden, we don't need to use that event)
      // https://github.com/microsoft/vscode-extension-samples/blob/main/webview-sample/src/extension.ts
      const serviceRegistry = new ServiceRegistry();
      registerGlobalService(serviceRegistry, context); // TODO: tabWebview 和 普通extension的context一样吗？
      const newInstantiationService = new InstantiationService(
        serviceRegistry.makeCollection(),
        this._instantiationService as InstantiationService,
      );
      newInstantiationService.invokeFunction((accessor) => {
        const workspaceFilesService = accessor.get(IWorkspaceFilesService) as WorkspaceFilesService;
        // 目前没有启动流程，在这里启动
        workspaceFilesService.init();
      });

      const tabWebview = new WebviewProvider(context.extensionUri, newInstantiationService);
      //const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined
      const lastCol = Math.max(...window.visibleTextEditors.map((editor) => editor.viewColumn || 0));

      // Check if there are any visible text editors, otherwise open a new group to the right
      const hasVisibleEditors = window.visibleTextEditors.length > 0;
      if (!hasVisibleEditors) {
        await commands.executeCommand('workbench.action.newGroupRight');
      }
      const targetCol = hasVisibleEditors ? Math.max(lastCol + 1, 1) : ViewColumn.Two;

      const panel = window.createWebviewPanel(WebviewProvider.tabPanelId, 'Codin', targetCol, {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [context.extensionUri],
      });

      panel.iconPath = Uri.joinPath(context.extensionUri, 'assets', 'codin-icon.svg');
      tabWebview.resolveWebviewView(panel);

      // 监听panel关闭
      panel.onDidDispose(() => {
        newInstantiationService.invokeFunction((accessor) => {
          // 关闭server
          const localServerService = accessor.get(ILocalServerService);
          localServerService.dispose();

          // 关闭rpc
          const rpcService = accessor.get(IRpcService);
          rpcService.dispose();
        });
      });

      // Lock the editor group so clicking on files doesn't open them over the panel
      await setTimeoutPromise(100);
      await commands.executeCommand('workbench.action.lockEditorGroup');
    });

    this.disposables.push(openInEditorDisposable);
    context.subscriptions.push(...this.disposables);
  }
}
