import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands } from 'vscode';
import { CommandRegister } from './command-register';
import { IIndexService } from '@/common/services/index/index-service.interface';

/**
 * Code Diff命令注册器
 * 负责将Code Diff相关的命令注册到VSCode扩展系统
 */
export class IndexRecordCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IIndexService private readonly _indexService: IIndexService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有反馈相关命令
   */
  public register(context: ExtensionContext): void {
    const viewHistoryDisposable = commands.registerCommand('coding-agent.indexRecord', () =>
      this._indexService.viewHistory(),
    );

    this.disposables.push(viewHistoryDisposable);
    context.subscriptions.push(...this.disposables);
  }
}
