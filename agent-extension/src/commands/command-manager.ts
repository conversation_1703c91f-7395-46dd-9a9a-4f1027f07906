import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext } from 'vscode';
import { AccountCommandsRegister } from './account-commands.register';
import type { CommandRegister } from './command-register';
import { ConversationCommandsRegister } from './conversation-commands.register';
import { FeedbackCommandsRegister } from './feedback-commands.register';
import { SettingsCommandsRegister } from './settings-commands.register';
import { TabCommandsRegister } from './tab-commands.register';
import { CodeDiffCommandsRegister } from './code-diff-commands.register';
import { IndexRecordCommandsRegister } from './index-record-commands.register';
import { UrlCommandsRegister } from './url-commands.register';
import { LogCommandsRegister } from './log-commands.register';

/**
 * 命令管理器
 * 负责统一管理所有扩展命令的注册和清理
 */
export class CommandManager {
  private readonly disposables: Disposable[] = [];
  private readonly commandRegisters: CommandRegister[] = [];

  constructor(@IInstantiationService private readonly _instantiationService: IInstantiationService) {}

  /**
   * 注册所有命令
   */
  public registerCommands(context: ExtensionContext): void {
    const conversationCommandsRegister = this._instantiationService.createInstance(ConversationCommandsRegister);
    conversationCommandsRegister.register(context);
    this.commandRegisters.push(conversationCommandsRegister);

    const feedbackCommandsRegister = this._instantiationService.createInstance(FeedbackCommandsRegister);
    feedbackCommandsRegister.register(context);
    this.commandRegisters.push(feedbackCommandsRegister);

    const accountCommandsRegister = this._instantiationService.createInstance(AccountCommandsRegister);
    accountCommandsRegister.register(context);
    this.commandRegisters.push(accountCommandsRegister);

    const settingsCommandsRegister = this._instantiationService.createInstance(SettingsCommandsRegister);
    settingsCommandsRegister.register(context);
    this.commandRegisters.push(settingsCommandsRegister);

    const tabCommandsRegister = this._instantiationService.createInstance(TabCommandsRegister);
    tabCommandsRegister.register(context);
    this.commandRegisters.push(tabCommandsRegister);

    const codeDiffCommandsRegister = this._instantiationService.createInstance(CodeDiffCommandsRegister);
    codeDiffCommandsRegister.register(context);
    this.commandRegisters.push(codeDiffCommandsRegister);

    const indexRecordCommandsRegister = this._instantiationService.createInstance(IndexRecordCommandsRegister);
    indexRecordCommandsRegister.register(context);
    this.commandRegisters.push(indexRecordCommandsRegister);

    const urlCommandsRegister = this._instantiationService.createInstance(UrlCommandsRegister);
    urlCommandsRegister.register();
    this.commandRegisters.push(urlCommandsRegister);

    const logCommandsRegister = this._instantiationService.createInstance(LogCommandsRegister);
    logCommandsRegister.register(context);
    this.commandRegisters.push(logCommandsRegister);
  }

  /**
   * 清理所有资源
   */
  public dispose(): void {
    for (const register of this.commandRegisters) {
      register.dispose?.();
    }
    this.commandRegisters.length = 0;
    for (const disposable of this.disposables) {
      disposable.dispose?.();
    }
    this.disposables.length = 0;
  }
}
