import { IMcpHubService } from '@/common/services/mcp-hub/mcp-hub-service.interface';
import { IRulesService } from '@/common/services/rules/rules-service.interface';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, Uri, commands } from 'vscode';
import { CommandRegister } from './command-register';
import { IRpcService } from '@/common/services/rpc/rpc-service.interface';

/**
 * Settings commands register
 * Responsible for registering settings-related commands to the VSCode extension system
 */
export class SettingsCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IMcpHubService private readonly _mcpHubService: IMcpHubService,
    @IRulesService private readonly _ruleService: IRulesService,
    @IRpcService private readonly _rpcService: IRpcService,
  ) {
    super(_instantiationService);
  }

  /**
   * Register all settings-related commands
   */
  public register(context: ExtensionContext): void {
    // open
    const settingsOpenDisposable = commands.registerCommand('codin.webview.settings.open', () => {
      this._rpcService.notify('navigate', {
        path: '/settings',
      });
    });
    this.disposables.push(settingsOpenDisposable);

    // code review
    const codeReviewOpenDisposable = commands.registerCommand('codin.webview.codeReview', () => {
      this._rpcService.notify('navigate', {
        path: '/code-review',
      });
    });
    this.disposables.push(codeReviewOpenDisposable);

    // close
    const settingsCloseDisposable = commands.registerCommand('codin.webview.settings.close', () => {
      this._rpcService.notify('navigate', {
        path: '/',
      });
    });
    this.disposables.push(settingsCloseDisposable);

    const settingsOpenMcpConfigDisposable = commands.registerCommand('codin.webview.settings.openMcpSettings', () => {
      commands.executeCommand('vscode.open', Uri.file(this._mcpHubService.settingsFilePath));
    });
    this.disposables.push(settingsOpenMcpConfigDisposable);

    const settingsOpenRuleConfigDisposable = commands.registerCommand('codin.webview.settings.openRuleSettings', () => {
      commands.executeCommand('vscode.open', Uri.file(this._ruleService.rulesConfigPath));
    });
    this.disposables.push(settingsOpenRuleConfigDisposable);

    // subscribe to vscode context
    context.subscriptions.push(...this.disposables);
  }
}
