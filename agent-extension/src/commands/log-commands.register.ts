import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, Uri, commands, env, window } from 'vscode';
import { CommandRegister } from './command-register';
import { IFileLoggerService } from '@/common/services/file-logger/file-logger-service.interface';

/**
 * 日志命令注册器
 * 负责将日志相关的命令注册到VSCode扩展系统
 */
export class LogCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IFileLoggerService private readonly _fileLoggerService: IFileLoggerService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有日志相关命令
   */
  public register(context: ExtensionContext): void {
    const onOpenLogFolderDisposable = commands.registerCommand('codin.webview.log.openLogFolder', async () => {
      try {
        const logFolder = this._fileLoggerService.getLogFileFolder();
        env.openExternal(Uri.file(logFolder));
      } catch (error) {
        window.showErrorMessage(`打开日志文件夹失败: ${error}`);
      }
    });

    this.disposables.push(onOpenLogFolderDisposable);
    context.subscriptions.push(...this.disposables);
  }
}
