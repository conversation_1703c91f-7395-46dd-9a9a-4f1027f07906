import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { type Disposable, type ExtensionContext, commands } from 'vscode';
import { CommandRegister } from './command-register';
import { IDiffViewService } from '@/common/services/diff-view/diff-view-service.interface';

/**
 * Code Diff命令注册器
 * 负责将Code Diff相关的命令注册到VSCode扩展系统
 */
export class CodeDiffCommandsRegister extends CommandRegister {
  private readonly disposables: Disposable[] = [];

  constructor(
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @IDiffViewService private readonly _diffViewService: IDiffViewService,
  ) {
    super(_instantiationService);
  }

  /**
   * 注册所有反馈相关命令
   */
  public register(context: ExtensionContext): void {
    const revertAllDisposable = commands.registerCommand('codin.webview.codediff.revertAll', async () => {
      await this._diffViewService.revertChanges(true);
      await this._diffViewService.reset();
    });

    const acceptAllDisposable = commands.registerCommand('codin.webview.codediff.acceptAll', async () => {
      await this._diffViewService.closeAllDiffViews();
      await this._diffViewService.reset();
    });

    this.disposables.push(revertAllDisposable, acceptAllDisposable);
    context.subscriptions.push(...this.disposables);
  }
}
