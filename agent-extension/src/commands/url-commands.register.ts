import { CommandRegister } from './command-register';
import { commands } from 'vscode';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';

export class UrlCommandsRegister extends CommandRegister {
  constructor(@IInstantiationService protected readonly _instantiationService: IInstantiationService) {
    super(_instantiationService);
  }

  public register(): void {
    this._disposables.push(
      commands.registerCommand('codin.openExternalUrl', (url: string) => {
        commands.executeCommand('vscode.open', url);
      }),
    );
  }
}
