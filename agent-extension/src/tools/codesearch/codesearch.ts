import { DataType } from '@/bam/namespaces/agentclient';
import { ICodesearchChatService } from '@/common/services/codesearch-chat/codesearch-chat.interface';
import { ToolResult, ToolResultStatus, Tool } from '../base';
import type { ToolInfo } from '@/bam/namespaces/agentclient';
import { ClientContentMessage } from '@/conversation/client-message/content-message';
import { ConversationStatus } from '@/common/services/conversation/base-conversation-service';
import { toolDesc, toolName } from './tool-info';
import type { Int64 } from '@/bam';
import { listenOnce } from '@byted-image/lv-bedrock/event';
import { AskMessageRole } from '@/common/services/base-chat/types';
import { PromptItemType } from '@/bam/namespaces/userinput';
import { ICodingChatService } from '@/common/services/coding-chat/coding-chat-service.interface';
import { ClientMessageType, Role } from '@/conversation/client-message/abstract-message';
import type { ClientToolMessage } from '@/conversation/client-message/tool-message';
import type { ClientMultiPartMessage } from '@/conversation/client-message/multi-part-message';
import { ILoggerFactoryService, type ILoggerService } from '@/common/services/logger/logger.interface';
import type { CodeSearchConversationContext } from '@/common/services/conversation/codesearch-conversation-context/codesearch-conversation-context';
import yaml from 'yaml';
import { ReadFileTool, MAX_LINES } from '../read-file';
import { ListDirectoryTool } from '../list-directory';
import { IWorkspaceService } from '@/common/services/workspace/workspace.interface';

interface Record {
  Path: string;
  Summary: string;
  Reason: string;
  Content: string;
}

interface SearchResult {
  Doc: string;
  Records: Record[];
}

interface CodeSnippet {
  file_path: string;
  summary: string;
  content: string;
}

interface CodeSearchResponse {
  summary: string;
  codes: CodeSnippet[];
}

export class CodeSearchTool extends Tool {
  private _logger: ILoggerService;
  private _readFileTool: ReadFileTool;
  private _listDirectoryTool: ListDirectoryTool;

  constructor(
    @ICodesearchChatService private readonly _codesearchChatService: ICodesearchChatService,
    @ICodingChatService private readonly _codingChatService: ICodingChatService,
    @ILoggerFactoryService private readonly _loggerFactoryService: ILoggerFactoryService,
    @IWorkspaceService _workspaceService: IWorkspaceService,
  ) {
    super();
    this._logger = this._loggerFactoryService.create('CodeSearchTool');
    this._readFileTool = new ReadFileTool(_workspaceService);
    this._listDirectoryTool = new ListDirectoryTool(_workspaceService);
  }

  getInfo(): ToolInfo {
    return {
      name: toolName,
      desc: toolDesc,
      params: {
        explanation: {
          type: DataType.String,
          desc: 'One sentence explanation as to why this tool is being used, and how it contributes to the goal.',
          required: false,
        },
        repository_name: {
          type: DataType.String,
          desc: 'The name of the repository to search in. <system_info> will provide the list of repositories in the workspace. If the repository list is not provided in <system_info>, you are not allowed to use this tool.',
          required: true,
        },
        query: {
          type: DataType.String,
          desc: "The search query to find relevant code. You should reuse the user's exact query/most recent message with their task description unless there is a clear reason not to.",
          required: true,
        },
        path_list: {
          type: DataType.Array,
          desc: 'The path list to search. (relative to the current working directory.)',
          required: true,
        },
      },
    };
  }

  async run(inputStr: string, _toolId: string, conversationId: string, version: Int64): Promise<ToolResult> {
    try {
      this._logger.info('Codesearch tool run', inputStr, conversationId, version);
      const [error, subCid] = (
        await this._codesearchChatService.createConversation({
          parentConversationId: conversationId,
          parentMessageVersion: version,
        })
      ).pair();

      if (error) {
        return new ToolResult(ToolResultStatus.AutoRunError, `Codesearch tool run error: ${error.msg}`);
      }
      // const input = JSON.parse(inputStr);

      const context = this._codesearchChatService.getConversationContext(subCid);
      if (!context) {
        return new ToolResult(ToolResultStatus.AutoRunError, 'Codesearch tool run error: no conversation context');
      }
      this._codingChatService.addMessageListener(context);

      const mainAgentMessage = this._getMainAgentMessageHistory();

      context.sendMessage({
        role: AskMessageRole.User,
        userContent: {
          prompt: {
            items: [
              [
                {
                  type: PromptItemType.Text,
                  text: { text: mainAgentMessage },
                },
              ],
              [
                {
                  type: PromptItemType.Text,
                  text: {
                    text: `Here are some tools for reading local files. Please use them as needed.
Pay attention: use get_summary tool first, then use semantic_search tool secondly.`,
                  },
                },
              ],
            ],
          },
        },
      });

      const result = await Promise.race([
        this._waitSendMessageError(context).then((errorMessage) => {
          return new ToolResult(ToolResultStatus.AutoRunError, errorMessage);
        }),
        this._waitMessageEnd(context).then((message) => {
          return new ToolResult(ToolResultStatus.AutoRunSuccess, message);
        }),
      ]);

      if (result.status === ToolResultStatus.AutoRunError) {
        return result;
      }

      this._logger.info('result', result);
      const parsedResult = await this._parseYAMLSearchResult(result.output);
      this._logger.info('parsedResult', parsedResult);
      const formatResult = await this._buildCodeSearchResponse(parsedResult, _toolId, conversationId, version);

      return new ToolResult(result.status, formatResult);
    } catch (error) {
      return new ToolResult(ToolResultStatus.AutoRunError, `Technical planning error: ${String(error)}`);
    }
  }

  private async _waitMessageEnd(context: CodeSearchConversationContext): Promise<string> {
    return new Promise((resolve) => {
      context.messageReceiver.onStatusChange((status) => {
        if (status === ConversationStatus.Idle) {
          const messages = context.messagesManager.getMessages();
          const lastMessage = messages[messages.length - 1] as ClientContentMessage;
          this._logger.info('waitMessageEnd', lastMessage);

          if (!lastMessage) {
            resolve('Codesearch error: no message');
            return;
          }

          resolve(lastMessage.content);
        }
      });
    });
  }

  private async _waitSendMessageError(context: CodeSearchConversationContext): Promise<string> {
    return new Promise((resolve) => {
      listenOnce(context.onSendMessageError)((error) => {
        this._logger.error('Codesearch tool send message error', error);
        resolve(error.toString());
      });
    });
  }

  // 主 agent 消息共享
  private _getMainAgentMessageHistory(): string {
    const messages = this._codingChatService
      .getMessages()
      .filter((message) => message.type !== ClientMessageType.SubAgent);

    // 主 agent 消息共享
    let mainAgentMessage = '<software_engineer_message_history> \n';
    for (const message of messages) {
      if (message.type === ClientMessageType.Content) {
        const contentMessage = message as ClientContentMessage;
        mainAgentMessage += `${contentMessage.role}: ${contentMessage.content}\n`;
      } else if (message.type === ClientMessageType.Tool) {
        const toolMessage = message as ClientToolMessage;
        if (toolMessage.name === toolName) continue;
        mainAgentMessage += `${Role.Assistant} call ${toolMessage.name}: ${toolMessage.input}\n`;
        mainAgentMessage += `${Role.Tool}: <MASKED description="The output of the tool is masked">\n`;
      } else if (message.type === ClientMessageType.MultiPart) {
        const multiPartMessage = message as ClientMultiPartMessage;

        let prompt = multiPartMessage.content.prompt?.items
          .map((item) => {
            return item
              .map((subItem) => {
                if (subItem.type === PromptItemType.Text) {
                  return subItem.text?.text;
                }

                if (subItem.type === PromptItemType.File) {
                  return subItem.file?.path;
                }

                if (subItem.type === PromptItemType.Folder) {
                  return subItem.folder?.path;
                }

                return '';
              })
              .join('');
          })
          .join('\n');

        for (const globalInfo of multiPartMessage.content.global_infos ?? []) {
          if (globalInfo.file) {
            prompt += `\nfilePath: ${globalInfo.file.path}\nfileContent: ${globalInfo.file.content}`;
          }

          if (globalInfo.folder) {
            prompt += `\nfolderPath: ${globalInfo.folder.path}\nfolderContent: ${globalInfo.folder.content}`;
          }
        }

        mainAgentMessage += `${multiPartMessage.role}: ${prompt}\n`;
      }
    }

    mainAgentMessage += '</software_engineer_message_history>';

    return mainAgentMessage;
  }

  async _parseYAMLSearchResult(yamlStr: string): Promise<SearchResult> {
    const result: SearchResult = { Records: [], Doc: '' };

    try {
      const parsed = yaml.parse(yamlStr);
      if (parsed) {
        result.Records = parsed.Records || [];
        this._logger.info(`parse success: ${result.Records.length} records`);
        return result;
      }
    } catch (err) {
      this._logger.error('parse fail', err);

      // 1. Split into lines
      const lines = yamlStr.split('\n');

      // 2. Find Records: or Doc: line
      const start = lines.findIndex((line) => line.startsWith('Records:') || line.startsWith('Doc:'));

      if (start === -1) {
        this._logger.error('parse fail', new Error('no start line found'));
        throw err;
      }

      // 3. From start line, find the last line that starts with two spaces
      let end = -1;
      for (let i = lines.length - 1; i > start; i--) {
        if (lines[i].startsWith('  ')) {
          end = i;
          break;
        }
      }

      if (end === -1) {
        this._logger.error('parse fail', new Error('no end line found'));
        throw err;
      }

      // 4. Try to parse just the Records portion
      const recordsYAML = lines
        .slice(start + 1, end + 1)
        .join('\n')
        .trim();
      const records = yaml.parse(recordsYAML);

      if (records) {
        result.Records = Array.isArray(records) ? records : [records];
        return result;
      }

      throw err;
    }

    return result;
  }

  async _buildCodeSearchResponse(result: SearchResult, _toolId: string, conversationId: string, version: Int64) {
    const response: CodeSearchResponse = {
      summary: result.Records.length ? result.Doc : '没有找到相关代码',
      codes: await Promise.all(
        result.Records.map(async (record) => {
          let content = '';
          if (record.Content) {
            content = record.Content;
          } else {
            if (record.Path.endsWith('/')) {
              const listDirectoryResult = await this._listDirectoryTool.run(
                JSON.stringify({
                  explanation: 'content为空，尝试使用工具读取文件目录',
                  path: record.Path,
                }),
                _toolId,
                conversationId,
                version,
              );
              this._logger.info('content empty, try read local dir', {
                result: listDirectoryResult,
                path: record.Path,
              });
              content = listDirectoryResult.output;
            } else {
              const readFileResult = await this._readFileTool.run(
                JSON.stringify({
                  explanation: 'content为空，尝试使用工具读取文件',
                  start_line: 1,
                  end_line: MAX_LINES,
                  path: record.Path,
                }),
                _toolId,
                conversationId,
                version,
              );
              this._logger.info('content empty, try read local file', {
                result: readFileResult,
                path: record.Path,
              });
              content = readFileResult.output;
            }
          }
          return {
            file_path: record.Path,
            summary: record.Summary,
            content,
          };
        }),
      ),
    };
    this._logger.info('build codeSearch response', response);
    return JSON.stringify(response.codes);
  }
}
