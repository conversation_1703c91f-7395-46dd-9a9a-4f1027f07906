import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Theme } from '@radix-ui/themes';
import { HamburgerMenu } from './hamburger-menu';
import { RpcContext } from '../../core/rpc/rpc-context';
import { AgentWorkMode } from '../../../bam/namespaces/base';

// Mock RPC client
const mockRpcClient = {
  call: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
};

// Mock props
const defaultProps = {
  currentConversationId: 'test-conversation-id',
  messageCount: 0,
  onBusinessChange: vi.fn(),
  onModeChange: vi.fn(),
};

const renderWithRpcContext = (props = defaultProps) => {
  return render(
    <Theme>
      <RpcContext.Provider value={mockRpcClient as any}>
        <HamburgerMenu {...props} />
      </RpcContext.Provider>
    </Theme>,
  );
};

describe('HamburgerMenu', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock the RPC calls that are made during component initialization
    mockRpcClient.call.mockImplementation((method) => {
      switch (method) {
        case 'getConversationBusinessList':
          return Promise.resolve(['业务1', '业务2', '业务3']);
        case 'getConversationBusiness':
          return Promise.resolve('业务1');
        case 'getConversationMode':
          return Promise.resolve(AgentWorkMode.Act);
        case 'setConversationBusiness':
          return Promise.resolve();
        case 'setConversationMode':
          return Promise.resolve();
        case 'setLastSelectedConversationMode':
          return Promise.resolve();
        default:
          return Promise.resolve();
      }
    });
  });

  it('should render without crashing', () => {
    const { container } = renderWithRpcContext(defaultProps);
    expect(container).toBeTruthy();
  });

  it('should calculate isSelectionDisabled correctly based on messageCount', () => {
    // Test with messageCount = 0 (should not be disabled)
    const { rerender } = renderWithRpcContext({ ...defaultProps, messageCount: 0 });
    expect(screen.getByTitle('菜单')).toBeTruthy();

    // Test with messageCount > 0 (should be disabled)
    rerender(
      <Theme>
        <RpcContext.Provider value={mockRpcClient as any}>
          <HamburgerMenu {...defaultProps} messageCount={1} />
        </RpcContext.Provider>
      </Theme>,
    );
    expect(screen.getByTitle('菜单')).toBeTruthy();
  });
});
