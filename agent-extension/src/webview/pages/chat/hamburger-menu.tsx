import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Menu } from 'lucide-react';
import { DropdownMenu } from '@radix-ui/themes';
import { RpcContext } from '../../core/rpc/rpc-context';
import { DEFAULT_WORK_MODE } from '../../../common/constants/storage';
import { AgentWorkMode } from '../../../bam/namespaces/base';

interface HamburgerMenuProps {
  currentConversationId: string;
  messageCount: number;
  onBusinessChange?: (business: string) => void;
  onModeChange?: (mode: AgentWorkMode) => void;
}

function getModeDisplayName(mode: AgentWorkMode) {
  switch (mode) {
    case AgentWorkMode.Unknown:
      return 'Unknown';
    case AgentWorkMode.PlanAct:
      return 'PlanAct';
    case AgentWorkMode.Act:
      return 'Act';
  }
}

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  currentConversationId,
  messageCount,
  onBusinessChange,
  onModeChange,
}) => {
  const [businessList, setBusinessList] = useState<string[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<string>('');
  const [isLoadingBusinessList, setIsLoadingBusinessList] = useState(false);
  const [isChangingBusiness, setIsChangingBusiness] = useState(false);

  // 模式相关状态
  const [selectedMode, setSelectedMode] = useState<AgentWorkMode>(DEFAULT_WORK_MODE);
  const [isChangingMode, setIsChangingMode] = useState(false);

  const rpcClient = useContext(RpcContext);

  // 当消息数量大于0时，禁用业务和模式选择
  const isSelectionDisabled = messageCount > 0;

  const setConversationBusiness = useCallback(
    async (business: string) => {
      try {
        setIsChangingBusiness(true);
        setSelectedBusiness(business);
        if (!rpcClient || !currentConversationId) return;
        await rpcClient.call('setConversationBusiness', { cid: currentConversationId, business });
        // 通知父组件业务选择已更改
        onBusinessChange?.(business);
      } finally {
        setIsChangingBusiness(false);
      }
    },
    [rpcClient, currentConversationId, onBusinessChange],
  );

  const setConversationMode = useCallback(
    async (mode: AgentWorkMode) => {
      try {
        setIsChangingMode(true);
        setSelectedMode(mode);
        if (!rpcClient) return;

        rpcClient.call('setLastSelectedConversationMode', { mode });

        if (!currentConversationId) return;
        await rpcClient.call('setConversationMode', { cid: currentConversationId, mode });
        // 通知父组件模式选择已更改
        onModeChange?.(mode);
      } finally {
        setIsChangingMode(false);
      }
    },
    [rpcClient, currentConversationId, onModeChange],
  );

  // 获取 business 列表
  useEffect(() => {
    console.info('getConversationBusinessList', currentConversationId);
    if (!rpcClient) return;

    const fetchBusinessList = async () => {
      try {
        setIsLoadingBusinessList(true);
        const list: string[] = await rpcClient.call('getConversationBusinessList', {});
        setBusinessList(list);

        if (!currentConversationId) {
          await setConversationBusiness(list[0]);
          return;
        }

        const business: string = await rpcClient.call('getConversationBusiness', { cid: currentConversationId });
        await setConversationBusiness(business || list[0]);
      } finally {
        setIsLoadingBusinessList(false);
      }
    };

    fetchBusinessList();
  }, [rpcClient, currentConversationId, setConversationBusiness]);

  // 获取当前模式
  useEffect(() => {
    console.info('getConversationMode', currentConversationId);
    if (!rpcClient) return;

    const fetchCurrentMode = async () => {
      try {
        if (!currentConversationId) {
          await setConversationMode(DEFAULT_WORK_MODE);
          return;
        }

        const mode: AgentWorkMode = await rpcClient.call('getConversationMode', { cid: currentConversationId });
        console.info('current mode', mode);
        await setConversationMode(mode || DEFAULT_WORK_MODE);
      } catch (error) {
        console.error('Failed to fetch conversation mode:', error);
        await setConversationMode(DEFAULT_WORK_MODE);
      }
    };

    fetchCurrentMode();
  }, [rpcClient, currentConversationId, setConversationMode]);

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <div className="hamburger-menu-trigger" title="菜单">
          {/* 业务和模式标注 */}
          <div className="business-mode-label">
            <span className="business-text">{selectedBusiness || '未选择业务'}</span>
            <span className="separator">,</span>
            <span className="mode-text">{getModeDisplayName(selectedMode)}</span>
          </div>
          <div className="hamburger-menu-button">
            <Menu size={14} />
          </div>
        </div>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="hamburger-menu-content">
        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger className="hamburger-menu-text" disabled={isSelectionDisabled}>
            业务{isSelectionDisabled ? ' (已锁定)' : ''}
          </DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent className="hamburger-menu-sub-content">
            {isLoadingBusinessList ? (
              <DropdownMenu.Item className="hamburger-menu-text" disabled>
                加载中...
              </DropdownMenu.Item>
            ) : (
              businessList.map((business) => (
                <DropdownMenu.Item
                  className="hamburger-menu-text"
                  key={business}
                  onSelect={() => setConversationBusiness(business)}
                  disabled={isChangingBusiness || isSelectionDisabled}
                >
                  {selectedBusiness === business ? '✓ ' : ''}
                  {isChangingBusiness && business === selectedBusiness ? '切换中...' : business}
                </DropdownMenu.Item>
              ))
            )}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger className="hamburger-menu-text" disabled={isSelectionDisabled}>
            模式{isSelectionDisabled ? ' (已锁定)' : ''}
          </DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent className="hamburger-menu-sub-content">
            {Object.values(AgentWorkMode)
              // 需要过滤非 number，防止数字枚举的双向映射问题
              .filter((mode) => mode !== AgentWorkMode.Unknown && typeof mode === 'number')
              .map((mode) => (
                <DropdownMenu.Item
                  className="hamburger-menu-text"
                  key={mode}
                  onSelect={() => setConversationMode(mode)}
                  disabled={isChangingMode || isSelectionDisabled}
                >
                  {selectedMode === mode ? '✓ ' : ''}
                  {isChangingMode && mode === selectedMode ? '切换中...' : getModeDisplayName(mode)}
                </DropdownMenu.Item>
              ))}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
