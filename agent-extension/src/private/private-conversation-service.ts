import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { ConversationTypeEnum } from '@/common/constants/conversation-types';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import type { IPrivateConversationService } from './private-conversation-service.interface';
import { INetworkClientFactoryService } from '@/common/services/network-client-factory/network-client-factory-service.interface';
import { BaseConversationService } from '@/common/services/conversation/base-conversation-service';
import { IConversationHistoryService } from '@/common/services/conversation/history';
import { AgentId } from '@/common/services/conversation/const';
import { makeOkWith, type ILvErrorOr } from '@byted-image/lv-bedrock/error';

export class PrivateConversationService extends BaseConversationService implements IPrivateConversationService {
  public _serviceBrand: undefined;

  public readonly agentType: AgentId = AgentId.Private;
  protected readonly _conversationType: ConversationTypeEnum = ConversationTypeEnum.Coding;

  constructor(
    @IConversationHistoryService historyService: IConversationHistoryService,
    @IInstantiationService instantiationService: IInstantiationService,
    @INetworkClientFactoryService networkClientFactoryService: INetworkClientFactoryService,
  ) {
    super(historyService, instantiationService, networkClientFactoryService);
  }

  protected _createConversationUrl(): string {
    return 'http://localhost:6789/create';
  }

  protected _getConversationHeaders(): Record<string, string> {
    return {};
  }

  protected _beforeCreateConversation() {
    return Promise.resolve(makeOkWith(undefined));
  }

  protected _afterAppendMessages(_messages: ClientMessage[]) {
    // nothing
  }

  protected _afterPresentAssistantMessage() {
    // nothing
  }

  public async switchConversation(_cid: string): Promise<ILvErrorOr<void>> {
    throw new Error('not implement');
  }
}
