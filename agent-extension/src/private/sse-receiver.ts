import { ClientStreamingMessage } from '@/conversation/client-message/streaming-message';
import { ClientToolMessage } from '@/conversation/client-message/tool-message';
import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { PushType, type Message, type StreamPushData } from '@/bam/namespaces/message';
import { getContent } from '@/conversation/utils/get-content';
import type { AgentInferenceParams } from '@/bam/namespaces/agentserver';

interface TempInterface {
  appendMessages: (messages: ClientMessage[]) => void;
  presentAssistantMessage: () => void;
}

export class SSEReceiver {
  private _activeStreamingMessage?: ClientStreamingMessage;

  private _reader?: ReadableStreamDefaultReader<Uint8Array>;
  private _toolMessages = new Map<string, ClientToolMessage>();

  constructor(private readonly _conversationService: TempInterface) {
    // this._activeStreamingMessage = new ClientStreamingMessage({
    //   content: '',
    //   createdAt: new Date().toString(),
    // });
  }

  public resetActiveStreamingMessage(): void {
    this._activeStreamingMessage = undefined;
  }

  public async send(params: AgentInferenceParams) {
    try {
      const response = await fetch('http://localhost:6789/agent', {
        method: 'POST',
        headers: {
          Accept: 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      console.log('SSE connection established');

      this._reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await this._reader!.read();
          if (done) {
            console.log('SSE stream ended');
            break;
          }

          // 将字节数据解码为文本
          buffer += decoder.decode(value, { stream: true });

          // 处理缓冲区中的SSE数据
          buffer = this._processSSEBuffer(buffer);
        }
      } catch (error: unknown) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Error reading SSE stream:', error);
        }
      }
    } catch (error) {
      console.error('Failed to initialize SSE connection:', error);
    } finally {
      this._reader?.releaseLock();
      this._reader = undefined;
    }
  }

  /**
   * 销毁 MessageReceiver，清理事件监听器和内部状态
   */
  public dispose() {
    // 关闭 reader
    if (this._reader) {
      this._reader.releaseLock();
      this._reader = undefined;
    }

    // 清理内部状态
    this._activeStreamingMessage = undefined;
    this._toolMessages.clear();
  }

  /**
   * 处理SSE数据缓冲区
   */
  private _processSSEBuffer(buffer: string): string {
    const lines = buffer.split('\n');
    // 保留最后一行，因为它可能是不完整的
    const lastLine = lines.pop() || '';

    let eventType = '';
    let eventData = '';

    for (const line of lines) {
      if (line.startsWith('event:')) {
        eventType = line.slice(6).trim();
      } else if (line.startsWith('data:')) {
        eventData += line.slice(5).trim();
      } else if (line.trim() === '') {
        // 空行表示一个SSE事件结束
        if (eventData) {
          this._handleSSEMessage(eventData, eventType);
          eventData = '';
          eventType = '';
        }
      }
    }

    return lastLine;
  }

  /**
   * 处理 SSE 消息
   */
  private _handleSSEMessage(data: string, _eventType?: string) {
    try {
      if (!data || data.trim() === '') {
        return;
      }
      const parsedData = JSON.parse(data) as StreamPushData;

      if (parsedData.type === PushType.Error) {
        console.error('SSE message error', parsedData.error_code, parsedData.error_message);
      } else if (parsedData.type === PushType.NewMessage) {
        this._processStreamSingle(parsedData.message!);
        this._conversationService.presentAssistantMessage();
      }
    } catch (error) {
      console.error('Failed to parse SSE message:', error, 'raw data:', data);
    }
  }

  /**
   * 查找是否存在相同工具ID和代理ID的未完成工具消息
   * @param toolId 工具ID
   * @returns 找到的工具消息或undefined
   */
  private _findIncompleteToolMessage(toolId: string) {
    return this._toolMessages.get(toolId);
  }

  private _processStreamSingle(dataParsed: Message) {
    if (dataParsed.tool_call_id) {
      this._activeStreamingMessage = undefined;
      // 尝试查找是否有未完成的相同工具消息
      const existingToolMessage = this._findIncompleteToolMessage(dataParsed.tool_call_id);
      if (existingToolMessage) {
        // 如果找到未完成的工具消息且当前消息包含输出，则更新现有消息
        existingToolMessage.output = getContent(dataParsed.content![0]).content;
      }
    }

    if (dataParsed.content && dataParsed.content.length > 0) {
      if (!this._activeStreamingMessage) {
        this._activeStreamingMessage = new ClientStreamingMessage({
          content: '',
          createdAt: new Date().toString(),
          version: dataParsed.version,
        });
        this._conversationService.appendMessages([this._activeStreamingMessage]);
      }

      for (const content of dataParsed.content) {
        this._activeStreamingMessage.appendChunk(getContent(content), dataParsed.version);
      }
      return;
    }

    if (dataParsed.tool_calls && dataParsed.tool_calls.length > 0) {
      this._activeStreamingMessage = undefined;

      for (const toolCall of dataParsed.tool_calls) {
        const newToolMessage = new ClientToolMessage({
          createdAt: new Date().toString(),
          id: toolCall.id,
          name: toolCall.name ?? '',
          input: toolCall.input ?? '',
          version: dataParsed.version,
        });
        this._toolMessages.set(toolCall.id, newToolMessage);
        this._conversationService.appendMessages([newToolMessage]);
      }
    }
  }
}
