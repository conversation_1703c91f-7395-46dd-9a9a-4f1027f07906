```mermaid
graph TD
    A[VS Code 启动 / 激活事件触发] --> B(1.激活 Extension);
    B --> C{extension.ts - activate};
    C --> D[2.初始化 ExtensionInitializer];
    D --> E[2.1 初始化依赖注入];
    E --> F[注册所有 Service];
    F --> G[创建 InstantiationService];
    D --> H[2.2 初始化 Webview];
    H --> I[创建 WebviewProvider];  
    I --> J[注册 UI 视图];
    D --> K[2.3 初始化命令];
    K --> L[创建 CommandManager];
    L --> M[注册所有 Commands];
    D --> N[2.4 初始化其他服务];
    N --> O[初始化 WorkspaceService, MerkleService 等];

    subgraph 3.运行与交互
        P[用户在 Webview UI 中操作] --> Q{RPC 调用};
        Q -- command, params --> R[RpcService - Extension 端];
        R --> S[执行对应 Service 逻辑];
        S --> T[Service 处理业务];
        T -- 结果 --> U{RPC 通知};
        U -- event, data --> V[Webview UI 接收并更新];
    end

    W[VS Code 关闭 / 插件停用] --> X(4.停用 Extension);
    X --> Y[执行 context.subscriptions 中的 dispose];
    Y --> Z[清理所有 Service, Command, Listener];

    J --> P;
    M --> P;
```