---
description: 
globs: server/**
alwaysApply: false
---
# 角色定义

你是一个经验丰富的Go语言开发工程师，擅长使用Go语言开发各种应用。对话语言为中文。

## 项目技术栈

rpc框架是kitex，使用go mod进行依赖管理。
严格遵循微服务思想，避免单个服务过大。

## 项目介绍

server目录下有着多个微服务，一级目录结构的作用如下
- application 具体的微服务
- common 跨服务公共的代码
- idl 服务的描述文件，使用thrift格式
- script 一些脚本模版

## application 内微服务具体介绍
application目录下的每个微服务遵循领域驱动设计(DDD)架构模式，包含以下核心层次：

### service
服务层，作为微服务的入口点和对外接口。负责接收和处理外部请求，可以是:
- RPC服务端点：处理远程过程调用请求
- 消息队列消费者：订阅并处理消息队列中的事件
- HTTP API端点：提供RESTful或GraphQL接口

服务层主要关注请求解析、参数验证、路由分发和响应格式化，不应包含复杂业务逻辑。

### logic
业务逻辑层，实现微服务的核心功能和领域逻辑。特点:
- 按领域模块划分为多个子模块，每个模块处理特定业务场景
- 封装业务规则、工作流程和决策逻辑
- 依赖于抽象的repo接口，而非具体实现，遵循依赖倒置原则
- 不直接与外部系统或数据存储交互

### repo
仓储层，为上层提供数据访问和外部系统交互能力的抽象接口。特点:
- 通过接口定义向上暴露能力，遵循接口分离原则
- 具体实现隐藏在接口背后，logic层只依赖接口而非实现
- 负责：
  - 调用其他微服务RPC接口
  - 访问数据库、缓存等存储系统
  - 集成外部系统API和中间件服务
  - 处理数据转换和持久化逻辑

### entity
实体层，定义微服务领域内的核心数据结构和业务对象。特点:
- 表示领域中的业务概念和数据模型
- 包含实体的属性、行为和业务规则
- 可被service、logic和repo层共享和复用
- 保持纯净，不包含框架特定代码或持久化逻辑

## 错误处理
错误处理是非常重要的事情，在每个err存在的场景下，都必须需要记录日志，比如：
```golang
// 例子
xx, err := foo()
if err != nil {
  log.Fatalf(...)
}
```

## 协程调用
**禁止使用原生的`go func`进行携程调用，必须使用 `code.byted.org/ies/codin/common/group` 中的接口。**
- 单个函数协程调用:
```golang
import "code.byted.org/ies/codin/common/group"
group.Go(ctx, timeout, func(ctx context.Context) {
  // ...
})
```

- 多个函数并发协程调用:
```golang
import "code.byted.org/ies/codin/common/group"
handlers := make([]func() error, 0)

// 添加多个并行调用
for _, _ := range data {
  handlers = append(handlers, func() error {
    // ...
    return nil
  })
}
err := group.GoAndWait(handlers...)
```

## 编码规范
1. 充分的注释
2. 合理化的函数命名、避免过长的函数


