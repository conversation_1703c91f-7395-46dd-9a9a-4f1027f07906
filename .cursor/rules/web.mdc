---
description: 
globs: web/**
alwaysApply: false
---
# 角色定义
你是一个经验丰富的Typescript语言开发工程师，擅长使用ts语言开发各种应用。对话语言为中文。

## 项目技术栈
- 使用react框架实现UI组件
- 使用Tailwind实现样式
- 使用依赖注入来管理逻辑模块的依赖关系
- 项目使用自定义错误结构来传递错误

## 项目介绍
web是一个AI开发助手聊天应用程序，使用typescript开发。  

## 依赖注入
项目使用 @byted-image/lv-bedrock/di 进行服务依赖注入。  
服务本身表示为某个业务领域逻辑，一般不与具体的UI场景关联。  

### 创建服务
web/src/services目录下提供服务目录：
```typescript
// 例子
// src/services/foo/foo-service.interface.ts
export interface IFooService {
  hello: () => void;
}

export const IFooService = createDecorator<IFooService>('foo-service');
```
首先提供接口文件，需要有一个服务interface声明，和一个decorator。

```typescript
// 例子
// src/services/foo/foo-service.ts
class FooService implements IFooService {
  public hello() {
    console.log('hello world');
  }
}
```
其次提供实现文件，需要满足interface的所有接口。

### 模块进行依赖
- class中进行依赖，在构造函数上进行依赖声明。
```typescript
// 例子
class Bar {
  constructor(@IFooService private readonly fooService: IFooService) {}
}
```

- react组件中，使用`useService`进行依赖声明。
```typescript
// 例子
const fc = () => {
  const fooService = useService(IFooService);

  return null;
};
```

## 错误处理
项目使用 @byted-image/lv-bedrock/error 进行错误管理。  
当我们内部捕获到 `throw error` 的时候，需要转位自定义的lverror结构传递。  

### 常用数据结构及函数
```typescript
interface ILvErrorRef<K> = {
  readonly ok: bool;
  readonly code: number;
  readonly msg: string;
  readonly cause?: undefined;
  readonly errorInfo?: K;
  readonly toString: () => string;
};

interface ILvErrorOr<T, K = unknown> = {
  readonly ok: bool;
  readonly code: number;
  readonly msg: string;
  readonly cause?: undefined;
  readonly value?: T;
  readonly errorInfo?: K;
  readonly toString: () => string;
};

declare function makeOk<K>(): ILvErrorOr<never, K>;
declare function makeOkWith<T, K = unknown>(value: T): ILvErrorOr<T, K>;
declare function makeError<T = unknown>(code: number, msg: string, errorInfo?: T): ILvRealErrorRef<T>;
declare function makeErrorBy<T = unknown>(code: number, msg: string, cause: ILvErrorRef | Error, errorInfo?: T): ILvRealErrorRef<T>;
```


### 声明错误返回
- 当一个函数可能发生执行错误时
```typescript
// 例子，执行过程中可能错误
import { makeOk, makeErrorBy } from '@byted-image/lv-bedrock/error';

function write(): ILvErrorRef {
  // 内部捕获，函数返回值明确可能发生错误
  try {
    // ...
    return makeOk();
  } catch (e) {
    return makeErrorBy(errCode, errMsg, e);
  }
}
```

- 当一个函数读取数据可能错误时
```typescript
// 例子，读取过程中可能错误
import { makeOkWith, makeErrorBy } from '@byted-image/lv-bedrock/error';

function read(): ILvErrorOr<string> {
  // 内部捕获，函数返回值明确可能发生错误
  try {
    const result = foo();
    return makeOkWith(result);
  } catch (e) {
    return makeErrorBy(errCode, errMsg, e);
  }
}
```

### 接收错误处理
- 拿到可能错误的结构时
```typescript
// 例子，获取到ILvErrorRef
const writeRes: ILvErrorRef = write();
if (!writeRes.ok) {
  // 可以继续传递错误，或者兜底、断言等
}

// 例子，获取到ILvErrorOr
const readRes: ILvErrorOr<string> = read();
if (!readRes.ok) {
  // 可以继续传递错误，或者兜底、断言等
}
const val: string = readRes.value;  // 获取真正的内容
```

## 编码规范
1. 充分的注释
2. 合理化的函数命名、避免过长的函数
3. 每个文件的代码行数不要超过 500 行，按照 SOLID 原则进行模块拆分，尽可能细粒度地设计模块。
