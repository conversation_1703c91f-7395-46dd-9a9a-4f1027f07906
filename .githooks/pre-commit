#!/bin/bash

# 获取待提交的文件列表并过滤出符合条件的 service 文件
changed_services=$(git diff --cached --name-only | grep "server/application/.*/service/.*_service\.go$" || true)

# 如果没有相关文件变更，直接退出
if [ -z "$changed_services" ]; then
    exit 0
fi

# 创建临时文件
temp_file=$(mktemp)
trap 'rm -f "$temp_file"' EXIT

# 遍历每个变更的服务文件
echo "$changed_services" | while IFS= read -r file; do
    # 提取服务目录路径 (server/application/xxx)
    service_dir=$(echo "$file" | sed -E 's|^(server/application/[^/]+).*|\1|')
    
    # 检查这个目录是否已经处理过
    if grep -q "^$service_dir$" "$temp_file"; then
        continue
    fi
    
    echo "检测到 service 文件变更: $file"
    echo "在 $service_dir 目录下执行 go generate..."
    
    # 记录这个目录
    echo "$service_dir" >> "$temp_file"
    
    # 记录执行 generate 前的文件状态
    pre_gen_files=$(find "$service_dir" -name "*_gen.go" -type f 2>/dev/null | sort)
    
    # 切换到服务目录并执行 go generate
    if ! (cd "$service_dir" && go generate ./...); then
        echo "错误: 在 $service_dir 目录下执行 go generate 失败"
        echo "提交已被阻止。请修复问题后重新提交。"
        exit 1
    fi
    
    # 获取执行 generate 后的文件状态
    post_gen_files=$(find "$service_dir" -name "*_gen.go" -type f 2>/dev/null | sort)
    
    # 找出新生成或被修改的文件
    while IFS= read -r gen_file; do
        if ! echo "$pre_gen_files" | grep -q "^$gen_file$" || \
           ! git ls-files --error-unmatch "$gen_file" >/dev/null 2>&1 || \
           [ -n "$(git diff "$gen_file")" ]; then
            echo "添加生成的文件到 commit: $gen_file"
            git add "$gen_file"
        fi
    done <<< "$post_gen_files"
done

# 检查最后一个命令的退出状态
exit_status=${PIPESTATUS[1]}
exit $exit_status 