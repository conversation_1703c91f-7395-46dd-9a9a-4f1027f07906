import { useService } from '@byted-image/lv-bedrock/di';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ICalcTokenService } from '@/common/services/calc-token/calc-token-service.interface';
import { TokenCount } from '@/bam/namespaces/asset';
import { IModelInfoService } from '@/common/services/model-info/model-info-service.interface';
import { addQueryParams, getQueryParams } from '@/common/utils/query-params';

function calcTotal(tokenCounts: TokenCount[]): TokenCount {
  const total: TokenCount = {
    input_count: 0,
    output_count: 0,
    model: '',
  };
  for (const count of tokenCounts) {
    total.input_count += count.input_count;
    total.output_count += count.output_count;
    if (total.model === '') {
      total.model = count.model;
    } else if (total.model !== count.model) {
      total.model = 'mix';
    }
  }
  return total;
}

function isValid19DigitNumber(input: string): boolean {
  const regex = /^[0-9]{19}$/;
  return regex.test(input);
}

// Token计算组件
export const TokenCalculator: React.FC = () => {
  const [conversationId, setConversationId] = useState<string>('');
  const [tokenCounts, setTokenCounts] = useState<TokenCount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const calcTokenService = useService(ICalcTokenService);
  const modelInfoService = useService(IModelInfoService);

  const handleCalculate = useCallback(
    async (cid: string) => {
      if (!isValid19DigitNumber(cid)) {
        setError('请输入19位数字会话ID');
        return;
      }

      addQueryParams('cid', cid);
      setLoading(true);
      setError('');

      try {
        const result = await calcTokenService.calcTokens([cid]);
        if (!result.ok) {
          setError(result.msg);
          return;
        }
        setTokenCounts(result.value[0]);
      } catch (e) {
        setError('计算失败：' + (e instanceof Error ? e.message : String(e)));
      } finally {
        setLoading(false);
      }
    },
    [calcTokenService, modelInfoService],
  );

  const total = useMemo(() => calcTotal(tokenCounts), [tokenCounts]);

  useEffect(() => {
    const cid = getQueryParams('cid');
    if (cid) {
      setConversationId(cid);
      handleCalculate(cid);
    }
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Token 计算</h1>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">会话ID</label>
          <div className="flex gap-2">
            <input
              type="text"
              value={conversationId}
              onChange={(e) => setConversationId(e.target.value)}
              placeholder="请输入会话ID"
              className="flex-1 rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={() => handleCalculate(conversationId)}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? '计算中...' : '计算'}
            </button>
          </div>
        </div>

        {error && <div className="text-red-500 text-sm">{error}</div>}

        {tokenCounts.length > 0 && (
          <div className="mt-6">
            <h2 className="text-xl font-semibold mb-4">计算结果</h2>
            <div className="space-y-4">
              <div className="border rounded-md p-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>总共:</span>
                    <span className="font-mono">
                      {JSON.stringify({
                        input_count: total.input_count,
                        output_count: total.output_count,
                        model: modelInfoService.getModelName(total.model),
                        cost: `¥${modelInfoService.calcCost(total).toFixed(2)}`,
                      })}
                    </span>
                  </div>
                </div>
              </div>
              {tokenCounts.map((count, index) => (
                <div key={index} className="border rounded-md p-4">
                  <div className="space-y-2">
                    <div key={index} className="flex justify-between">
                      <span>轮次 {index + 1}:</span>
                      <span className="font-mono">
                        {JSON.stringify({
                          input_count: count.input_count,
                          output_count: count.output_count,
                          model: modelInfoService.getModelName(count.model),
                          cost: `¥${modelInfoService.calcCost(count).toFixed(2)}`,
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
