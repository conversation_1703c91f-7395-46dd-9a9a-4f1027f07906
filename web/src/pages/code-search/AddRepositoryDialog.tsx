/**
 * 添加仓库对话框组件
 * 提供表单用于填写新仓库的信息
 */
import React, { useState } from 'react';
import { CreateRepositoryRequest } from './shared/types';

interface AddRepositoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateRepositoryRequest) => Promise<void>;
}

const AddRepositoryDialog: React.FC<AddRepositoryDialogProps> = ({ isOpen, onClose, onSubmit }) => {
  console.log('🔥 AddRepositoryDialog 组件被调用了！isOpen:', isOpen);

  const [formData, setFormData] = useState<CreateRepositoryRequest>({
    name: 'pippit_lynx',
    description: '小云雀',
    url: 'https://code.byted.org/ies/lv-lynx.git',
    language: 'TypeScript',
    branch: 'master',
    projectID: '774208',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof CreateRepositoryRequest, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    if (!formData.name.trim()) {
      alert('请输入仓库名称');
      return;
    }
    if (!formData.url.trim()) {
      alert('请输入仓库地址');
      return;
    }
    if (!formData.language.trim()) {
      alert('请选择编程语言');
      return;
    }
    if (!formData.projectID.trim()) {
      alert('请选择仓库 ProjectID');
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('📋 提交表单数据:', formData);
      await onSubmit(formData);

      setFormData({
        name: '',
        description: '',
        url: '',
        language: '',
        branch: '',
        projectID: '',
      });

      onClose();
    } catch (error) {
      console.error('❌ 提交失败:', error);
      alert('添加仓库失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (isSubmitting) return;

    setFormData({
      name: '',
      description: '',
      url: '',
      language: '',
      branch: '',
      projectID: '',
    });

    onClose();
  };

  if (!isOpen) {
    console.log('❌ 对话框未打开 (isOpen = false)');
    return null;
  }

  console.log('✅ 对话框正在渲染！');

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-screen overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">📋 添加新仓库</h3>
          <button
            type="button"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* 仓库地址 */}
            <div>
              <label htmlFor="repository-url" className="block text-sm font-medium text-gray-700 mb-1">
                仓库地址 <span className="text-red-500">*</span>
              </label>
              <input
                type="url"
                value={formData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
                placeholder="https://code.byted.org/ies/codin.git"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
                required
              />
            </div>

            {/* 仓库名称 */}
            <div>
              <label htmlFor="repository-name" className="block text-sm font-medium text-gray-700 mb-1">
                仓库名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="输入仓库名称"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
                required
              />
            </div>

            {/* 仓库 ProjectID */}
            <div>
              <label htmlFor="repository-projectID" className="block text-sm font-medium text-gray-700 mb-1">
                ProjectID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.projectID}
                onChange={(e) => handleInputChange('projectID', e.target.value)}
                placeholder="输入仓库 ProjectID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
                required
              />
            </div>

            {/* 索引分支 */}
            <div>
              <label htmlFor="repository-branch" className="block text-sm font-medium text-gray-700 mb-1">
                索引分支 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.branch}
                onChange={(e) => handleInputChange('branch', e.target.value)}
                placeholder="master"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
                required
              />
            </div>

            {/* 编程语言 */}
            <div>
              <label htmlFor="repository-language" className="block text-sm font-medium text-gray-700 mb-1">
                语言 <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.language}
                onChange={(e) => handleInputChange('language', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
                required
              >
                <option value="">请选择语言</option>
                <option value="TypeScript">TypeScript</option>
                <option value="Go">Go</option>
                <option value="Swift">Swift</option>
                <option value="Kotlin">Kotlin</option>
              </select>
            </div>

            {/* 仓库描述 */}
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                仓库描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="描述这个仓库的用途和功能..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
              />
            </div> */}
          </div>

          {/* 按钮区域 */}
          <div className="flex space-x-3 mt-6 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isSubmitting ? '添加中...' : '确定'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddRepositoryDialog;
