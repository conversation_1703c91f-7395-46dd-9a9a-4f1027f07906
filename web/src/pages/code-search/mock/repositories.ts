const mockedRepositories = {
  success: true,
  data: {
    repositories: [
      {
        id: '686de6be74088900529be3a7',
        name: 'user-dashboard',
        description: '用户管理后台系统，包含用户管理、权限控制等功能',
        status: 'indexed',
        indexedAt: '2024-01-15T14:30:22.000Z',
        language: 'TypeScript',
        size: '2.3 MB',
        lastActivity: '2024-01-20T09:15:33.000Z',
        url: 'https://github.com/company/user-dashboard',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3a8',
        name: 'api-gateway',
        description: 'API 网关服务，负责请求转发和鉴权',
        status: 'indexed',
        indexedAt: '2024-01-18T11:22:15.000Z',
        language: 'Go',
        size: '1.8 MB',
        lastActivity: '2024-01-21T16:45:12.000Z',
        url: 'https://github.com/company/api-gateway',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3a9',
        name: 'mobile-ios-app',
        description: 'iOS端原生应用，提供完整的移动端体验',
        status: 'pending',
        indexedAt: null,
        language: 'Swift',
        size: '4.2 MB',
        lastActivity: '2024-01-19T13:28:45.000Z',
        url: 'https://github.com/company/mobile-ios-app',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3aa',
        name: 'android-client',
        description: 'Android端应用客户端，支持多种业务场景',
        status: 'failed',
        indexedAt: null,
        language: 'Kotlin',
        size: '3.8 MB',
        lastActivity: '2024-01-17T08:33:21.000Z',
        url: 'https://github.com/company/android-client',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3ab',
        name: 'web-components',
        description: '公共组件库，包含各种可复用的 UI 组件',
        status: 'indexed',
        indexedAt: '2024-01-16T15:45:30.000Z',
        language: 'TypeScript',
        size: '1.2 MB',
        lastActivity: '2024-01-22T10:20:15.000Z',
        url: 'https://github.com/company/web-components',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3ac',
        name: 'microservice-auth',
        description: '认证微服务，处理用户登录和权限验证',
        status: 'not_indexed',
        indexedAt: null,
        language: 'Go',
        size: '2.1 MB',
        lastActivity: '2024-01-14T17:12:08.000Z',
        url: 'https://github.com/company/microservice-auth',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3ad',
        name: 'data-analytics',
        description: '数据分析平台，提供实时数据洞察和报表',
        status: 'indexed',
        indexedAt: '2024-01-13T09:20:15.000Z',
        language: 'TypeScript',
        size: '5.6 MB',
        lastActivity: '2024-01-23T14:35:22.000Z',
        url: 'https://github.com/company/data-analytics',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3ae',
        name: 'payment-service',
        description: '支付服务，集成多种支付方式和风控系统',
        status: 'pending',
        indexedAt: null,
        language: 'Go',
        size: '3.4 MB',
        lastActivity: '2024-01-21T11:45:30.000Z',
        url: 'https://github.com/company/payment-service',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3af',
        name: 'notification-center',
        description: '通知中心，统一管理各种消息推送和通知',
        status: 'indexed',
        indexedAt: '2024-01-12T16:20:45.000Z',
        language: 'Kotlin',
        size: '2.8 MB',
        lastActivity: '2024-01-20T09:15:12.000Z',
        url: 'https://github.com/company/notification-center',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
      {
        id: '686de6be74088900529be3b0',
        name: 'admin-panel',
        description: '管理员控制面板，提供系统配置和监控功能',
        status: 'failed',
        indexedAt: null,
        language: 'TypeScript',
        size: '4.1 MB',
        lastActivity: '2024-01-18T13:22:18.000Z',
        url: 'https://github.com/company/admin-panel',
        createdAt: '2025-07-09T03:49:18.010Z',
        updatedAt: '2025-07-09T03:49:18.010Z',
        indexedBranch: 'master',
      },
    ],
    total: 20,
    page: 1,
    pageSize: 10,
    totalPages: 2,
  },
};

export { mockedRepositories };
