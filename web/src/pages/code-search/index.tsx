/**
 * 仓库管理后台主组件
 * 负责整体布局和状态管理，协调各个子组件的交互
 */
import React, { useEffect } from 'react';
import { useRepositoryData } from './repositoryManagerState';
import { useRepositoryRequest } from './repositoryManagerRequest';
import { CreateRepositoryRequest } from './shared/types';
import Header from './Header';
import FilterBar from './FilterBar';
import RepositoryList from './RepositoryList';
import AddRepositoryDialog from './AddRepositoryDialog';

const RepositoryManager: React.FC = () => {
  const repositoryData = useRepositoryData();
  const { repositories: stateRepositories, isAddDialogOpen, openAddDialog, closeAddDialog } = repositoryData;
  const {
    repositories: requestRepositories,
    isLoading,
    error,
    total,
    totalPages,
    createRepository,
  } = useRepositoryRequest();

  // 使用 request 中的数据，因为它是最新的
  const repositories = requestRepositories;

  // 监听repositories变化
  useEffect(() => {
    console.log('=== 📊 仓库数据监听 ===');
    console.log('🏪 状态中的仓库数量:', stateRepositories.length);
    console.log('📡 请求中的仓库数量:', requestRepositories.length);
    console.log('📋 最终使用的仓库数量:', repositories.length);
    console.log('⏳ 加载状态:', isLoading);
    console.log('❌ 错误状态:', error?.message || '无');
    console.log('📊 总数:', total);
    console.log('📄 总页数:', totalPages);

    if (repositories.length > 0) {
      console.log(
        '✅ 仓库数据样例:',
        repositories.slice(0, 2).map((repo) => ({
          id: repo.id,
          name: repo.name,
          status: repo.status,
        })),
      );
    }
  }, [stateRepositories, requestRepositories, repositories, isLoading, error, total, totalPages]);

  console.log('🎯 RepositoryManager 组件渲染状态:', {
    repositoriesCount: repositories.length,
    isLoading,
    hasError: !!error,
    errorMessage: error?.message,
    total,
    totalPages,
  });

  const handleCreateRepository = async (data: CreateRepositoryRequest) => {
    console.log('🚀 主组件: 处理创建仓库请求', data);
    try {
      await createRepository(data);
      console.log('✅ 主组件: 仓库创建成功');
    } catch (error) {
      console.error('❌ 主组件: 仓库创建失败', error);
      throw error;
    }
  };

  const handleOpenAddDialog = () => {
    console.log('🎯 主组件: 收到打开对话框请求');
    openAddDialog();
    console.log('🎯 主组件: 对话框状态应该已更新为:', isAddDialogOpen);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header onAddRepository={handleOpenAddDialog} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <FilterBar />
        </div>

        <div>
          {error ? (
            <div className="bg-white rounded-lg shadow-sm border border-red-200 p-12 text-center">
              <div className="text-red-600 text-lg">❌ 数据加载失败</div>
              <p className="text-red-500 mt-2">{error.message}</p>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  刷新页面
                </button>
              </div>
            </div>
          ) : isLoading && repositories.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-gray-600 text-lg">⏳ 正在加载仓库数据...</div>
              <div className="mt-2 text-sm text-gray-500">首次加载会自动初始化20个示例仓库</div>
              <div className="mt-4">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ) : repositories.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-gray-400 text-lg">📭 暂无仓库数据</div>
            </div>
          ) : (
            <RepositoryList
              // @ts-ignore
              repositories={repositories}
              total={total}
              totalPages={totalPages}
            />
          )}
        </div>
      </div>
      {/* 添加仓库对话框 */}
      <AddRepositoryDialog isOpen={isAddDialogOpen} onClose={closeAddDialog} onSubmit={handleCreateRepository} />
    </div>
  );
};

export default RepositoryManager;
