/**
 * 仓库列表组件
 * 展示仓库信息，包含状态、操作和分页功能
 */
import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { Repository } from './shared/types';
import { useRepositoryData } from './repositoryManagerState';
import { useRepositoryRequest } from './repositoryManagerRequest';
import RepositoryCard from './RepositoryCard';

interface RepositoryListProps {
  repositories: Repository[];
  total?: number;
  totalPages?: number;
}

const RepositoryList: React.FC<RepositoryListProps> = ({
  repositories,
  total: propTotal,
  totalPages: propTotalPages,
}) => {
  const { currentPage, setCurrentPage, pageSize, activeSearchTerm, languageFilter } = useRepositoryData();

  const { triggerSearch } = useRepositoryRequest();

  // 优先使用 props 传递的数据，确保数据同步
  const total = propTotal || 0;
  const totalPages = propTotalPages || Math.ceil(total / pageSize);

  console.log('📋 RepositoryList 渲染:', {
    receivedRepositories: repositories.length,
    propTotal,
    propTotalPages,
    finalTotal: total,
    finalTotalPages: totalPages,
    currentPage,
    pageSize,
  });

  // 强制确保 repositories 是数组
  const safeRepositories = Array.isArray(repositories) ? repositories : [];

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= Math.max(totalPages, 1)) {
      console.log('📄 === 翻页按钮点击 ===');
      console.log('🎯 目标页码:', page);
      console.log('📍 当前页码:', currentPage);
      console.log('⏰ 点击时间:', new Date().toLocaleTimeString());

      // 立即更新页码状态
      setCurrentPage(page);

      // 延迟触发搜索，确保状态更新完成后再发送请求
      // 使用较短的延迟，避免用户感知到延迟
      setTimeout(() => {
        console.log('🚀 延迟触发分页请求，页码:', page);
        console.log('📄 使用当前搜索条件:', { activeSearchTerm, languageFilter });
        triggerSearch(activeSearchTerm, languageFilter, page);
      }, 100);
    }
  };

  // const startIndex = (currentPage - 1) * pageSize + 1;
  // const endIndex = Math.min(currentPage * pageSize, total);

  if (safeRepositories.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <div className="text-gray-400 text-lg">📭 暂无仓库数据显示</div>
        <p className="text-gray-500 mt-2">
          接收到的仓库数据: {repositories.length} 个<br />
          总计数据: {total} 个
        </p>
        <div className="mt-4 text-xs text-gray-400">
          调试信息:{' '}
          {JSON.stringify(
            {
              repositories: repositories.length,
              total,
              totalPages,
              currentPage,
            },
            null,
            2,
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 列表头部 */}
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            📋 仓库列表
            <span className="text-sm ml-2 text-gray-500">(总计 {total} 个仓库)</span>
          </h3>
        </div>
      </div>

      {/* 仓库列表 */}
      <div className="divide-y divide-gray-200">
        {safeRepositories.map((repository, index) => {
          console.log(`📝 渲染仓库 ${index + 1}:`, {
            id: repository.id || repository._id,
            name: repository.name,
            status: repository.status,
          });

          return <RepositoryCard key={repository.id || repository._id || `repo-${index}`} repository={repository} />;
        })}
      </div>

      {/* 分页组件 - 确保翻页按钮始终显示 */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            第 {currentPage} 页，共 {Math.max(totalPages, 1)} 页
          </div>

          <div className="flex items-center space-x-3">
            {/* 上一页按钮 */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-lg transition-all duration-200 ${
                currentPage <= 1
                  ? 'border-gray-200 text-gray-400 bg-gray-100 cursor-not-allowed'
                  : 'border-blue-300 text-blue-700 bg-white hover:bg-blue-50 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm hover:shadow-md'
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4 mr-1" />
              上一页
            </button>

            {/* 页码显示和快速跳转 */}
            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                {(() => {
                  const pages = [];
                  const showPages = 5; // 显示的页码数量
                  let startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
                  const endPage = Math.min(totalPages, startPage + showPages - 1);

                  // 调整起始页，确保显示足够的页码
                  if (endPage - startPage + 1 < showPages) {
                    startPage = Math.max(1, endPage - showPages + 1);
                  }

                  // 第一页
                  if (startPage > 1) {
                    pages.push(
                      <button
                        type="button"
                        key={1}
                        onClick={() => handlePageChange(1)}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 shadow-sm hover:shadow-md"
                      >
                        1
                      </button>,
                    );
                    if (startPage > 2) {
                      pages.push(
                        <span key="start-ellipsis" className="px-2 text-gray-500">
                          ...
                        </span>,
                      );
                    }
                  }

                  // 中间页码
                  for (let i = startPage; i <= endPage; i++) {
                    pages.push(
                      <button
                        type="button"
                        key={i}
                        onClick={() => handlePageChange(i)}
                        className={`inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md transition-all duration-200 shadow-sm hover:shadow-md ${
                          i === currentPage
                            ? 'border-blue-500 text-blue-600 bg-blue-50 cursor-default ring-2 ring-blue-200'
                            : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500'
                        }`}
                      >
                        {i}
                      </button>,
                    );
                  }

                  // 最后一页
                  if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                      pages.push(
                        <span key="end-ellipsis" className="px-2 text-gray-500">
                          ...
                        </span>,
                      );
                    }
                    pages.push(
                      <button
                        type="button"
                        key={totalPages}
                        onClick={() => handlePageChange(totalPages)}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 shadow-sm hover:shadow-md"
                      >
                        {totalPages}
                      </button>,
                    );
                  }

                  return pages;
                })()}
              </div>
            )}

            {/* 下一页按钮 */}
            <button
              type="button"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= Math.max(totalPages, 1)}
              className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-lg transition-all duration-200 ${
                currentPage >= Math.max(totalPages, 1)
                  ? 'border-gray-200 text-gray-400 bg-gray-100 cursor-not-allowed'
                  : 'border-blue-300 text-blue-700 bg-white hover:bg-blue-50 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm hover:shadow-md'
              }`}
            >
              下一页
              <ChevronRightIcon className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RepositoryList;
