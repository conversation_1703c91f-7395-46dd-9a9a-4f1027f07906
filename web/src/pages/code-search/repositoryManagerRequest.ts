/**
 * 仓库管理网络请求相关逻辑
 * 处理与服务端的交互，包括数据获取和状态更新
 */
import useSWR from 'swr';
import {
  Repository,
  ApiResponse,
  type QueryBuildRepoResponse,
  type CreateRepositoryRequest,
  type CreateRepositoryResponse,
} from './shared/types';
import { useRepositoryData } from './repositoryManagerState';
import { useEffect, useRef } from 'react';
// import { mockedRepositories } from './mock/repositories';

const API_BASE = 'https://capcut-devops.byted.org';

// 数据获取器
const fetcher = async (url: string) => {
  // return mockedRepositories.data;

  console.log('🚀 === FETCHER 执行 ===');
  console.log('📡 这说明网络请求确实发送了！');
  console.log('🌐 请求URL:', url);
  console.log('⏰ 请求时间:', new Date().toLocaleTimeString());

  if (!API_BASE) {
    console.error('❌ API_BASE 环境变量未设置');
    throw new Error('API_BASE 环境变量未设置');
  }

  const fullUrl = `${API_BASE}${url}`;
  console.log('🎯 完整URL:', fullUrl);

  const response = await fetch(fullUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  console.log('📊 响应状态:', response.status, response.statusText);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('❌ 请求失败:', errorText);
    throw new Error(`请求失败: ${response.status} - ${errorText}`);
  }

  const result: QueryBuildRepoResponse = await response.json();
  console.log('✅ 请求成功，仓库数量:', result.items?.length || 0);

  if (result.base_resp.StatusCode !== 0) {
    throw new Error(result.base_resp.StatusMessage || '请求失败');
  }

  const urlObj = new URL(fullUrl);
  const search = urlObj.searchParams.get('search')?.toLocaleLowerCase();
  const language = urlObj.searchParams.get('language')?.toLowerCase();

  const items = result.items.filter((item) => {
    if (item.branch !== 'master') {
      return false;
    }
    if (language && item.language.toLowerCase() !== language) {
      return false;
    }
    if (search && item.repo_name.toLowerCase().indexOf(search) === -1) {
      return false;
    }
    return true;
  });

  return {
    repositories: convertItemsToRepositoryList(items),
    total: result.items.length,
    totalPages: 1,
    page: 1,
    pageSize: result.items.length,
  };
};

function convertItemsToRepositoryList(items: QueryBuildRepoResponse['items']): Repository[] {
  return items.map((item) => ({
    name: item.repo_name,
    description: '',
    status: item.status === '1' ? 'indexed' : item.status === '2' ? 'failed' : 'pending',
    indexedAt: item.gmt_create,
    language: item.language || 'unknown',
    size: '0',
    lastActivity: '',
    url: '',
    indexedBranch: item.branch,
  }));
}

export const useRepositoryRequest = () => {
  const {
    setRepositories,
    setRepositoryTotal,
    activeSearchTerm,
    searchInput, // 添加 searchInput
    languageFilter,
    currentPage,
    pageSize,
  } = useRepositoryData();

  // 使用 ref 追踪上次的URL，确保能检测到变化
  const lastUrlRef = useRef('');

  // 构建查询参数 - 修改这里，使用更稳定的搜索词获取方式
  const buildApiUrl = (searchTerm?: string, langFilter?: string, page?: number) => {
    const queryParams = new URLSearchParams();

    // 优先使用传入的参数，其次使用状态中的值
    const finalSearchTerm = searchTerm !== undefined ? searchTerm : activeSearchTerm;
    const finalLanguageFilter = langFilter !== undefined ? langFilter : languageFilter;
    const finalPage = page !== undefined ? page : currentPage;

    console.log('🔧 构建API URL参数:', {
      searchTerm: finalSearchTerm,
      activeSearchTerm,
      searchInput,
      finalLanguageFilter,
      finalPage,
    });

    if (finalSearchTerm?.trim()) {
      queryParams.append('search', finalSearchTerm.trim());
      console.log('✅ 搜索参数已添加:', finalSearchTerm.trim());
    } else {
      console.log('❌ 没有搜索词，跳过搜索参数');
    }

    if (finalLanguageFilter && finalLanguageFilter !== 'all') {
      queryParams.append('language', finalLanguageFilter);
    }
    queryParams.append('page', finalPage.toString());
    queryParams.append('pageSize', pageSize.toString());

    return `/merklet/query-build-repo?${queryParams.toString()}`;
  };

  const apiUrl = buildApiUrl();

  console.log('🔧 最终API URL:', apiUrl);

  // 检测URL变化
  useEffect(() => {
    if (apiUrl !== lastUrlRef.current) {
      console.log('🔄 URL变化检测:', {
        old: lastUrlRef.current,
        new: apiUrl,
        changed: true,
      });
      lastUrlRef.current = apiUrl;
    }
  }, [apiUrl]);

  // 使用 SWR 进行数据管理 - 优化配置确保请求能发送
  const {
    data,
    error,
    isLoading,
    mutate: swrMutate,
    isValidating,
  } = useSWR(apiUrl, fetcher, {
    refreshInterval: 0,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    dedupingInterval: 500, // 减少去重间隔，允许更快的连续请求
    errorRetryCount: 1,
    errorRetryInterval: 2000,
    revalidateIfStale: true, // 如果数据过期则重新验证
    revalidateOnMount: true, // 组件挂载时重新验证
    onSuccess: (data) => {
      console.log('✅ SWR 成功回调执行');
      console.log('📊 收到数据:', {
        repositories: data?.repositories?.length || 0,
        total: data?.total || 0,
      });

      if (Array.isArray(data?.repositories)) {
        // @ts-ignore
        setRepositories(data.repositories);
        setRepositoryTotal(data.total || 0);
      } else {
        setRepositories([]);
        setRepositoryTotal(0);
      }
    },
    onError: (error) => {
      console.error('❌ SWR 错误回调执行:', error.message);
      setRepositories([]);
      setRepositoryTotal(0);
    },
  });

  // 监听 SWR 状态变化
  useEffect(() => {
    console.log('📊 SWR 状态更新:', {
      url: apiUrl,
      isLoading,
      isValidating,
      hasData: !!data,
      hasError: !!error,
      dataCount: data?.repositories?.length || 0,
    });
  }, [apiUrl, isLoading, isValidating, data, error]);

  // 强制触发搜索 - 修改这里，支持传入特定的搜索词
  const triggerSearch = async (forcedSearchTerm?: string, forcedLanguage?: string, forcedPage?: number) => {
    console.log('🔄 强制触发搜索');
    console.log('🎯 传入参数:', { forcedSearchTerm, forcedLanguage, forcedPage });
    console.log('🎯 当前状态:', { activeSearchTerm, searchInput, languageFilter, currentPage });

    // 如果传入了强制搜索词，直接使用该词构建新的URL
    const searchUrl =
      forcedSearchTerm !== undefined ? buildApiUrl(forcedSearchTerm, forcedLanguage, forcedPage) : apiUrl;

    console.log('🎯 最终请求URL:', searchUrl);
    console.log('⏰ 触发时间:', new Date().toLocaleTimeString());

    try {
      // 强制重新验证，绕过缓存
      await swrMutate(
        async () => {
          return await fetcher(searchUrl);
        },
        {
          revalidate: false, // 不要重新验证，因为我们已经在手动获取数据
          rollbackOnError: false,
          populateCache: true,
          optimisticData: undefined,
        },
      );
      console.log('✅ 强制搜索完成');
    } catch (error) {
      console.error('❌ 强制搜索失败:', error);
    }
  };

  // 更新仓库状态
  const updateRepositoryStatus = async (id: string, status: Repository['status']) => {
    try {
      console.log(`🔄 更新仓库 ${id} 状态为: ${status}`);

      const response = await fetch(`${API_BASE}/api/repositories/${id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error('更新失败');
      }

      const result: ApiResponse<unknown> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新失败');
      }

      // 刷新当前页数据
      await swrMutate();

      console.log('✅ 状态更新成功');
    } catch (error) {
      console.error('❌ 更新仓库状态失败:', error);
      throw error;
    }
  };

  // 添加新仓库
  const createRepository = async (data: CreateRepositoryRequest) => {
    try {
      console.log('📋 创建新仓库:', data.name);

      const postData = {
        repoName: data.name,
        repoURL: data.url,
        language: data.language.toLowerCase(),
        branch: data.branch,
        projectID: data.projectID,
      };

      const response = await fetch(`${API_BASE}/merklet/build-index`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`创建失败: ${response.status} - ${errorText}`);
      }

      const result: CreateRepositoryResponse = await response.json();

      if (result.base_resp.StatusCode !== 0) {
        throw new Error(result.base_resp.StatusMessage || '请求失败');
      }

      console.log('✅ 仓库创建成功:', result.userKnowledgeId);

      // 刷新当前页数据
      await swrMutate();

      return result.userKnowledgeId;
    } catch (error) {
      console.error('❌ 创建仓库失败:', error);
      throw error;
    }
  };

  // 返回数据
  const repositories = data?.repositories || [];
  const total = data?.total || 0;
  const totalPages = data?.totalPages || Math.ceil(total / pageSize);

  return {
    repositories,
    total,
    totalPages,
    isLoading,
    error,
    updateRepositoryStatus,
    triggerSearch,
    createRepository,
  };
};
