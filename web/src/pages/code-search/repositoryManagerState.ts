/**
 * 仓库管理状态管理
 * 管理仓库数据、筛选条件、搜索状态、分页等所有相关状态
 */
import { useState, useMemo, useCallback } from 'react';
import { Repository } from './shared/types';

export const useRepositoryData = () => {
  const [repositories, setRepositoriesState] = useState<Repository[]>([]);

  // 分离搜索输入和实际查询条件
  const [searchInput, setSearchInput] = useState('');
  const [activeSearchTerm, setActiveSearchTerm] = useState('');
  const [languageFilter, setLanguageFilterState] = useState<string>('all');
  const [currentPage, setCurrentPageState] = useState(1);
  const [pageSize] = useState(100);
  const [total, setTotal] = useState(0);

  // 添加仓库对话框状态
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // 简化状态更新函数
  const setRepositories = useCallback((repos: Repository[]) => {
    console.log('📦 状态管理: 设置仓库数据', repos.length, '个');
    setRepositoriesState(repos);
  }, []);

  const setRepositoryTotal = useCallback((totalCount: number) => {
    console.log('📊 状态管理: 设置总数', totalCount);
    setTotal(totalCount);
  }, []);

  // 执行搜索的函数
  const executeSearch = useCallback(() => {
    console.log('🔍 状态管理: 执行搜索', searchInput);
    setActiveSearchTerm(searchInput);
    setCurrentPageState(1);
  }, [searchInput]);

  // 清除搜索
  const clearSearch = useCallback(() => {
    console.log('🧹 状态管理: 清除搜索');
    setSearchInput('');
    setActiveSearchTerm('');
    setCurrentPageState(1);
  }, []);

  // 页码更新函数 - 添加日志
  const setCurrentPage = useCallback(
    (page: number) => {
      console.log('📄 状态管理: 设置页码', currentPage, '->', page);
      setCurrentPageState(page);
    },
    [currentPage],
  );

  // 语言筛选更新函数 - 重命名以避免冲突，添加日志
  const setLanguageFilter = useCallback(
    (language: string) => {
      console.log('🏷️ 状态管理: 设置语言筛选', languageFilter, '->', language);
      setLanguageFilterState(language);
    },
    [languageFilter],
  );

  // 🔧 修复对话框控制函数 - 移除依赖数组中的状态依赖
  const openAddDialog = useCallback(() => {
    console.log('📋 状态管理: 准备打开添加仓库对话框');
    setIsAddDialogOpen(true);
    console.log('📋 状态管理: 已设置对话框状态为 true');
  }, []); // 🚨 移除 isAddDialogOpen 依赖

  const closeAddDialog = useCallback(() => {
    console.log('📋 状态管理: 准备关闭添加仓库对话框');
    setIsAddDialogOpen(false);
    console.log('📋 状态管理: 已设置对话框状态为 false');
  }, []); // 🚨 移除 isAddDialogOpen 依赖

  // 移除前端过滤逻辑
  const filteredRepositories = useMemo(() => {
    return repositories;
  }, [repositories]);

  const totalPages = useMemo(() => {
    return Math.ceil(total / pageSize);
  }, [total, pageSize]);

  return {
    repositories,
    setRepositories,
    filteredRepositories,

    // 搜索相关状态
    searchInput,
    setSearchInput,
    activeSearchTerm,
    executeSearch,
    clearSearch,

    languageFilter,
    setLanguageFilter,
    currentPage,
    setCurrentPage,
    pageSize,
    total,
    setRepositoryTotal,
    totalPages,

    // 对话框状态
    isAddDialogOpen,
    openAddDialog,
    closeAddDialog,
  };
};
