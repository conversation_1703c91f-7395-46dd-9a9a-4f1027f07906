/**
 * 统计概览组件
 * 显示仓库总数和各种状态的统计数据
 */
import React from 'react';
import { Repository } from './shared/types';
import { CheckCircleIcon, ClockIcon, XCircleIcon, MinusCircleIcon } from 'lucide-react';

interface StatsOverviewProps {
  repositories: Repository[];
}

const StatsOverview: React.FC<StatsOverviewProps> = ({ repositories }) => {
  const stats = React.useMemo(() => {
    const total = repositories.length;
    const indexed = repositories.filter((r) => r.status === 'indexed').length;
    const pending = repositories.filter((r) => r.status === 'pending').length;
    const failed = repositories.filter((r) => r.status === 'failed').length;
    const notIndexed = repositories.filter((r) => r.status === 'not_indexed').length;

    return { total, indexed, pending, failed, notIndexed };
  }, [repositories]);

  const statCards = [
    {
      title: '总仓库数',
      value: stats.total,
      icon: MinusCircleIcon,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
    },
    {
      title: '已接入',
      value: stats.indexed,
      icon: CheckCircleIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: '处理中',
      value: stats.pending,
      icon: ClockIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: '失败',
      value: stats.failed,
      icon: XCircleIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat) => {
        const IconComponent = stat.icon;
        return (
          <div
            key={stat.title}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className={`${stat.bgColor} p-3 rounded-lg`}>
                <IconComponent className={`w-6 h-6 ${stat.color}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StatsOverview;
