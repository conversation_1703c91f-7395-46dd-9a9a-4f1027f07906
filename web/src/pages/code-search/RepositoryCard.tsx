/**
 * 仓库卡片组件
 * 展示单个仓库的详细信息和操作按钮
 */
import React from 'react';
import { Repository } from './shared/types';
// import { useRepositoryRequest } from './repositoryManagerRequest';
import {
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  MinusCircleIcon,
  // ExternalLinkIcon,
  // PlayIcon,
  // RefreshCwIcon
} from 'lucide-react';

interface RepositoryCardProps {
  repository: Repository;
}

const RepositoryCard: React.FC<RepositoryCardProps> = ({ repository }) => {
  // const [isUpdating, setIsUpdating] = useState(false);
  // const { updateRepositoryStatus } = useRepositoryRequest();

  const getStatusConfig = (status: Repository['status']) => {
    switch (status) {
      case 'indexed':
        return {
          icon: CheckCircleIcon,
          text: '已接入',
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          borderColor: 'border-green-200',
        };
      case 'pending':
        return {
          icon: ClockIcon,
          text: '处理中',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          borderColor: 'border-yellow-200',
        };
      case 'failed':
        return {
          icon: XCircleIcon,
          text: '失败',
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          borderColor: 'border-red-200',
        };
      case 'not_indexed':
        return {
          icon: MinusCircleIcon,
          text: '未接入',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200',
        };
    }
  };

  const statusConfig = getStatusConfig(repository.status);
  const StatusIcon = statusConfig.icon;

  // const handleStatusUpdate = async (newStatus: Repository['status']) => {
  //   if (isUpdating) return;

  //   setIsUpdating(true);
  //   try {
  //     await updateRepositoryStatus(repository.id!, newStatus);
  //   } catch (error) {
  //     console.error('更新状态失败:', error);
  //   } finally {
  //     setIsUpdating(false);
  //   }
  // };

  // const getActionButton = () => {
  //   switch (repository.status) {
  //     case 'indexed':
  //       return (
  //         <button
  //           onClick={() => handleStatusUpdate('pending')}
  //           disabled={isUpdating}
  //           className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
  //         >
  //           <RefreshCwIcon className="w-3 h-3 mr-1" />
  //           重新索引
  //         </button>
  //       );
  //     case 'failed':
  //     case 'not_indexed':
  //       return (
  //         <button
  //           onClick={() => handleStatusUpdate('pending')}
  //           disabled={isUpdating}
  //           className="inline-flex items-center px-3 py-1.5 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
  //         >
  //           <PlayIcon className="w-3 h-3 mr-1" />
  //           开始索引
  //         </button>
  //       );
  //     case 'pending':
  //       return (
  //         <button
  //           disabled
  //           className="inline-flex items-center px-3 py-1.5 border border-gray-200 text-xs font-medium rounded text-gray-400 bg-gray-50 cursor-not-allowed"
  //         >
  //           <ClockIcon className="w-3 h-3 mr-1" />
  //           处理中...
  //         </button>
  //       );
  //   }
  // };

  // 格式化时间显示
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  return (
    <div className="p-6 hover:bg-gray-50 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-3">
            <h4 className="text-lg font-medium text-gray-900 truncate">{repository.name}</h4>

            <div
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig.bgColor} ${statusConfig.color} ${statusConfig.borderColor}`}
            >
              <StatusIcon className="w-3 h-3 mr-1" />
              {statusConfig.text}
            </div>

            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
              {repository.language}
            </span>
          </div>

          <p className="mt-2 text-sm text-gray-600 line-clamp-2">{repository.description}</p>

          <div className="mt-3 flex items-center space-x-6 text-sm text-gray-500">
            {/* <span>大小: {repository.size}</span> */}
            <span>索引分支: {repository.indexedBranch}</span>
            {repository.indexedAt && <span>索引时间: {formatDateTime(repository.indexedAt)}</span>}
          </div>
        </div>

        {/* <div className="flex items-center space-x-3 ml-6">
          {getActionButton()}
          
          <a
            href={repository.url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <ExternalLinkIcon className="w-3 h-3 mr-1" />
            查看源码
          </a>
        </div> */}
      </div>
    </div>
  );
};

export default RepositoryCard;
