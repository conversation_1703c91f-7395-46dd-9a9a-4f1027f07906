/**
 * 页面头部组件
 * 显示页面标题和主要操作按钮
 */
import React from 'react';
import { PlusIcon } from 'lucide-react';
// import { useRepositoryRequest } from './repositoryManagerRequest';

interface HeaderProps {
  onAddRepository: () => void;
}

const Header: React.FC<HeaderProps> = ({ onAddRepository }) => {
  // const { triggerSearch } = useRepositoryRequest();

  // const handleRefresh = async () => {
  //   try {
  //     await triggerSearch();
  //   } catch (error) {
  //     console.error('刷新失败:', error);
  //   }
  // };

  const handleAddRepository = () => {
    console.log('🚀 Header: 点击添加仓库按钮');
    console.log('🔧 Header: 调用父组件传递的 onAddRepository 函数');

    try {
      onAddRepository();
      console.log('✅ Header: onAddRepository 调用成功');
    } catch (error) {
      console.error('❌ Header: onAddRepository 调用失败:', error);
    }
  };

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">代码索引管理</h1>
            <p className="mt-2 text-gray-600">管理和查看仓库代码索引状态</p>
          </div>

          <div className="flex space-x-3">
            {/* <button 
              onClick={handleRefresh}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <RefreshCcwIcon className="w-4 h-4 mr-2" />
              刷新状态
            </button> */}

            <button
              type="button"
              onClick={handleAddRepository}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              添加仓库
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
