/**
 * 筛选栏组件
 * 提供搜索和语言筛选功能，支持扩展更多筛选项
 */
import React from 'react';
import { SearchIcon } from 'lucide-react';
import { useRepositoryData } from './repositoryManagerState';
import { useRepositoryRequest } from './repositoryManagerRequest';

const FilterBar: React.FC = () => {
  const {
    searchInput, // 用户输入的搜索词
    setSearchInput, // 设置用户输入
    activeSearchTerm, // 实际生效的搜索词
    executeSearch, // 执行搜索
    clearSearch, // 清除搜索
    languageFilter,
    setLanguageFilter,
    setCurrentPage,
  } = useRepositoryData();

  const { triggerSearch } = useRepositoryRequest();

  const languageOptions = [
    { value: 'all', label: '全部语言' },
    { value: 'TypeScript', label: 'TypeScript' },
    { value: 'Go', label: 'Go' },
    { value: 'Swift', label: 'Swift' },
    { value: 'Kotlin', label: 'Kotlin' },
  ];

  const handleSearch = () => {
    console.log('🔍 === 搜索按钮点击 ===');
    console.log('🎯 搜索词:', searchInput);
    console.log('⏰ 点击时间:', new Date().toLocaleTimeString());

    // 先执行搜索状态更新
    executeSearch();

    // 立即触发搜索，直接传入当前的searchInput值，避免状态更新延迟问题
    console.log('🚀 立即触发搜索，使用当前输入:', searchInput);
    triggerSearch(searchInput.trim(), languageFilter, 1);
  };

  const handleLanguageChange = (language: string) => {
    console.log('🏷️ === 语言筛选变化 ===');
    console.log('🎯 新语言:', language);
    console.log('⏰ 变化时间:', new Date().toLocaleTimeString());

    setLanguageFilter(language);
    setCurrentPage(1);

    // 语言筛选也直接传入参数触发搜索
    setTimeout(() => {
      console.log('🚀 语言筛选触发搜索');
      triggerSearch(activeSearchTerm, language, 1);
    }, 50);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleClearAll = () => {
    console.log('🧹 === 清除所有筛选条件 ===');
    console.log('⏰ 清除时间:', new Date().toLocaleTimeString());

    // 清除状态
    clearSearch();
    setLanguageFilter('all');
    setCurrentPage(1);

    // 立即触发搜索，使用空的搜索词和全部语言
    setTimeout(() => {
      console.log('🚀 清除筛选后触发搜索');
      triggerSearch('', 'all', 1);
    }, 50);
  };

  return (
    <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 p-6 backdrop-blur-sm">
      {/* 筛选区域 - 优化布局确保语言筛选合适宽度 */}
      <div className="space-y-4">
        {/* 搜索区域 */}
        <div className="space-y-2">
          <label htmlFor="search-repository" className="block text-sm font-medium text-gray-700 mb-2">
            🔍 搜索仓库
          </label>
          <div className="flex space-x-3">
            <div className="relative flex-1 group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none transition-colors group-focus-within:text-blue-600">
                <SearchIcon className="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
              </div>
              <input
                type="text"
                className="block w-full pl-12 pr-4 py-3 text-base border-2 border-gray-200 rounded-xl leading-6 bg-white placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 hover:border-gray-300 hover:shadow-md focus:shadow-lg"
                placeholder="输入仓库名称或描述关键词..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyPress={handleKeyPress}
              />
              {/* 清除按钮 */}
              {searchInput && (
                <button
                  type="button"
                  onClick={() => setSearchInput('')}
                  className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <span className="text-lg">×</span>
                </button>
              )}
            </div>

            {/* 搜索按钮 */}
            <button
              type="button"
              onClick={handleSearch}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold text-base rounded-xl transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-500 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0 shrink-0"
            >
              <SearchIcon className="h-5 w-5" />
              <span>搜索</span>
            </button>
          </div>
        </div>

        {/* 筛选项区域 - 横向排列 */}
        <div className="flex flex-wrap items-end gap-4">
          {/* 语言筛选 - 控制在合适宽度 */}
          <div className="w-48 space-y-2">
            <label htmlFor="repository-language" className="block text-sm font-medium text-gray-700 mb-2">
              🏷️ 编程语言
            </label>
            <div className="relative">
              <select
                className="block w-full px-4 py-3 text-base border-2 border-gray-200 rounded-xl bg-white text-gray-900 font-medium focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200 hover:border-gray-300 hover:shadow-md focus:shadow-lg appearance-none cursor-pointer"
                value={languageFilter}
                onChange={(e) => handleLanguageChange(e.target.value)}
              >
                {languageOptions.map((option) => (
                  <option key={option.value} value={option.value} className="py-2">
                    {option.label}
                  </option>
                ))}
              </select>
              {/* 自定义下拉箭头 */}
              <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                <svg
                  className="h-5 w-5 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* 预留空间用于未来的筛选项 */}
          <div className="w-48 space-y-2">{/* 未来可以在这里添加其他筛选项 */}</div>
        </div>
      </div>

      {/* 当前筛选状态指示 */}
      {(activeSearchTerm || languageFilter !== 'all') && (
        <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-blue-900">🎯 当前筛选条件：</span>
              <div className="flex flex-wrap gap-2">
                {activeSearchTerm && (
                  <span className="px-3 py-1 bg-blue-200 text-blue-800 text-sm rounded-lg font-medium">
                    搜索: "{activeSearchTerm}"
                  </span>
                )}
                {languageFilter !== 'all' && (
                  <span className="px-3 py-1 bg-purple-200 text-purple-800 text-sm rounded-lg font-medium">
                    语言: {languageOptions.find((opt) => opt.value === languageFilter)?.label}
                  </span>
                )}
              </div>
            </div>
            <button
              type="button"
              onClick={handleClearAll}
              className="px-3 py-1.5 text-sm text-blue-700 hover:text-blue-900 hover:bg-blue-100 rounded-lg transition-colors"
            >
              清除筛选
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterBar;
