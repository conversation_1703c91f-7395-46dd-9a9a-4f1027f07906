/**
 * 前后端共享的类型定义
 * 保证前后端数据结构的一致性
 */
export interface Repository {
  _id?: string;
  id?: string;
  name: string;
  description: string;
  status: 'indexed' | 'pending' | 'failed' | 'not_indexed';
  indexedAt?: string;
  language: string;
  size: string;
  lastActivity: string;
  url: string;
  createdAt?: string;
  updatedAt?: string;
  indexedBranch: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface RepositoryListResponse {
  repositories: Repository[];
  total: number;
  page?: number;
  pageSize?: number;
  totalPages?: number;
}

export interface QueryBuildRepoResponse {
  base_resp: {
    StatusCode: number;
    StatusMessage: string;
  };
  items: {
    branch: string;
    gmt_create: string;
    gmt_modify: string;
    language: string;
    repo_name: string;
    status: string;
    uid: string;
  }[];
}

export interface CreateRepositoryRequest {
  name: string;
  description: string;
  url: string;
  language: string;
  branch?: string; // 索引分支
  projectID: string;
}

export interface CreateRepositoryResponse {
  base_resp: {
    StatusCode: number;
    StatusMessage: string;
  };
  userKnowledgeId: string;
}
