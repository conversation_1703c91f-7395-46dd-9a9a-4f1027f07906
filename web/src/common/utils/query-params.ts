export const removeQueryParams = (key: string) => {
  if (typeof window === 'undefined') {
    return;
  }
  const url = new URL(window.location.href);
  url.searchParams.delete(key);

  // 不刷新页面，更新地址栏
  window.history.replaceState({}, document.title, url.toString());
};

export const addQueryParams = (key: string, value: string) => {
  if (typeof window === 'undefined') {
    return;
  }
  const url = new URL(window.location.href);
  url.searchParams.set(key, value);

  // 不刷新页面，更新地址栏
  window.history.replaceState({}, document.title, url.toString());
};

export const getQueryParams = (key: string, defaultValue?: string) => {
  if (typeof window === 'undefined') {
    return defaultValue;
  }
  const url = new URL(window.location.href);
  const value = url.searchParams.get(key);
  if (value === null) {
    return defaultValue;
  }
  return value;
};
